# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Keep example env file but ignore actual env files
!.env.example

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Build directories
dist/
build/

# Temporary folders
tmp/
temp/

# Memory bank and local data
memory-bank/
.roomodes

# Development and testing files
reports/
tests/
tools/
backup/
organize-files.js

# Test files (optional - remove if you want to include tests)
test_system_prompt.html