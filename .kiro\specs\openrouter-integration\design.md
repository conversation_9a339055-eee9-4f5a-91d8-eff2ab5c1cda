# Design Document

## Overview

Інтеграція OpenRouter.ai як додатковий API провайдер в існуючу архітектуру Nebula AI Interface. Система буде підтримувати переключення між поточним LLM API та OpenRouter.ai через налаштування користувача.

## Architecture

### Provider Selection System
- Додати enum для API провайдерів: `CHUTES_AI`, `OPENROUTER`
- Розширити конфігурацію для зберігання активного провайдера
- Створити фабрику для API викликів на основі обраного провайдера

### API Abstraction Layer
- Створити базовий інтерфейс для API провайдерів
- Імплементувати OpenRouter-специфічну логіку стрімінгу
- Зберегти сумісність з існуючою логікою thinking models

## Components and Interfaces

### 1. Provider Configuration
```javascript
const API_PROVIDERS = {
    CHUTES_AI: {
        name: 'Chutes AI',
        endpoint: 'https://llm.chutes.ai/v1/chat/completions',
        requiresAuth: true
    },
    OPENROUTER: {
        name: 'OpenRouter.ai',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        requiresAuth: true,
        models: [
            'moonshotai/kimi-k2:free',
            'tngtech/deepseek-r1t2-chimera:free',
            'deepseek/deepseek-r1-0528:free',
            'qwen/qwen3-235b-a22b:free',
            'deepseek/deepseek-chat-v3-0324:free'
        ]
    }
};
```

### 2. API Factory Pattern
- `createApiCall(provider, requestData, loadingId)` - фабрика для створення API викликів
- `handleOpenRouterStream(response, loadingId, isThinkingModel)` - обробка OpenRouter стрімінгу
- `parseOpenRouterSSE(line)` - парсинг SSE з коментарями OpenRouter

### 3. Settings Extension
- Додати поле вибору провайдера в UI налаштувань
- Розширити `saveSettings()` та `loadSettings()` для провайдера
- Додати валідацію API ключів для різних провайдерів

## Data Models

### Settings Object Extension
```javascript
const settingsSchema = {
    // Existing fields...
    apiProvider: 'CHUTES_AI', // Default
    openrouterApiKey: '', // Will fallback to OPENROUTER_API_KEY env variable
    selectedModel: '', // Provider-specific model
    // ...
};
```

### Environment Variables
- `OPENROUTER_API_KEY` - default API key for OpenRouter.ai (similar to existing DEFAULT_API_KEY)
- Should be added to `.env.example` with placeholder value

### Request Format Mapping
- Chutes AI: існуючий формат
- OpenRouter: стандартний OpenAI-сумісний формат з додатковими полями

## Error Handling

### Provider-Specific Errors
- Обробка різних форматів помилок від провайдерів
- Fallback механізм при недоступності провайдера
- Валідація API ключів для кожного провайдера

### Stream Error Handling
- Обробка OpenRouter SSE коментарів (`: OPENROUTER PROCESSING`)
- Graceful degradation при проблемах зі стрімінгом
- Підтримка stream cancellation для OpenRouter

## Testing Strategy

### Unit Tests
- Тестування парсингу OpenRouter SSE
- Валідація конфігурації провайдерів
- Тестування API factory pattern

### Integration Tests
- Тестування переключення між провайдерами
- Перевірка збереження налаштувань
- Тестування стрімінгу для обох провайдерів

### Manual Testing
- UI тестування вибору провайдера
- Тестування з реальними API ключами
- Перевірка thinking models з OpenRouter