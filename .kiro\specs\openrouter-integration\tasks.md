# Implementation Plan

- [x] 1. Create provider configuration system





  - Add API_PROVIDERS constant with Chutes AI and OpenRouter configurations
  - Create provider validation functions
  - _Requirements: 1.1, 1.2_

- [x] 2. Extend settings system for provider selection








  - Add provider selection dropdown to settings UI
  - Update saveSettings() and loadSettings() functions to handle provider and OpenRouter API key
  - Add provider-specific API key fields in settings modal
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 3. Create API abstraction layer





  - Implement createApiCall() factory function that routes to appropriate provider
  - Create OpenRouter-specific API call function based on existing callAPI structure
  - Ensure both providers use same interface for consistency
  - _Requirements: 2.1, 2.2_

- [x] 4. Implement OpenRouter streaming handler





  - Create handleOpenRouterStream() function to process OpenRouter SSE format
  - Add parsing for OpenRouter comments (`: OPENROUTER PROCESSING`)
  - Integrate with existing thinking model logic for DeepSeek-R1 support
  - _Requirements: 3.1, 3.2_

- [x] 5. Add model selection for OpenRouter




  - Create provider-specific model lists in configuration
  - Update model dropdown to show models based on selected provider
  - Implement model validation and fallback logic
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 6. Update connection testing





  - Modify testConnection() function to work with selected provider
  - Add provider-specific endpoint and authentication testing
  - Update UI feedback for different providers
  - _Requirements: 1.3, 2.1_

- [x] 7. Add stream cancellation support





  - Implement AbortController for OpenRouter requests
  - Update existing cancellation logic to work with both providers
  - Add proper cleanup for cancelled streams
  - _Requirements: 3.3_

- [x] 8. Update error handling





  - Add provider-specific error message parsing
  - Update error display to show provider context
  - Implement graceful fallback when provider is unavailable
  - _Requirements: 2.2, 3.2_