# Project Structure

## Root Level
- `index.html` - Main application entry point with embedded Tailwind config
- `package.json` - Node.js dependencies and scripts

- `.env` / `.env.example` - Environment configuration
- `README.md` - Project documentation
- `LICENSE` - ISC license

## Frontend Assets
```
css/
└── main.css              # Custom styles and dark theme overrides

js/
├── main.js               # Main application orchestrator and message sending
├── config.js             # Configuration constants and model settings
├── api.js                # LLM API communication and streaming
├── messageHandler.js     # Chat message creation and management
├── rendering.js          # Content rendering (markdown, code, math)
├── settings.js           # Settings persistence and management
├── uiElements.js         # DOM element references
├── uiInteractions.js     # UI event handlers and interactions
├── utils.js              # Utility functions and helpers
├── thinkingUI.js         # Thinking animation for reasoning models
├── systemPromptManager.js # System prompt management
└── systemPrompt_nsfwbase.js # Built-in system prompts
```

## Memory Bank (Documentation)
```
memory-bank/
├── productContext.md     # High-level project overview
├── systemPatterns.md     # Coding and architectural patterns
├── activeContext.md      # Current development context
├── decisionLog.md        # Development decisions log
└── progress.md           # Development progress tracking
```

## Architecture Patterns

### Modular JavaScript Organization
- **Separation of Concerns**: Each JS file handles a specific domain
- **Global State**: Minimal global variables, mostly UI element references
- **Function-based**: No classes, pure functions with clear dependencies
- **Event-driven**: UI interactions trigger function calls across modules

### UI State Management
- **Local Storage**: Settings and API tokens persisted locally
- **DOM-centric**: UI state reflected directly in DOM elements
- **Modal System**: Overlay-based settings and prompt management

### API Integration
- **Direct API Calls**: Direct communication with LLM APIs
- **Streaming Support**: Real-time response handling for LLM interactions
- **Error Handling**: Graceful degradation with user feedback

### Styling Approach
- **Utility-first**: Tailwind CSS with extensive utility classes
- **Dark Theme**: Custom color palette with `sora-gray` variants
- **Responsive**: Mobile-first design with responsive breakpoints