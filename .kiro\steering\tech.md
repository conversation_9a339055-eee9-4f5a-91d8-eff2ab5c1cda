# Technology Stack

## Frontend
- **Core**: <PERSON><PERSON><PERSON><PERSON>, CSS3, Vanilla JavaScript (ES6+)
- **Styling**: Tailwind CSS with custom dark theme configuration
- **Icons**: Font Awesome
- **Content Rendering**:
  - Markdown: Marked.js
  - Math: KaTeX
  - Code Highlighting: Prism.js with multiple language support
- **Typography**: Tailwind Typography plugin

## Backend
- **Static Serving**: Frontend served via live-server

## External APIs
- **LLM API**: https://llm.chutes.ai/v1/chat/completions

## Development Tools
- **Package Manager**: npm
- **Development Server**: live-server

## Common Commands

### Development
```bash
npm install          # Install dependencies
npm run dev         # Start development server
npm start           # Start development server
```

## Environment Configuration
- `.env` file for API keys and configuration
- `DEFAULT_API_KEY` for fallback API access
- `OPENROUTER_API_KEY` for OpenRouter API access