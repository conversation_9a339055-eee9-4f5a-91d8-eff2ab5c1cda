{"extends": ["stylelint-config-standard"], "rules": {"custom-property-pattern": null, "selector-class-pattern": null, "selector-id-pattern": null, "keyframes-name-pattern": null, "property-no-vendor-prefix": null, "value-no-vendor-prefix": null, "at-rule-no-vendor-prefix": null, "selector-no-vendor-prefix": null, "selector-max-specificity": null, "no-duplicate-selectors": null, "declaration-block-no-duplicate-properties": [true, {"ignore": ["consecutive-duplicates-with-different-values"]}], "color-function-notation": "legacy", "alpha-value-notation": "number", "import-notation": "string", "custom-property-empty-line-before": null, "declaration-empty-line-before": null, "rule-empty-line-before": null, "comment-empty-line-before": null, "max-nesting-depth": 4, "selector-max-compound-selectors": 6, "declaration-property-value-no-unknown": true, "font-family-no-missing-generic-family-keyword": null, "no-descending-specificity": null, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global"]}], "media-feature-range-notation": null, "shorthand-property-no-redundant-values": null}, "ignoreFiles": ["node_modules/**/*", "css-backup/**/*", "css/main.compiled.css", "css/critical.css"]}