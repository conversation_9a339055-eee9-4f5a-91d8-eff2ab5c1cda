# CSS Architecture

This directory contains the modular CSS architecture for the Nebula AI Interface. The CSS has been refactored from a monolithic 5,858-line file into 32 organized, maintainable files.

## Performance Improvements

- **Bundle Size**: Reduced from 141.61 KB to 137.55 KB (-2.87%)
- **Lines of Code**: Reduced from 5,858 to 3,382 lines (-42.27%)
- **Modularization**: Split into 32 focused files
- **Critical CSS**: 13.63 KB extracted for faster initial load

## Structure

- **base/**: Foundation styles (reset, typography, root variables)
- **tokens/**: Design tokens (colors, spacing, shadows, animations)
- **utilities/**: Utility classes for common styling patterns
- **components/**: Component-specific styles (buttons, modals, messages, etc.)
- **animations/**: Animation keyframes and transitions
- **layout/**: Layout system (grid, containers, responsive)
- **accessibility/**: Accessibility-focused styles (focus, contrast, motion)

## Key Features

### Design Token System
- Consistent color palette with semantic mappings
- Standardized spacing scale and animation timing
- CSS custom properties for easy theming

### Component Architecture
- Modular button system with variants and states
- Unified status indicators with animations
- Enhanced modal system with proper z-index management

### Accessibility Support
- Focus management with enhanced indicators
- High contrast mode support
- Reduced motion preferences
- ARIA-compliant components

## Build System

The CSS uses PostCSS with postcss-import to handle modular imports:

```bash
# Build CSS once
npm run build:css

# Watch for changes and rebuild
npm run watch:css
```

## Files

- `main.css`: Entry point with organized imports
- `main.compiled.css`: Compiled/minified output (referenced by HTML)
- `critical.css`: Critical CSS for above-the-fold content
- `ARCHITECTURE.md`: Detailed architecture documentation
- `main.css.backup`: Backup of original monolithic CSS file

## Validation Tools

- `visual-consistency-validator.js`: Compares before/after visual output
- `css-performance-validator.js`: Measures bundle size and performance
- `css-usage-analyzer.js`: Identifies unused CSS
- `validate-design-tokens.js`: Ensures token consistency

For detailed documentation, see [ARCHITECTURE.md](./ARCHITECTURE.md).