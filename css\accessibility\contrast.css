/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    /* High contrast color overrides */
    --color-blue-primary: #06f;
    --color-blue-secondary: #0052cc;
    --color-purple-primary: #60c;
    --color-purple-secondary: #5200a3;
    --color-green-primary: #0c6;
    --color-green-secondary: #00a352;
    --color-orange-primary: #f60;
    --color-orange-secondary: #cc5200;
    --color-error: #c00;
  }

  .status-indicator,
  .settings-section-header,
  .code-block-enhanced,
  .message-user,
  .message-assistant,
  .message-system {
    border-width: 2px;
  }

  .status-indicator,
  .settings-section-header h3,
  .code-language-badge {
    font-weight: 700;
  }

  button:focus,
  input:focus,
  select:focus,
  textarea:focus,
  [tabindex]:focus {
    outline: 3px solid currentcolor;
    outline-offset: 2px;
  }

  .focus-enhanced:focus {
    outline-width: 3px;
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(56, 189, 248, 0.4);
  }

  .focus-enhanced.focus-purple:focus {
    box-shadow: 0 0 0 6px rgba(139, 92, 246, 0.4);
  }

  .focus-enhanced.focus-green:focus {
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.4);
  }

  .focus-enhanced.focus-orange:focus {
    box-shadow: 0 0 0 6px rgba(245, 158, 11, 0.4);
  }

  button,
  .button,
  input[type="button"],
  input[type="submit"] {
    border: 2px solid currentcolor;
    font-weight: 700;
  }

  button:hover,
  .button:hover,
  input[type="button"]:hover,
  input[type="submit"]:hover {
    background-color: currentcolor;
    color: white;
  }

  button:focus,
  .button:focus,
  input[type="button"]:focus,
  input[type="submit"]:focus {
    outline: 3px solid currentcolor;
    outline-offset: 3px;
  }

  .text-blue-primary,
  .text-purple-primary,
  .text-green-primary,
  .text-orange-primary {
    filter: contrast(1.2);
  }

  .bg-blue-tint,
  .bg-purple-tint,
  .bg-green-tint,
  .bg-orange-tint {
    opacity: 0.2;
    border: 1px solid currentcolor;
  }
}
