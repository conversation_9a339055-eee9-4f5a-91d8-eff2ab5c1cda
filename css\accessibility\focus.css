/* Focus Management Styles */
.focus-blue:focus {
  outline: 2px solid var(--color-focus-blue);
  outline-offset: 2px;
}

.focus-purple:focus {
  outline: 2px solid var(--color-focus-purple);
  outline-offset: 2px;
}

.focus-green:focus {
  outline: 2px solid var(--color-focus-green);
  outline-offset: 2px;
}

.focus-orange:focus {
  outline: 2px solid var(--color-focus-orange);
  outline-offset: 2px;
}

.focus-enhanced:focus {
  outline: 2px solid var(--color-blue-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(56, 189, 248, 0.2);
  border-radius: 0.25rem;
}

.focus-enhanced.focus-purple:focus {
  outline-color: var(--color-purple-primary);
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
}

.focus-enhanced.focus-green:focus {
  outline-color: var(--color-green-primary);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
}

.focus-enhanced.focus-orange:focus {
  outline-color: var(--color-orange-primary);
  box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.2);
}

.keyboard-focusable {
  position: relative;
}

.keyboard-focusable:focus::after {
  content: "";
  position: absolute;
  inset: -2px;
  border: 2px solid var(--color-blue-primary);
  border-radius: 0.375rem;
  pointer-events: none;
}
