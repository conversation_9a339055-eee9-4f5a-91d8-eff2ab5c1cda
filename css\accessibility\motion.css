/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .code-block-enhanced:hover,
  .settings-section-header:hover,
  .code-copy-btn-enhanced:hover,
  .code-language-badge:hover {
    transform: none;
  }

  .status-indicator i,
  .status-badge,
  .status-dot {
    animation: none;
  }

  .status-indicator.status-connected {
    border-left: 4px solid var(--color-connected);
  }

  .status-indicator.status-error,
  .status-indicator.status-disconnected {
    border-left: 4px solid var(--color-error);
  }

  .status-indicator.status-warning {
    border-left: 4px solid var(--color-warning);
  }

  .status-indicator.status-loading,
  .status-indicator.status-connecting {
    border-left: 4px solid var(--color-loading);
  }

  .btn-enhanced,
  .btn-blue-enhanced,
  .btn-purple-enhanced,
  .btn-green-enhanced,
  .btn-orange-enhanced,
  .interactive-hover,
  .card-enhanced,
  .link-enhanced,
  .toggle-enhanced,
  .input-enhanced {
    transition: none;
    animation: none;
    transform: none;
  }

  .btn-enhanced::before {
    display: none;
  }

  .loading-pulse-blue::after,
  .loading-pulse-purple::after,
  .loading-pulse-green::after,
  .loading-pulse-orange::after {
    display: none;
  }

  .pulse-blue,
  .pulse-purple,
  .pulse-green,
  .pulse-orange {
    animation: none;
  }
}
