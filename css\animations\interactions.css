/* Interaction Animation Classes */
.interactive-element {
  transition: all var(--animation-duration-normal) var(--animation-ease-in-out);
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
  transition-duration: var(--animation-duration-fast);
}

.focus-ring-animated {
  position: relative;
  transition: all var(--animation-duration-fast) var(--animation-ease-out);
}

.focus-ring-animated:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-blue-accent);
  animation: focusRingPulse 1s ease-in-out;
}

.focus-ring-blue:focus {
  box-shadow: 0 0 0 2px var(--color-blue-accent);
  animation: focusRingPulseBlue 1s ease-in-out;
}

.focus-ring-purple:focus {
  box-shadow: 0 0 0 2px var(--color-purple-primary);
  animation: focusRingPulsePurple 1s ease-in-out;
}

.focus-ring-green:focus {
  box-shadow: 0 0 0 2px var(--color-green-primary);
  animation: focusRingPulseGreen 1s ease-in-out;
}

.hover-glow {
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(56, 189, 248, 0.3);
  transform: translateY(-2px);
}

.hover-glow-purple:hover {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
}

.hover-glow-green:hover {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
}

.hover-glow-orange:hover {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  transform: translateY(-2px);
}
