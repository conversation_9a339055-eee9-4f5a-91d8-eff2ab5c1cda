/* Animation Keyframes */
@keyframes pulse-green {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes shake-red {
  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-2px);
  }

  75% {
    transform: translateX(2px);
  }
}

@keyframes blink-orange {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes spin-blue {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-green-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes pulse-red-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

@keyframes pulse-orange-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.15);
    opacity: 0.75;
  }
}

@keyframes pulse-blue-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.3);
    opacity: 0.6;
  }
}

@keyframes glow-green {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.4);
  }
}

@keyframes glow-red {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.4);
  }
}

@keyframes glow-orange {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.4);
  }
}

@keyframes glow-blue {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(56, 189, 248, 0.4);
  }
}

@keyframes copy-success {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes typing {
  0% {
    content: ".";
  }

  33% {
    content: "..";
  }

  66% {
    content: "...";
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes promptSourceFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-blue {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(56, 189, 248, 0.7);
    background-color: var(--color-blue-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(56, 189, 248, 0);
    background-color: var(--color-blue-light);
  }
}

@keyframes pulse-purple {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
    background-color: var(--color-purple-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
    background-color: var(--color-purple-light);
  }
}

@keyframes pulse-green {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    background-color: var(--color-green-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    background-color: var(--color-green-light);
  }
}

@keyframes pulse-orange {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    background-color: var(--color-orange-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
    background-color: var(--color-orange-light);
  }
}

@keyframes loading-sweep {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

@keyframes gradientPulse {
  0%,
  100% {
    background: linear-gradient(
      90deg,
      var(--color-blue-primary) 0%,
      var(--color-purple-primary) 50%,
      var(--color-green-primary) 100%
    );
    background-size: 200% 100%;
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes rotateGradient {
  0% {
    background: conic-gradient(
      from 0deg,
      var(--color-blue-primary),
      var(--color-purple-primary),
      var(--color-green-primary),
      var(--color-orange-primary),
      var(--color-blue-primary)
    );
  }

  100% {
    background: conic-gradient(
      from 360deg,
      var(--color-blue-primary),
      var(--color-purple-primary),
      var(--color-green-primary),
      var(--color-orange-primary),
      var(--color-blue-primary)
    );
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes typingDot {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }

  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes progressGradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes focusRingPulse {
  0% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }
}

@keyframes focusRingPulseBlue {
  0% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(56, 189, 248, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }
}

@keyframes focusRingPulsePurple {
  0% {
    box-shadow: 0 0 0 2px var(--color-purple-primary);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-purple-primary);
  }
}

@keyframes focusRingPulseGreen {
  0% {
    box-shadow: 0 0 0 2px var(--color-green-primary);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-green-primary);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes glowPulse {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(56, 189, 248, 0.5);
  }

  50% {
    box-shadow: 0 0 25px rgba(56, 189, 248, 0.8);
  }
}

@keyframes glowPulsePurple {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
  }

  50% {
    box-shadow: 0 0 25px rgba(139, 92, 246, 0.8);
  }
}

@keyframes glowPulseGreen {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
  }

  50% {
    box-shadow: 0 0 25px rgba(16, 185, 129, 0.8);
  }
}

@keyframes themeSwitch {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(0.98);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes colorMorph {
  0% {
    filter: hue-rotate(0deg) brightness(1);
  }

  50% {
    filter: hue-rotate(180deg) brightness(1.1);
  }

  100% {
    filter: hue-rotate(360deg) brightness(1);
  }
}

@keyframes progress-shine {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}
