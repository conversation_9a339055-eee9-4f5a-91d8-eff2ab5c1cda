/* Transition Classes */
.color-transition {
  transition:
    color var(--animation-duration-normal) var(--animation-ease-in-out),
    background-color var(--animation-duration-normal) var(--animation-ease-in-out),
    border-color var(--animation-duration-normal) var(--animation-ease-in-out),
    box-shadow var(--animation-duration-normal) var(--animation-ease-in-out);
}

.color-transition-fast {
  transition:
    color var(--animation-duration-fast) var(--animation-ease-out),
    background-color var(--animation-duration-fast) var(--animation-ease-out),
    border-color var(--animation-duration-fast) var(--animation-ease-out),
    box-shadow var(--animation-duration-fast) var(--animation-ease-out);
}

.color-transition-slow {
  transition:
    color var(--animation-duration-slow) var(--animation-ease-in-out),
    background-color var(--animation-duration-slow) var(--animation-ease-in-out),
    border-color var(--animation-duration-slow) var(--animation-ease-in-out),
    box-shadow var(--animation-duration-slow) var(--animation-ease-in-out);
}

.theme-transition {
  transition:
    background-color var(--animation-duration-slow) var(--animation-ease-in-out),
    color var(--animation-duration-slow) var(--animation-ease-in-out),
    border-color var(--animation-duration-slow) var(--animation-ease-in-out),
    box-shadow var(--animation-duration-slow) var(--animation-ease-in-out);
}
