/* CSS Reset and Normalization */
html {
  /* Set base font size to 80% of browser default */
  font-size: 90%;
}

body {
  /* Default text for light mode (Tailwind handles dark:text-white) */

  /* Default background for light mode (Tailwind handles dark:bg-black) */
  overflow-x: hidden;
}

/* Apply base dark mode styles here for initial load */
html.dark body {
  background-color: #000; /* Sora black */
  color: #fff; /* Sora white */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
