/* Button Component Styles */
.settings-button {
  transition: all 0.2s ease-in-out;
}

/* Theme buttons - smaller size */
#settingsDarkBtn,
#settingsLightBtn {
  padding: 0.5rem 1rem !important; /* Smaller padding: py-2 px-4 -> py-2 px-4 but smaller */
  font-size: 0.875rem !important; /* Smaller font size */
  min-height: 2.5rem; /* Consistent height */
}

/* General button size improvements */
.settings-button.ui {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.settings-button.api {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Save Settings button - smaller size */
button:has(i.fa-save),
button[class*="Save"],
#saveSettingsBtn {
  padding: 0.75rem 1.5rem !important;
  font-size: 0.875rem !important;
}

/* Expand button - smaller size */
#expandSystemPromptBtn {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
}

/* Copy button - smaller size */
#copyFilePromptBtn {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
}

/* Prompt Library button - smaller size */
#promptLibraryBtn {
  padding: 0.5rem 1rem !important;
  font-size: 0.75rem !important;
}

.settings-button.api {
  border-color: var(--color-api-settings);
  color: var(--color-api-settings);
}

.settings-button.api:hover {
  background-color: rgba(56, 189, 248, 0.1);
  border-color: var(--color-blue-light);
  color: var(--color-blue-light);
}

.settings-button.ui {
  border-color: var(--color-ui-settings);
  color: var(--color-ui-settings);
}

.settings-button.ui:hover {
  background-color: rgba(139, 92, 246, 0.1);
  border-color: var(--color-purple-light);
  color: var(--color-purple-light);
}

.settings-button.prompt {
  border-color: var(--color-prompt-settings);
  color: var(--color-prompt-settings);
}

.settings-button.prompt:hover {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: var(--color-green-light);
  color: var(--color-green-light);
}

.settings-button.advanced {
  border-color: var(--color-advanced-settings);
  color: var(--color-advanced-settings);
}

.settings-button.advanced:hover {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: var(--color-orange-light);
  color: var(--color-orange-light);
}

.chat-send-button {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

#sendBtn {
  position: absolute !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10;
}

#clearInputBtn {
  position: absolute !important;
  right: 4rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10;
}

#sendBtn,
#clearInputBtn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.code-copy-btn {
  opacity: 0;
  transition: all 0.2s ease-in-out;
  z-index: 20;
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(51, 51, 51, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

pre:hover .code-copy-btn {
  opacity: 0.8;
}

.code-copy-btn:hover {
  opacity: 1 !important;
  transform: scale(1.05);
  background-color: rgba(51, 51, 51, 0.9);
}

.code-copy-btn:active {
  transform: scale(0.95);
}

.code-copy-btn-enhanced {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(56, 189, 248, 0.3);
  background-color: rgba(56, 189, 248, 0.1);
  color: var(--color-blue-primary);
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease-in-out;
  z-index: 10;
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.code-copy-btn-enhanced i {
  font-size: 0.625rem;
}

.code-block-enhanced:hover .code-copy-btn-enhanced {
  opacity: 0.8;
}

.code-copy-btn-enhanced:hover {
  opacity: 1 !important;
  background-color: rgba(56, 189, 248, 0.2);
  border-color: rgba(56, 189, 248, 0.5);
  color: var(--color-blue-light);
  transform: scale(1.05);
}

.code-copy-btn-enhanced:active {
  transform: scale(0.95);
  background-color: rgba(56, 189, 248, 0.3);
}

.code-copy-btn-enhanced.copied {
  background-color: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
  color: var(--color-success);
}

.code-copy-btn-enhanced.copied:hover {
  background-color: rgba(16, 185, 129, 0.3);
  border-color: rgba(16, 185, 129, 0.6);
  color: var(--color-green-light);
}

.code-copy-btn-enhanced.error {
  background-color: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  color: var(--color-error);
}

.code-copy-btn-enhanced.error:hover {
  background-color: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.6);
  color: #f87171;
}

.btn-enhanced {
  position: relative;
  transition: all 0.2s ease-in-out;
  transform-origin: center;
  overflow: hidden;
}

.btn-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease-in-out;
  z-index: 1;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-blue-enhanced {
  background: linear-gradient(135deg, var(--color-blue-primary), var(--color-blue-secondary));
  color: white;
  border: 1px solid var(--color-blue-primary);
  transition: all 0.2s ease-in-out;
}

.btn-blue-enhanced:hover {
  background: linear-gradient(135deg, var(--color-blue-light), var(--color-blue-primary));
  border-color: var(--color-blue-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(56, 189, 248, 0.3);
}

.btn-blue-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(56, 189, 248, 0.4);
}

.btn-purple-enhanced {
  background: linear-gradient(135deg, var(--color-purple-primary), var(--color-purple-secondary));
  color: white;
  border: 1px solid var(--color-purple-primary);
  transition: all 0.2s ease-in-out;
}

.btn-purple-enhanced:hover {
  background: linear-gradient(135deg, var(--color-purple-light), var(--color-purple-primary));
  border-color: var(--color-purple-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.btn-purple-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.4);
}

.btn-green-enhanced {
  background: linear-gradient(135deg, var(--color-green-primary), var(--color-green-secondary));
  color: white;
  border: 1px solid var(--color-green-primary);
  transition: all 0.2s ease-in-out;
}

.btn-green-enhanced:hover {
  background: linear-gradient(135deg, var(--color-green-light), var(--color-green-primary));
  border-color: var(--color-green-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-green-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.4);
}

.btn-orange-enhanced {
  background: linear-gradient(135deg, var(--color-orange-primary), var(--color-orange-secondary));
  color: white;
  border: 1px solid var(--color-orange-primary);
  transition: all 0.2s ease-in-out;
}

.btn-orange-enhanced:hover {
  background: linear-gradient(135deg, var(--color-orange-light), var(--color-orange-primary));
  border-color: var(--color-orange-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-orange-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(245, 158, 11, 0.4);
}

.btn-bounce {
  transition:
    transform var(--animation-duration-normal) var(--animation-ease-bounce),
    color var(--animation-duration-fast) var(--animation-ease-out),
    background-color var(--animation-duration-fast) var(--animation-ease-out),
    border-color var(--animation-duration-fast) var(--animation-ease-out),
    box-shadow var(--animation-duration-fast) var(--animation-ease-out);
}

.btn-bounce:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-bounce:active {
  transform: scale(0.98);
  transition-duration: var(--animation-duration-fast);
}

.btn-spring {
  transition:
    transform var(--animation-duration-normal) var(--animation-ease-spring),
    color var(--animation-duration-fast) var(--animation-ease-out),
    background-color var(--animation-duration-fast) var(--animation-ease-out),
    border-color var(--animation-duration-fast) var(--animation-ease-out);
}

.btn-spring:hover {
  transform: scale(1.08);
}

.btn-spring:active {
  transform: scale(0.95);
  transition-duration: var(--animation-duration-fast);
}

.btn-scale {
  transition:
    transform var(--animation-duration-normal) var(--animation-ease-out),
    color var(--animation-duration-fast) var(--animation-ease-out),
    background-color var(--animation-duration-fast) var(--animation-ease-out),
    border-color var(--animation-duration-fast) var(--animation-ease-out);
}

.btn-scale:hover {
  transform: scale(1.02);
}

.btn-scale:active {
  transform: scale(0.98);
}

.btn-blue-animated {
  color: var(--color-blue-primary);
  border-color: var(--color-blue-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}

.btn-blue-animated:hover {
  color: var(--color-blue-light);
  border-color: var(--color-blue-light);
  background-color: rgba(56, 189, 248, 0.1);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
  transform: translateY(-1px);
}

.btn-purple-animated {
  color: var(--color-purple-primary);
  border-color: var(--color-purple-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}

.btn-purple-animated:hover {
  color: var(--color-purple-light);
  border-color: var(--color-purple-light);
  background-color: rgba(139, 92, 246, 0.1);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

.btn-green-animated {
  color: var(--color-green-primary);
  border-color: var(--color-green-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}

.btn-green-animated:hover {
  color: var(--color-green-light);
  border-color: var(--color-green-light);
  background-color: rgba(16, 185, 129, 0.1);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  transform: translateY(-1px);
}

.btn-orange-animated {
  color: var(--color-orange-primary);
  border-color: var(--color-orange-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}

.btn-orange-animated:hover {
  color: var(--color-orange-light);
  border-color: var(--color-orange-light);
  background-color: rgba(245, 158, 11, 0.1);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  transform: translateY(-1px);
}
