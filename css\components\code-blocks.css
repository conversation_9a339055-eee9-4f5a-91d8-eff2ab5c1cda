/* Code Block Component Styles */
pre[class*="language-"] {
  margin: 1em 0;
  border-radius: 0.5rem;
  max-height: 500px;
  overflow: auto;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}

html:not(.dark) pre[class*="language-"] {
  background: #2d2d2d;
  color: #ccc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

html.dark pre[class*="language-"] {
  background: #1a1a1a;
  border: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

code[class*="language-"],
pre[class*="language-"] {
  color: #f8f8f2;
  background: none;
  text-shadow: 0 1px rgba(0, 0, 0, 0.3);
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.3;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

pre[class*="language-"] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
  white-space: pre;
  line-height: 1.3;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #272822;
}

:not(pre) > code[class*="language-"] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #8292a2;
}

.token.punctuation {
  color: #f8f8f2;
}

.token.namespace {
  opacity: 0.7;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
  color: #f92672;
}

.token.boolean,
.token.number {
  color: #ae81ff;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #a6e22e;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
  color: #f8f8f2;
}

.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
  color: #e6db74;
}

.token.keyword {
  color: #66d9ef;
}

.token.regex,
.token.important {
  color: #fd971f;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

.line-numbers .line-numbers-rows {
  border-right: 1px solid #999;
  padding-right: 0.8em;
}

:not(pre) > code {
  background: rgba(135, 131, 120, 0.15);
  color: #eb5757;
  padding: 0.1em 0.4em;
  border-radius: 0.3em;
  white-space: normal;
  font-size: 0.9em;
}

html.dark :not(pre) > code {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
}

.code-title {
  background: #444;
  color: white;
  font-family: monospace;
  padding: 0.5em 1em;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  font-size: 0.8em;
  border-bottom: 1px solid #555;
}

html.dark .code-title {
  background: #333;
  border-bottom: 1px solid #444;
}

pre[class*="language-"] {
  position: relative;
}

pre[class*="language-"]::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

pre[class*="language-"]::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

pre[class*="language-"]::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

pre[class*="language-"]::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.code-block-enhanced {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease-in-out;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.5) 100%);
}

html:not(.dark) .code-block-enhanced {
  border-color: rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.code-block-enhanced:hover {
  border-color: rgba(56, 189, 248, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

html:not(.dark) .code-block-enhanced:hover {
  border-color: rgba(56, 189, 248, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.code-language-badge {
  position: absolute;
  top: 0.75rem;
  right: 3.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  z-index: 10;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
  backdrop-filter: blur(8px);
}

.code-language-badge.lang-javascript {
  background-color: rgba(247, 223, 30, 0.15);
  color: var(--color-lang-javascript);
  border-color: rgba(247, 223, 30, 0.3);
}

.code-language-badge.lang-python {
  background-color: rgba(55, 118, 171, 0.15);
  color: var(--color-lang-python);
  border-color: rgba(55, 118, 171, 0.3);
}

.code-language-badge.lang-html {
  background-color: rgba(227, 79, 38, 0.15);
  color: var(--color-lang-html);
  border-color: rgba(227, 79, 38, 0.3);
}

.code-language-badge.lang-css {
  background-color: rgba(21, 114, 182, 0.15);
  color: var(--color-lang-css);
  border-color: rgba(21, 114, 182, 0.3);
}

.code-language-badge.lang-java {
  background-color: rgba(237, 139, 0, 0.15);
  color: var(--color-lang-java);
  border-color: rgba(237, 139, 0, 0.3);
}

.code-language-badge.lang-cpp,
.code-language-badge.lang-c {
  background-color: rgba(0, 89, 156, 0.15);
  color: var(--color-lang-cpp);
  border-color: rgba(0, 89, 156, 0.3);
}

.code-language-badge.lang-csharp {
  background-color: rgba(35, 145, 32, 0.15);
  color: var(--color-lang-csharp);
  border-color: rgba(35, 145, 32, 0.3);
}

.code-language-badge.lang-php {
  background-color: rgba(119, 123, 180, 0.15);
  color: var(--color-lang-php);
  border-color: rgba(119, 123, 180, 0.3);
}

.code-language-badge.lang-ruby {
  background-color: rgba(204, 52, 45, 0.15);
  color: var(--color-lang-ruby);
  border-color: rgba(204, 52, 45, 0.3);
}

.code-language-badge.lang-go {
  background-color: rgba(0, 173, 216, 0.15);
  color: var(--color-lang-go);
  border-color: rgba(0, 173, 216, 0.3);
}

.code-language-badge.lang-rust {
  background-color: rgba(0, 0, 0, 0.15);
  color: #ce422b;
  border-color: rgba(206, 66, 43, 0.3);
}

.code-language-badge.lang-typescript {
  background-color: rgba(49, 120, 198, 0.15);
  color: var(--color-lang-typescript);
  border-color: rgba(49, 120, 198, 0.3);
}

.code-language-badge.lang-json {
  background-color: rgba(56, 189, 248, 0.15);
  color: var(--color-blue-primary);
  border-color: rgba(56, 189, 248, 0.3);
}

.code-language-badge.lang-xml {
  background-color: rgba(255, 102, 0, 0.15);
  color: var(--color-lang-xml);
  border-color: rgba(255, 102, 0, 0.3);
}

.code-language-badge.lang-sql {
  background-color: rgba(51, 103, 145, 0.15);
  color: var(--color-lang-sql);
  border-color: rgba(51, 103, 145, 0.3);
}

.code-language-badge.lang-bash,
.code-language-badge.lang-shell {
  background-color: rgba(78, 170, 37, 0.15);
  color: var(--color-lang-bash);
  border-color: rgba(78, 170, 37, 0.3);
}

.code-language-badge.lang-powershell {
  background-color: rgba(1, 36, 86, 0.15);
  color: #5391fe;
  border-color: rgba(83, 145, 254, 0.3);
}

.code-language-badge.lang-default {
  background-color: rgba(56, 189, 248, 0.15);
  color: var(--color-lang-default);
  border-color: rgba(56, 189, 248, 0.3);
}

.code-language-badge:hover {
  transform: scale(1.05);
  opacity: 0.9;
}

.code-block-enhanced.lang-javascript {
  border-left: 4px solid var(--color-lang-javascript);
}

.code-block-enhanced.lang-python {
  border-left: 4px solid var(--color-lang-python);
}

.code-block-enhanced.lang-html {
  border-left: 4px solid var(--color-lang-html);
}

.code-block-enhanced.lang-css {
  border-left: 4px solid var(--color-lang-css);
}

.code-block-enhanced.lang-java {
  border-left: 4px solid var(--color-lang-java);
}

.code-block-enhanced.lang-cpp,
.code-block-enhanced.lang-c {
  border-left: 4px solid var(--color-lang-cpp);
}

.code-block-enhanced.lang-csharp {
  border-left: 4px solid var(--color-lang-csharp);
}

.code-block-enhanced.lang-php {
  border-left: 4px solid var(--color-lang-php);
}

.code-block-enhanced.lang-ruby {
  border-left: 4px solid var(--color-lang-ruby);
}

.code-block-enhanced.lang-go {
  border-left: 4px solid var(--color-lang-go);
}

.code-block-enhanced.lang-rust {
  border-left: 4px solid #ce422b;
}

.code-block-enhanced.lang-typescript {
  border-left: 4px solid var(--color-lang-typescript);
}

.code-block-enhanced.lang-json {
  border-left: 4px solid var(--color-blue-primary);
}

.code-block-enhanced.lang-xml {
  border-left: 4px solid var(--color-lang-xml);
}

.code-block-enhanced.lang-sql {
  border-left: 4px solid var(--color-lang-sql);
}

.code-block-enhanced.lang-bash,
.code-block-enhanced.lang-shell {
  border-left: 4px solid var(--color-lang-bash);
}

.code-block-enhanced.lang-powershell {
  border-left: 4px solid #5391fe;
}

.code-block-enhanced.lang-default {
  border-left: 4px solid var(--color-lang-default);
}

.code-block-enhanced pre[class*="language-"]:hover {
  background: rgba(0, 0, 0, 0.1);
}

html:not(.dark) .code-block-enhanced pre[class*="language-"]:hover {
  background: rgba(255, 255, 255, 0.1);
}

.code-block-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rem;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, transparent 100%);
  pointer-events: none;
  z-index: 5;
}

html:not(.dark) .code-block-header {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}

.code-block-header > * {
  pointer-events: auto;
}

.code-block-enhanced pre[class*="language-"] {
  margin: 0;
  padding: 3rem 1rem 1rem;
  border-radius: 0;
  background: transparent;
}
