/* Form Component Styles */
.chat-input-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.chat-input-field {
  flex: 1;
  display: flex;
  align-items: center;
}

#userInput {
  padding-right: 5.5rem !important; /* Space for both clear and send buttons - optimized spacing */
}

.input-container-relative {
  position: relative;
  display: flex;
  align-items: center;
}

.input-container-relative {
  min-height: 3rem; /* Ensure consistent height */
}

#userInput {
  min-height: 3rem !important;
  line-height: 1.5 !important;
}

.settings-input.api:focus {
  border-color: var(--color-api-settings);
  box-shadow: 0 0 0 1px var(--color-api-settings);
}

.settings-input.ui:focus {
  border-color: var(--color-ui-settings);
  box-shadow: 0 0 0 1px var(--color-ui-settings);
}

.settings-input.prompt:focus {
  border-color: var(--color-prompt-settings);
  box-shadow: 0 0 0 1px var(--color-prompt-settings);
}

.settings-input.advanced:focus {
  border-color: var(--color-advanced-settings);
  box-shadow: 0 0 0 1px var(--color-advanced-settings);
}

.input-enhanced {
  transition: all 0.2s ease-in-out;
  border: 2px solid transparent;
}

.input-enhanced:focus {
  transform: scale(1.01);
}

.input-enhanced.input-valid {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.input-enhanced.input-invalid {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-enhanced.input-warning {
  border-color: var(--color-warning);
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* Range Slider Improvements */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

/* Slider Track */
input[type="range"]::-webkit-slider-track {
  background: #e5e7eb !important; /* Light gray for light mode */
  height: 8px;
  border-radius: 4px;
}

html.dark input[type="range"]::-webkit-slider-track {
  background: #4b5563 !important; /* Better contrast for dark mode */
}

/* Slider Thumb */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--color-blue-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

html.dark input[type="range"]::-webkit-slider-thumb {
  border: 2px solid #000;
  background: var(--color-blue-primary);
}

/* Firefox */
input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 8px;
  border-radius: 4px;
  border: none;
}

html.dark input[type="range"]::-moz-range-track {
  background: #4b5563;
}

input[type="range"]::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--color-blue-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

html.dark input[type="range"]::-moz-range-thumb {
  border: 2px solid #000;
}

/* Checkbox Improvements */
input[type="checkbox"] {
  accent-color: var(--color-blue-primary);
}

html.dark input[type="checkbox"] {
  accent-color: var(--color-blue-primary);
  background-color: #374151 !important; /* Better background for dark mode */
  border-color: #6b7280 !important; /* Better border for dark mode */
}

html.dark input[type="checkbox"]:checked {
  background-color: var(--color-blue-primary) !important;
  border-color: var(--color-blue-primary) !important;
}

/* Select Dropdown Improvements */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

html.dark select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}
