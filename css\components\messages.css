/* Message Component Styles */
.message-stream {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.message-user {
  border-left: 4px solid var(--color-user-message);
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.05) 0%, rgba(14, 165, 233, 0.08) 100%);
  position: relative;
}

/* Removed duplicate user icon - using JavaScript-generated icon instead */

.message-assistant {
  border-left: 4px solid var(--color-assistant-message);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(168, 85, 247, 0.08) 100%);
  position: relative;
}

/* Removed duplicate assistant icon - using JavaScript-generated icon instead */

.message-system {
  border-left: 4px solid var(--color-system-message);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.08) 100%);
  position: relative;
}

/* Removed duplicate system icon - using JavaScript-generated icon instead */

.message-user:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.08) 0%, rgba(14, 165, 233, 0.12) 100%);
  border-left-color: var(--color-blue-light);
  transition: all 0.2s ease-in-out;
}

.message-assistant:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(168, 85, 247, 0.12) 100%);
  border-left-color: var(--color-purple-light);
  transition: all 0.2s ease-in-out;
}

.message-system:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.12) 100%);
  border-left-color: var(--color-green-light);
  transition: all 0.2s ease-in-out;
}

/* Removed hover effects for ::before elements since they no longer exist */

.message-role-user {
  border-left: 4px solid var(--color-user-message);
  background-color: rgba(56, 189, 248, 0.05);
}

.message-role-assistant {
  border-left: 4px solid var(--color-assistant-message);
  background-color: rgba(139, 92, 246, 0.05);
}

.message-role-system {
  border-left: 4px solid var(--color-system-message);
  background-color: rgba(16, 185, 129, 0.05);
}

.message-icon-user {
  color: var(--color-user-message);
}

.message-icon-assistant {
  color: var(--color-assistant-message);
}

.message-icon-system {
  color: var(--color-system-message);
}
