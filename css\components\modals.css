/* Modal Component Styles */
#promptLibraryModal {
  backdrop-filter: blur(4px);
}

.modal-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay,
#paramsPanel {
  z-index: 1000 !important;
}

#systemPromptEditorModal {
  z-index: 1100 !important;
}

#promptLibraryModal {
  z-index: 1200 !important;
}

.modal-content,
#paramsPanel > div,
#systemPromptEditorModal > div,
#promptLibraryModal > div {
  position: relative;
  z-index: 10;
}

.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 1rem;
  z-index: 1000;
}

.modal-content {
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
  margin: auto;
  z-index: 10;
}

.modal-open {
  overflow: hidden;
}

#paramsPanel:not(.invisible),
#systemPromptEditorModal:not(.invisible),
#promptLibraryModal:not(.invisible) {
  opacity: 1 !important;
  transform: scale(1) !important;
  pointer-events: auto !important;
}

.modal-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enhanced.modal-enter {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}

.modal-enhanced.modal-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.modal-enhanced.modal-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.modal-enhanced.modal-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}
