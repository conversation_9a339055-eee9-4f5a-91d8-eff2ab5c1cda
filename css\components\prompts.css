/* Prompt Library Component Styles */
#promptLibraryModal {
  backdrop-filter: blur(4px);
}

.prompt-category {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.prompt-category-builtin {
  color: var(--color-prompt-builtin);
  background-color: rgba(56, 189, 248, 0.1);
  border-color: rgba(56, 189, 248, 0.2);
}

.prompt-category-builtin:hover {
  background-color: rgba(56, 189, 248, 0.15);
  border-color: rgba(56, 189, 248, 0.3);
  color: var(--color-blue-light);
  transform: translateY(-1px);
}

.prompt-category-manual {
  color: var(--color-prompt-manual);
  background-color: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.2);
}

.prompt-category-manual:hover {
  background-color: rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
  color: var(--color-purple-light);
  transform: translateY(-1px);
}

.prompt-category-library {
  color: var(--color-prompt-library);
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

.prompt-category-library:hover {
  background-color: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
  color: var(--color-green-light);
  transform: translateY(-1px);
}

.prompt-category-custom {
  color: var(--color-prompt-custom);
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.prompt-category-custom:hover {
  background-color: rgba(245, 158, 11, 0.15);
  border-color: rgba(245, 158, 11, 0.3);
  color: var(--color-orange-light);
  transform: translateY(-1px);
}

.prompt-category i {
  margin-right: 0.375rem;
  font-size: 0.625rem;
}

.prompt-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  background: transparent;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  position: relative;
}

.prompt-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

html.dark .prompt-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.prompt-item-selected {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-color: rgba(56, 189, 248, 0.3);
  box-shadow: 0 0 0 1px rgba(56, 189, 248, 0.2);
}

.prompt-item-selected:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%);
  border-color: rgba(56, 189, 248, 0.4);
  box-shadow:
    0 0 0 1px rgba(56, 189, 248, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.prompt-item-active {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
}

.prompt-item-active:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.15) 100%);
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow:
    0 0 0 1px rgba(16, 185, 129, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.prompt-item-modified {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.2);
}

.prompt-item-modified:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.15) 100%);
  border-color: rgba(245, 158, 11, 0.4);
  box-shadow:
    0 0 0 1px rgba(245, 158, 11, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.prompt-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.prompt-item-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: inherit;
  margin: 0;
  line-height: 1.25;
}

.prompt-item-category {
  flex-shrink: 0;
  margin-left: 0.5rem;
}

.prompt-item-preview {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  max-height: 2.8rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 0.5rem;
}

html:not(.dark) .prompt-item-preview {
  color: rgba(0, 0, 0, 0.6);
}

.prompt-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.5);
}

html:not(.dark) .prompt-item-footer {
  color: rgba(0, 0, 0, 0.5);
}

.prompt-item-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.prompt-item-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.prompt-item:hover .prompt-item-actions {
  opacity: 1;
}

.prompt-item-action {
  padding: 0.25rem;
  border-radius: 0.25rem;
  background: transparent;
  border: none;
  color: inherit;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.prompt-item-action:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}
