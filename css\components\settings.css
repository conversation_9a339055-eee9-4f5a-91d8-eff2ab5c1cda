/* Settings Component Styles */
.settings-api {
  color: var(--color-api-settings);
  border-color: var(--color-api-settings);
}

.settings-ui {
  color: var(--color-ui-settings);
  border-color: var(--color-ui-settings);
}

.settings-prompt {
  color: var(--color-prompt-settings);
  border-color: var(--color-prompt-settings);
}

.settings-advanced {
  color: var(--color-advanced-settings);
  border-color: var(--color-advanced-settings);
}

.settings-section-header {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  border-left: 4px solid;
  transition: all 0.2s ease-in-out;
  display: block;
  width: 100%;
}

.settings-section-header .flex {
  flex-wrap: wrap;
  gap: 0.5rem;
}

.settings-section-header label {
  flex: 1;
  min-width: 200px;
}

.settings-section-header.api {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.1) 0%, rgba(14, 165, 233, 0.15) 100%);
  border-left-color: var(--color-api-settings);
  color: var(--color-api-settings);
}

.settings-section-header.ui {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.15) 100%);
  border-left-color: var(--color-ui-settings);
  color: var(--color-ui-settings);
}

.settings-section-header.prompt {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.15) 100%);
  border-left-color: var(--color-prompt-settings);
  color: var(--color-prompt-settings);
}

.settings-section-header.advanced {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.15) 100%);
  border-left-color: var(--color-advanced-settings);
  color: var(--color-advanced-settings);
}

.settings-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
  transition: color 0.2s ease-in-out;
}

.settings-icon.api {
  color: var(--color-api-settings);
}

.settings-icon.ui {
  color: var(--color-ui-settings);
}

.settings-icon.prompt {
  color: var(--color-prompt-settings);
}

.settings-icon.advanced {
  color: var(--color-advanced-settings);
}

.settings-divider {
  height: 1px;
  margin: 1.5rem 0;
  border: none;
  background: linear-gradient(90deg, transparent 0%, currentcolor 50%, transparent 100%);
  opacity: 0.3;
}

.settings-divider.api {
  color: var(--color-api-settings);
}

.settings-divider.ui {
  color: var(--color-ui-settings);
}

.settings-divider.prompt {
  color: var(--color-prompt-settings);
}

.settings-divider.advanced {
  color: var(--color-advanced-settings);
}

.settings-section-header:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

html.dark .settings-section-header:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.settings-section-header + .settings-section-header {
  margin-top: 1.5rem;
}

.settings-section-header h3,
.settings-section-header label,
.settings-section-header > * {
  display: block !important;
  width: 100% !important;
  margin-bottom: 0.5rem;
}

.settings-section-header.prompt .flex.justify-between.items-center {
  flex-direction: column !important;
  align-items: flex-start !important;
  gap: 0.5rem;
}

.settings-section-header.prompt .flex.justify-between.items-center > label {
  width: 100% !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

.settings-section-header.prompt .flex.justify-between.items-center > button {
  align-self: flex-start !important;
  margin-top: 0 !important;
}
