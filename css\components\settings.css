/* Settings Component Styles */
.settings-api {
  color: var(--color-api-settings);
  border-color: var(--color-api-settings);
}

.settings-ui {
  color: var(--color-ui-settings);
  border-color: var(--color-ui-settings);
}

.settings-prompt {
  color: var(--color-prompt-settings);
  border-color: var(--color-prompt-settings);
}

.settings-advanced {
  color: var(--color-advanced-settings);
  border-color: var(--color-advanced-settings);
}

.settings-section-header {
  padding: 1rem 1.25rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid;
  transition: all 0.2s ease-in-out;
  display: block;
  width: 100%;
  position: relative;
}

.settings-section-header .flex {
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

.settings-section-header label {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
}

/* Improved form element spacing within settings sections */
.settings-section-header > div {
  margin-bottom: 1rem;
}

.settings-section-header > div:last-child {
  margin-bottom: 0;
}

/* Better alignment for input groups */
.settings-section-header .relative {
  display: flex;
  align-items: center;
  position: relative;
}

/* Consistent button alignment */
.settings-section-header button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.settings-section-header.api {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.08) 0%, rgba(14, 165, 233, 0.12) 100%);
  border-left-color: var(--color-api-settings);
  color: var(--color-api-settings);
  box-shadow: 0 1px 3px rgba(56, 189, 248, 0.1);
}

.settings-section-header.ui {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(168, 85, 247, 0.12) 100%);
  border-left-color: var(--color-ui-settings);
  color: var(--color-ui-settings);
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.1);
}

.settings-section-header.prompt {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.12) 100%);
  border-left-color: var(--color-prompt-settings);
  color: var(--color-prompt-settings);
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.1);
}

.settings-section-header.advanced {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(217, 119, 6, 0.12) 100%);
  border-left-color: var(--color-advanced-settings);
  color: var(--color-advanced-settings);
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.1);
}

/* Subtle hover effects for better balance */
.settings-section-header.api:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.12) 0%, rgba(14, 165, 233, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(56, 189, 248, 0.15);
}

.settings-section-header.ui:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.12) 0%, rgba(168, 85, 247, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
}

.settings-section-header.prompt:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(5, 150, 105, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}

.settings-section-header.advanced:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.12) 0%, rgba(217, 119, 6, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.15);
}

.settings-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
  transition: color 0.2s ease-in-out;
}

.settings-icon.api {
  color: var(--color-api-settings);
}

.settings-icon.ui {
  color: var(--color-ui-settings);
}

.settings-icon.prompt {
  color: var(--color-prompt-settings);
}

.settings-icon.advanced {
  color: var(--color-advanced-settings);
}

.settings-divider {
  height: 1px;
  margin: 1.5rem 0;
  border: none;
  background: linear-gradient(90deg, transparent 0%, currentcolor 50%, transparent 100%);
  opacity: 0.3;
}

.settings-divider.api {
  color: var(--color-api-settings);
}

.settings-divider.ui {
  color: var(--color-ui-settings);
}

.settings-divider.prompt {
  color: var(--color-prompt-settings);
}

.settings-divider.advanced {
  color: var(--color-advanced-settings);
}

.settings-section-header:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Dark theme adjustments for better balance */
html.dark .settings-section-header.api {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.06) 0%, rgba(14, 165, 233, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(56, 189, 248, 0.08);
}

html.dark .settings-section-header.ui {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.06) 0%, rgba(168, 85, 247, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.08);
}

html.dark .settings-section-header.prompt {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.06) 0%, rgba(5, 150, 105, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.08);
}

html.dark .settings-section-header.advanced {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.06) 0%, rgba(217, 119, 6, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.08);
}

html.dark .settings-section-header:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
}

html.dark .settings-section-header.api:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.1) 0%, rgba(14, 165, 233, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(56, 189, 248, 0.12);
}

html.dark .settings-section-header.ui:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.12);
}

html.dark .settings-section-header.prompt:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.12);
}

html.dark .settings-section-header.advanced:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.12);
}

.settings-section-header + .settings-section-header {
  margin-top: 1.5rem;
}

/* Modal-specific settings improvements */
#paramsPanel .settings-section-header {
  padding: 1rem;
  margin-bottom: 1.5rem;
}

#paramsPanel .settings-section-header h3 {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-weight: 600;
}

#paramsPanel .settings-section-header label {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* Better spacing for form elements in modal */
#paramsPanel .mb-4 {
  margin-bottom: 1rem;
}

#paramsPanel .space-y-4 > * + * {
  margin-top: 1rem;
}

/* Improved input field alignment */
#paramsPanel input,
#paramsPanel select,
#paramsPanel textarea {
  width: 100%;
  display: block;
}

/* Better button alignment */
#paramsPanel .flex {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

#paramsPanel .flex.justify-between {
  justify-content: space-between;
}

#paramsPanel .flex.items-center {
  align-items: center;
}

/* Range slider improvements */
#paramsPanel input[type="range"] {
  width: 100%;
  margin: 0.5rem 0;
}

/* Checkbox alignment */
#paramsPanel .flex.items-center input[type="checkbox"] {
  margin-right: 0.5rem;
  flex-shrink: 0;
}

/* Button group improvements */
#paramsPanel .flex.space-x-4 button {
  flex: 1;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Input field with button alignment */
#paramsPanel .relative input {
  padding-right: 3rem;
}

#paramsPanel .relative button {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

/* Status indicator positioning */
#paramsPanel .status-dot {
  position: absolute;
  right: 2.5rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 5;
}

/* Action buttons at bottom of sections */
#paramsPanel .settings-section-header .flex.justify-between {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

html.dark #paramsPanel .settings-section-header .flex.justify-between {
  border-top-color: rgba(255, 255, 255, 0.05);
}

/* Consistent spacing for all form elements */
#paramsPanel .settings-section-header > * {
  margin-bottom: 1rem;
}

#paramsPanel .settings-section-header > *:last-child {
  margin-bottom: 0;
}

/* Perfect alignment for expand button */
#paramsPanel #expandSystemPromptBtn {
  margin-left: auto;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
  display: inline-flex;
  align-items: center;
}

/* Better alignment for prompt library button and status */
#paramsPanel .flex.justify-between {
  align-items: center;
  min-height: 2rem;
}

/* Improved divider styling */
#paramsPanel .settings-divider {
  margin: 1.5rem 0;
  opacity: 0.4;
}

/* Enhanced modal content spacing */
#paramsPanel .p-6 {
  padding: 1.5rem;
}

/* Perfect button heights */
#paramsPanel button {
  min-height: 2.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Range slider label alignment */
#paramsPanel .flex.justify-between.items-center {
  margin-bottom: 0.5rem;
}

/* Save button section enhancement */
#paramsPanel .pt-4.border-t {
  padding-top: 1.5rem;
  margin-top: 1.5rem;
}

/* Icon spacing consistency */
#paramsPanel i.fas {
  margin-right: 0.5rem;
  width: 1rem;
  text-align: center;
}

.settings-section-header.prompt .flex.justify-between.items-center {
  flex-direction: column !important;
  align-items: flex-start !important;
  gap: 0.5rem;
}

.settings-section-header.prompt .flex.justify-between.items-center > label {
  width: 100% !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

.settings-section-header.prompt .flex.justify-between.items-center > button {
  align-self: flex-start !important;
  margin-top: 0 !important;
}
