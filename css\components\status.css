/* Status Indicator Components */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  border: 1px solid transparent;
}

.status-indicator i {
  margin-right: 0.5rem;
  font-size: 0.75rem;
}

.status-connected {
  color: var(--color-connected);
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

.status-connected i {
  color: var(--color-connected);
  animation: pulse-green 2s infinite;
}

.status-error,
.status-disconnected {
  color: var(--color-error);
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.status-error i,
.status-disconnected i {
  color: var(--color-error);
  animation: shake-red 1s infinite;
}

.status-warning {
  color: var(--color-warning);
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.status-warning i {
  color: var(--color-warning);
  animation: blink-orange 1.5s infinite;
}

/* Token validation indicators - smaller and less intrusive */
.visual-indicator-alt {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  border: none !important;
  background-color: var(--color-connected) !important;
  opacity: 0.7;
  transition: opacity 0.2s ease-in-out;
}

.visual-indicator-alt:hover {
  opacity: 1;
}

.visual-indicator-alt.status-error {
  background-color: var(--color-error) !important;
}

.visual-indicator-alt.status-warning {
  background-color: var(--color-warning) !important;
}

.status-loading,
.status-connecting {
  color: var(--color-loading);
  background-color: rgba(56, 189, 248, 0.1);
  border-color: rgba(56, 189, 248, 0.2);
}

.status-loading i,
.status-connecting i {
  color: var(--color-loading);
  animation: spin-blue 1s linear infinite;
}

.status-indicator-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.status-indicator-sm i {
  font-size: 0.625rem;
  margin-right: 0.25rem;
}

.status-indicator-lg {
  padding: 0.5rem 1rem;
  font-size: 1rem;
}

.status-indicator-lg i {
  font-size: 0.875rem;
  margin-right: 0.75rem;
}

.status-indicator-outline {
  background-color: transparent;
  border-width: 1px;
}

.status-indicator-solid {
  border: none;
}

.status-indicator-solid.status-connected {
  background-color: var(--color-connected);
  color: white;
}

.status-indicator-solid.status-error,
.status-indicator-solid.status-disconnected {
  background-color: var(--color-error);
  color: white;
}

.status-indicator-solid.status-warning {
  background-color: var(--color-warning);
  color: white;
}

.status-indicator-solid.status-loading,
.status-indicator-solid.status-connecting {
  background-color: var(--color-loading);
  color: white;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 2px solid transparent;
  transition: all 0.3s ease-in-out;
}

.status-badge.status-connected {
  background-color: var(--color-connected);
  border-color: rgba(16, 185, 129, 0.3);
  animation: pulse-green-badge 2s infinite;
}

.status-badge.status-error,
.status-badge.status-disconnected {
  background-color: var(--color-error);
  border-color: rgba(239, 68, 68, 0.3);
  animation: pulse-red-badge 1s infinite;
}

.status-badge.status-warning {
  background-color: var(--color-warning);
  border-color: rgba(245, 158, 11, 0.3);
  animation: pulse-orange-badge 1.5s infinite;
}

.status-badge.status-loading,
.status-badge.status-connecting {
  background-color: var(--color-loading);
  border-color: rgba(56, 189, 248, 0.3);
  animation: pulse-blue-badge 1s infinite;
}

.status-dot {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease-in-out;
}

.status-dot.status-connected {
  background-color: var(--color-connected);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  animation: glow-green 2s infinite;
}

.status-dot.status-error,
.status-dot.status-disconnected {
  background-color: var(--color-error);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  animation: glow-red 1s infinite;
}

.status-dot.status-warning {
  background-color: var(--color-warning);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: glow-orange 1.5s infinite;
}

.status-dot.status-loading,
.status-dot.status-connecting {
  background-color: var(--color-loading);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
  animation: glow-blue 1s infinite;
}

.status-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-transition.status-changing {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.status-text-connected {
  color: var(--color-connected);
}

.status-text-error,
.status-text-disconnected {
  color: var(--color-error);
}

.status-text-warning {
  color: var(--color-warning);
}

.status-text-loading,
.status-text-connecting {
  color: var(--color-loading);
}

.status-bg-connected {
  background-color: rgba(16, 185, 129, 0.1);
}

.status-bg-error,
.status-bg-disconnected {
  background-color: rgba(239, 68, 68, 0.1);
}

.status-bg-warning {
  background-color: rgba(245, 158, 11, 0.1);
}

.status-bg-loading,
.status-bg-connecting {
  background-color: rgba(56, 189, 248, 0.1);
}

.status-border-connected {
  border-color: rgba(16, 185, 129, 0.3);
}

.status-border-error,
.status-border-disconnected {
  border-color: rgba(239, 68, 68, 0.3);
}

.status-border-warning {
  border-color: rgba(245, 158, 11, 0.3);
}

.status-border-loading,
.status-border-connecting {
  border-color: rgba(56, 189, 248, 0.3);
}
