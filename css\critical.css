:root{--color-blue-primary:#38bdf8;--color-blue-secondary:#0ea5e9;--color-blue-accent:#3b82f6;--color-blue-light:#7dd3fc;--color-blue-dark:#0284c7;--color-purple-primary:#8b5cf6;--color-purple-secondary:#a855f7;--color-purple-light:#c084fc;--color-purple-dark:#7c3aed;--color-green-primary:#10b981;--color-green-secondary:#059669;--color-green-light:#34d399;--color-green-dark:#047857;--color-orange-primary:#f59e0b;--color-orange-secondary:#d97706;--color-orange-light:#fbbf24;--color-orange-dark:#b45309;--color-user-message:var(--color-blue-primary);--color-assistant-message:var(--color-purple-primary);--color-system-message:var(--color-green-primary);--color-warning:var(--color-orange-primary);--color-success:var(--color-green-primary);--color-error:#ef4444;--color-info:var(--color-blue-primary);--color-accent:var(--color-purple-primary);--color-api-settings:var(--color-blue-primary);--color-ui-settings:var(--color-purple-primary);--color-prompt-settings:var(--color-green-primary);--color-advanced-settings:var(--color-orange-primary);--color-connected:var(--color-green-primary);--color-disconnected:var(--color-error);--color-connecting:var(--color-blue-primary);--color-loading:var(--color-blue-primary);--color-hover-blue:var(--color-blue-light);--color-hover-purple:var(--color-purple-light);--color-hover-green:var(--color-green-light);--color-hover-orange:var(--color-orange-light);--color-focus-blue:var(--color-blue-accent);--color-focus-purple:var(--color-purple-secondary);--color-focus-green:var(--color-green-secondary);--color-focus-orange:var(--color-orange-secondary);--color-lang-javascript:#f7df1e;--color-lang-python:#3776ab;--color-lang-html:#e34f26;--color-lang-css:#1572b6;--color-lang-java:#ed8b00;--color-lang-cpp:#00599c;--color-lang-csharp:#239120;--color-lang-php:#777bb4;--color-lang-ruby:#cc342d;--color-lang-go:#00add8;--color-lang-rust:#000;--color-lang-typescript:#3178c6;--color-lang-json:#000;--color-lang-xml:#f60;--color-lang-sql:#336791;--color-lang-bash:#4eaa25;--color-lang-powershell:#012456;--color-lang-default:var(--color-blue-primary);--color-prompt-builtin:var(--color-blue-primary);--color-prompt-manual:var(--color-purple-primary);--color-prompt-library:var(--color-green-primary);--color-prompt-custom:var(--color-orange-primary);--color-prompt-active:var(--color-green-primary);--color-prompt-modified:var(--color-orange-primary);--color-prompt-error:var(--color-error);--color-prompt-inactive:var(--color-sora-gray-light)} :root{--color-blue-primary:#06f;--color-blue-secondary:#0052cc;--color-purple-primary:#60c;--color-purple-secondary:#5200a3;--color-green-primary:#0c6;--color-green-secondary:#00a352;--color-orange-primary:#f60;--color-orange-secondary:#cc5200;--color-error:#c00} :root{--shadow-blue:rgba(56,189,248,.3);--shadow-purple:rgba(139,92,246,.3);--shadow-green:rgba(16,185,129,.3);--shadow-orange:rgba(245,158,11,.3);--shadow-neutral:rgba(0,0,0,.1);--shadow-neutral-dark:rgba(0,0,0,.3);--glow-blue:rgba(56,189,248,.4);--glow-purple:rgba(139,92,246,.4);--glow-green:rgba(16,185,129,.4);--glow-orange:rgba(245,158,11,.4);--layer-bg-1:hsla(0,0%,100%,.02);--layer-bg-2:hsla(0,0%,100%,.04);--layer-bg-3:hsla(0,0%,100%,.06);--layer-bg-4:hsla(0,0%,100%,.08);--layer-bg-5:hsla(0,0%,100%,.1)} :root{--animation-duration-fast:150ms;--animation-duration-normal:300ms;--animation-duration-slow:500ms;--animation-ease-out:cubic-bezier(0.25,0.46,0.45,0.94);--animation-ease-in-out:cubic-bezier(0.4,0,0.2,1);--animation-ease-spring:cubic-bezier(0.68,-0.55,0.265,1.55);--animation-ease-bounce:cubic-bezier(0.68,-0.6,0.32,1.6);--animation-delay-short:50ms;--animation-delay-medium:100ms;--animation-delay-long:200ms} :root{--color-blue-primary:#06f;--color-blue-secondary:#0052cc;--color-purple-primary:#60c;--color-purple-secondary:#5200a3;--color-green-primary:#0c6;--color-green-secondary:#00a352;--color-orange-primary:#f60;--color-orange-secondary:#cc5200;--color-error:#c00} @import "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap";html{font-size:90%} body{overflow-x:hidden} html.dark body{background-color:#000;color:#fff} body{font-family:Inter,sans-serif} .gradient-text{background:linear-gradient(90deg,#38bdf8,#0ea5e9);-webkit-background-clip:text;background-clip:text;color:transparent} html:not(.dark){--layer-bg-1:rgba(0,0,0,.02);--layer-bg-2:rgba(0,0,0,.04);--layer-bg-3:rgba(0,0,0,.06);--layer-bg-4:rgba(0,0,0,.08);--layer-bg-5:rgba(0,0,0,.1);--shadow-neutral:rgba(0,0,0,.15);--shadow-neutral-dark:rgba(0,0,0,.25)} .container-layer-base{background:var(--layer-bg-1);border:1px solid hsla(0,0%,100%,.05);border-radius:.5rem;position:relative} html:not(.dark) .container-layer-base{border-color:rgba(0,0,0,.05)} .container-layer-elevated{background:var(--layer-bg-2);border:1px solid hsla(0,0%,100%,.08);border-radius:.5rem;box-shadow:0 2px 8px var(--shadow-neutral);position:relative;z-index:10} html:not(.dark) .container-layer-elevated{border-color:rgba(0,0,0,.08)} .container-layer-floating{background:var(--layer-bg-3);border:1px solid hsla(0,0%,100%,.12);border-radius:.5rem;box-shadow:0 4px 15px var(--shadow-neutral);position:relative;z-index:20} html:not(.dark) .container-layer-floating{border-color:rgba(0,0,0,.12)} .container-layer-modal{background:var(--layer-bg-4);border:1px solid hsla(0,0%,100%,.15);border-radius:.5rem;box-shadow:0 8px 25px var(--shadow-neutral-dark);position:relative;z-index:50} html:not(.dark) .container-layer-modal{border-color:rgba(0,0,0,.15)} .border-blue-primary{border-color:var(--color-blue-primary)} .border-blue-secondary{border-color:var(--color-blue-secondary)} .border-purple-primary{border-color:var(--color-purple-primary)} .border-purple-secondary{border-color:var(--color-purple-secondary)} .border-green-primary{border-color:var(--color-green-primary)} .border-green-secondary{border-color:var(--color-green-secondary)} .border-orange-primary{border-color:var(--color-orange-primary)} .border-orange-secondary{border-color:var(--color-orange-secondary)} .border-user-message{border-color:var(--color-user-message)} .border-assistant-message{border-color:var(--color-assistant-message)} .border-system-message{border-color:var(--color-system-message)} .border-success{border-color:var(--color-success)} .border-warning{border-color:var(--color-warning)} .border-error{border-color:var(--color-error)} .border-info{border-color:var(--color-info)} .border-accent{border-color:var(--color-accent)} .shadow-blue-sm{box-shadow:0 1px 3px var(--shadow-blue),0 1px 2px rgba(0,0,0,.1)} .settings-button{transition:all .2s ease-in-out} .settings-button.api{border-color:var(--color-api-settings);color:var(--color-api-settings)} .settings-button.api:hover{background-color:rgba(56,189,248,.1);border-color:var(--color-blue-light);color:var(--color-blue-light)} .settings-button.ui{border-color:var(--color-ui-settings);color:var(--color-ui-settings)} .settings-button.ui:hover{background-color:rgba(139,92,246,.1);border-color:var(--color-purple-light);color:var(--color-purple-light)} .settings-button.prompt{border-color:var(--color-prompt-settings);color:var(--color-prompt-settings)} .settings-button.prompt:hover{background-color:rgba(16,185,129,.1);border-color:var(--color-green-light);color:var(--color-green-light)} .settings-button.advanced{border-color:var(--color-advanced-settings);color:var(--color-advanced-settings)} .settings-button.advanced:hover{background-color:rgba(245,158,11,.1);border-color:var(--color-orange-light);color:var(--color-orange-light)} .chat-send-button{align-items:center;display:flex;flex-shrink:0;justify-content:center} html.dark .message-user:before{background:hsla(0,0%,100%,.1)} html.dark .message-assistant:before{background:hsla(0,0%,100%,.1)} html.dark .message-system:before{background:hsla(0,0%,100%,.1)} .settings-section-header{border-left:4px solid;border-radius:.5rem;display:block;margin-bottom:1rem;padding:.75rem 1rem;transition:all .2s ease-in-out;width:100%} .settings-section-header .flex{flex-wrap:wrap;gap:.5rem} .settings-section-header label{flex:1;min-width:200px} .settings-section-header.api{background:linear-gradient(135deg,rgba(56,189,248,.1),rgba(14,165,233,.15));border-left-color:var(--color-api-settings);color:var(--color-api-settings)} .settings-section-header.ui{background:linear-gradient(135deg,rgba(139,92,246,.1),rgba(168,85,247,.15));border-left-color:var(--color-ui-settings);color:var(--color-ui-settings)} .settings-section-header.prompt{background:linear-gradient(135deg,rgba(16,185,129,.1),rgba(5,150,105,.15));border-left-color:var(--color-prompt-settings);color:var(--color-prompt-settings)} .settings-section-header.advanced{background:linear-gradient(135deg,rgba(245,158,11,.1),rgba(217,119,6,.15));border-left-color:var(--color-advanced-settings);color:var(--color-advanced-settings)} .settings-section-header:hover{box-shadow:0 2px 8px rgba(0,0,0,.1);transform:translateX(4px)} html.dark .settings-section-header:hover{box-shadow:0 2px 8px hsla(0,0%,100%,.1)} .settings-section-header+.settings-section-header{margin-top:1.5rem} .settings-section-header h3,.settings-section-header label,.settings-section-header>*{display:block!important;margin-bottom:.5rem;width:100%!important} .settings-section-header.prompt .flex.justify-between.items-center{align-items:flex-start!important;flex-direction:column!important;gap:.5rem} .settings-section-header.prompt .flex.justify-between.items-center>label{display:block!important;margin-bottom:.5rem!important;width:100%!important} .settings-section-header.prompt .flex.justify-between.items-center>button{align-self:flex-start!important;margin-top:0!important} pre[class*=language-]{border-radius:.5rem;box-sizing:border-box;margin:1em 0;max-height:500px;max-width:100%;width:100%} html:not(.dark) pre[class*=language-]{background:#2d2d2d;box-shadow:0 2px 8px rgba(0,0,0,.15);color:#ccc} html.dark pre[class*=language-]{background:#1a1a1a;border:1px solid #333;box-shadow:0 2px 8px rgba(0,0,0,.3)} code[class*=language-],pre[class*=language-]{background:none;color:#f8f8f2;font-family:Consolas,Monaco,Andale Mono,Ubuntu Mono,monospace;font-size:1em;text-align:left;text-shadow:0 1px rgba(0,0,0,.3);white-space:pre;word-break:normal;word-spacing:normal;word-wrap:normal;-webkit-hyphens:none;hyphens:none;line-height:1.3;-moz-tab-size:4;-o-tab-size:4;tab-size:4} pre[class*=language-]{border-radius:.3em;line-height:1.3;margin:.5em 0;overflow:auto;padding:1em;white-space:pre} :not(pre)>code[class*=language-],pre[class*=language-]{background:#272822} :not(pre)>code[class*=language-]{border-radius:.3em;padding:.1em;white-space:normal} html.dark :not(pre)>code{background:hsla(0,0%,100%,.1);color:#ff6b6b} html.dark .code-title{background:#333;border-bottom:1px solid #444} pre[class*=language-]{position:relative} pre[class*=language-]::-webkit-scrollbar{height:8px;width:8px} pre[class*=language-]::-webkit-scrollbar-track{background:rgba(0,0,0,.1);border-radius:4px} pre[class*=language-]::-webkit-scrollbar-thumb{background:hsla(0,0%,100%,.2);border-radius:4px} pre[class*=language-]::-webkit-scrollbar-thumb:hover{background:hsla(0,0%,100%,.3)} html:not(.dark) .code-block-enhanced{background:linear-gradient(135deg,hsla(0,0%,100%,.8),hsla(0,0%,100%,.9));border-color:rgba(0,0,0,.1)} html:not(.dark) .code-block-enhanced:hover{border-color:rgba(56,189,248,.3);box-shadow:0 4px 12px rgba(0,0,0,.1)} .code-language-badge.lang-html{background-color:rgba(227,79,38,.15);border-color:rgba(227,79,38,.3);color:var(--color-lang-html)} .code-block-enhanced.lang-html{border-left:4px solid var(--color-lang-html)} .code-block-enhanced pre[class*=language-]:hover{background:rgba(0,0,0,.1)} html:not(.dark) .code-block-enhanced pre[class*=language-]:hover{background:hsla(0,0%,100%,.1)} .code-block-header{background:linear-gradient(180deg,rgba(0,0,0,.1),transparent);height:3rem;left:0;pointer-events:none;position:absolute;right:0;top:0;z-index:5} html:not(.dark) .code-block-header{background:linear-gradient(180deg,hsla(0,0%,100%,.1),transparent)} .code-block-header>*{pointer-events:auto} .code-block-enhanced pre[class*=language-]{background:transparent;border-radius:0;margin:0;padding:3rem 1rem 1rem} .prompt-item:hover,html.dark .prompt-item:hover{background:hsla(0,0%,100%,.05);border-color:hsla(0,0%,100%,.1)} html.dark .prompt-item:hover{box-shadow:0 2px 8px rgba(0,0,0,.3)} .prompt-item-header{align-items:flex-start;display:flex;justify-content:space-between;margin-bottom:.5rem} html:not(.dark) .prompt-item-preview{color:rgba(0,0,0,.6)} html:not(.dark) .prompt-item-footer{color:rgba(0,0,0,.5)} .focus-enhanced:focus{border-radius:.25rem;box-shadow:0 0 0 4px rgba(56,189,248,.2);outline:2px solid var(--color-blue-primary);outline-offset:2px} .focus-enhanced.focus-purple:focus{box-shadow:0 0 0 4px rgba(139,92,246,.2);outline-color:var(--color-purple-primary)} .focus-enhanced.focus-green:focus{box-shadow:0 0 0 4px rgba(16,185,129,.2);outline-color:var(--color-green-primary)} .focus-enhanced.focus-orange:focus{box-shadow:0 0 0 4px rgba(245,158,11,.2);outline-color:var(--color-orange-primary)} @keyframes pulse-green-badge{0%,to{opacity:1;transform:scale(1)} @keyframes pulse-red-badge{0%,to{opacity:1;transform:scale(1)} @keyframes pulse-orange-badge{0%,to{opacity:1;transform:scale(1)} @keyframes pulse-blue-badge{0%,to{opacity:1;transform:scale(1)} @keyframes pulse-blue{0%,to{background-color:var(--color-blue-primary);box-shadow:0 0 0 0 rgba(56,189,248,.7)} @keyframes pulse-purple{0%,to{background-color:var(--color-purple-primary);box-shadow:0 0 0 0 rgba(139,92,246,.7)} @keyframes pulse-green{0%,to{background-color:var(--color-green-primary);box-shadow:0 0 0 0 rgba(16,185,129,.7)} @keyframes pulse-orange{0%,to{background-color:var(--color-orange-primary);box-shadow:0 0 0 0 rgba(245,158,11,.7)} @keyframes pulse{0%,to{opacity:1;transform:scale(1)} @keyframes spin-blue{0%{transform:rotate(0deg)} @keyframes spin{0%{transform:rotate(0deg)}