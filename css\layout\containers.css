/* Container and Wrapper Styles */
.container-layer-base {
  background: var(--layer-bg-1);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  position: relative;
}

html:not(.dark) .container-layer-base {
  border-color: rgba(0, 0, 0, 0.05);
}

.container-layer-elevated {
  background: var(--layer-bg-2);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px var(--shadow-neutral);
  position: relative;
  z-index: 10;
}

html:not(.dark) .container-layer-elevated {
  border-color: rgba(0, 0, 0, 0.08);
}

.container-layer-floating {
  background: var(--layer-bg-3);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px var(--shadow-neutral);
  position: relative;
  z-index: 20;
}

html:not(.dark) .container-layer-floating {
  border-color: rgba(0, 0, 0, 0.12);
}

.container-layer-modal {
  background: var(--layer-bg-4);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 0.5rem;
  box-shadow: 0 8px 25px var(--shadow-neutral-dark);
  position: relative;
  z-index: 50;
}

html:not(.dark) .container-layer-modal {
  border-color: rgba(0, 0, 0, 0.15);
}
