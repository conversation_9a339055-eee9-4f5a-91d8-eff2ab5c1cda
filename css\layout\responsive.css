/* Responsive Breakpoint System */
@media (width <= 768px) {
  pre[class*="language-"] {
    max-width: calc(100vw - 6rem);
    font-size: 0.85rem;
  }

  .code-title {
    max-width: calc(100vw - 6rem);
    font-size: 0.75rem;
  }

  .thinking-spoiler-bubble {
    width: 95%;
    max-width: 95%;
  }

  .spoiler-header {
    padding: 0.5rem 0.75rem;
  }

  .thinking-spoiler-bubble.expanded .spoiler-content {
    max-height: 500px;
  }

  .spoiler-header-text {
    font-size: 0.9rem;
  }
}

@media (width <= 640px) {
  pre[class*="language-"] {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.2;
  }

  code[class*="language-"] {
    line-height: 1.2;
  }

  .code-language-badge {
    top: 0.5rem;
    right: 2.5rem;
    padding: 0.125rem 0.5rem;
    font-size: 0.625rem;
  }

  .code-copy-btn-enhanced {
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.375rem 0.5rem;
    font-size: 0.625rem;
  }

  .code-block-enhanced pre[class*="language-"] {
    padding: 2.5rem 0.75rem 0.75rem;
  }
}
