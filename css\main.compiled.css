
@import "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap";
/* Nebula AI Interface - Modular CSS Architecture */
/* Main entry point with organized imports */
/* Base Styles */
/* CSS Reset and Normalization */
html {
  /* Set base font size to 80% of browser default */
  font-size: 90%;
}
body {
  /* Default text for light mode (Tailwind handles dark:text-white) */

  /* Default background for light mode (Tailwind handles dark:bg-black) */
  overflow-x: hidden;
}
/* Apply base dark mode styles here for initial load */
html.dark body {
  background-color: #000; /* Sora black */
  color: #fff; /* Sora white */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
/* Typography System */
body {
  font-family: Inter, sans-serif;
}
.gradient-text {
  background: linear-gradient(90deg, #38bdf8, #0ea5e9);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
/* This file is intentionally left empty. */
/* All CSS custom properties and design tokens are now located in the /css/tokens/ directory. */
/* Design Tokens */
/* Color Palette and Semantic Colors */
:root {
  /* Enhanced Color Palette */
  --color-blue-primary: #38bdf8;
  --color-blue-secondary: #0ea5e9;
  --color-blue-accent: rgb(59, 130, 246);
  --color-blue-light: #7dd3fc;
  --color-blue-dark: #0284c7;
  --color-purple-primary: #8b5cf6;
  --color-purple-secondary: #a855f7;
  --color-purple-light: #c084fc;
  --color-purple-dark: #7c3aed;
  --color-green-primary: #10b981;
  --color-green-secondary: #059669;
  --color-green-light: #34d399;
  --color-green-dark: #047857;
  --color-orange-primary: #f59e0b;
  --color-orange-secondary: #d97706;
  --color-orange-light: #fbbf24;
  --color-orange-dark: #b45309;

  /* Semantic Color Variables */
  --color-user-message: var(--color-blue-primary);
  --color-assistant-message: var(--color-purple-primary);
  --color-system-message: var(--color-green-primary);
  --color-warning: var(--color-orange-primary);
  --color-success: var(--color-green-primary);
  --color-error: #ef4444;
  --color-info: var(--color-blue-primary);
  --color-accent: var(--color-purple-primary);

  /* UI Context Colors */
  --color-api-settings: var(--color-blue-primary);
  --color-ui-settings: var(--color-purple-primary);
  --color-prompt-settings: var(--color-green-primary);
  --color-advanced-settings: var(--color-orange-primary);

  /* Status Colors */
  --color-connected: var(--color-green-primary);
  --color-disconnected: var(--color-error);
  --color-connecting: var(--color-blue-primary);
  --color-loading: var(--color-blue-primary);

  /* Interactive State Colors */
  --color-hover-blue: var(--color-blue-light);
  --color-hover-purple: var(--color-purple-light);
  --color-hover-green: var(--color-green-light);
  --color-hover-orange: var(--color-orange-light);
  --color-focus-blue: var(--color-blue-accent);
  --color-focus-purple: var(--color-purple-secondary);
  --color-focus-green: var(--color-green-secondary);
  --color-focus-orange: var(--color-orange-secondary);

  /* Language-specific color variables */
  --color-lang-javascript: #f7df1e;
  --color-lang-python: #3776ab;
  --color-lang-html: #e34f26;
  --color-lang-css: #1572b6;
  --color-lang-java: #ed8b00;
  --color-lang-cpp: #00599c;
  --color-lang-csharp: #239120;
  --color-lang-php: #777bb4;
  --color-lang-ruby: #cc342d;
  --color-lang-go: #00add8;
  --color-lang-rust: #000;
  --color-lang-typescript: #3178c6;
  --color-lang-json: #000;
  --color-lang-xml: #f60;
  --color-lang-sql: #336791;
  --color-lang-bash: #4eaa25;
  --color-lang-powershell: #012456;
  --color-lang-default: var(--color-blue-primary);

  /* Prompt Category Color Variables */
  --color-prompt-builtin: var(--color-blue-primary);
  --color-prompt-manual: var(--color-purple-primary);
  --color-prompt-library: var(--color-green-primary);
  --color-prompt-custom: var(--color-orange-primary);

  /* Prompt State Colors */
  --color-prompt-active: var(--color-green-primary);
  --color-prompt-modified: var(--color-orange-primary);
  --color-prompt-error: var(--color-error);
  --color-prompt-inactive: var(--color-sora-gray-light);
}
@media (prefers-contrast: high) {
  :root {
    /* High contrast color overrides */
    --color-blue-primary: #06f;
    --color-blue-secondary: #0052cc;
    --color-purple-primary: #60c;
    --color-purple-secondary: #5200a3;
    --color-green-primary: #0c6;
    --color-green-secondary: #00a352;
    --color-orange-primary: #f60;
    --color-orange-secondary: #cc5200;
    --color-error: #c00;
  }
}
/* Spacing Scale and Layout Tokens */
/* This file will contain spacing scale and layout token definitions */
/* Shadow and Depth Tokens */
:root {
  /* Shadow Colors */
  --shadow-blue: rgba(56, 189, 248, 0.3);
  --shadow-purple: rgba(139, 92, 246, 0.3);
  --shadow-green: rgba(16, 185, 129, 0.3);
  --shadow-orange: rgba(245, 158, 11, 0.3);
  --shadow-neutral: rgba(0, 0, 0, 0.1);
  --shadow-neutral-dark: rgba(0, 0, 0, 0.3);

  /* Glow Colors */
  --glow-blue: rgba(56, 189, 248, 0.4);
  --glow-purple: rgba(139, 92, 246, 0.4);
  --glow-green: rgba(16, 185, 129, 0.4);
  --glow-orange: rgba(245, 158, 11, 0.4);

  /* Layer Background Colors */
  --layer-bg-1: rgba(255, 255, 255, 0.02);
  --layer-bg-2: rgba(255, 255, 255, 0.04);
  --layer-bg-3: rgba(255, 255, 255, 0.06);
  --layer-bg-4: rgba(255, 255, 255, 0.08);
  --layer-bg-5: rgba(255, 255, 255, 0.1);
}
html:not(.dark) {
  --layer-bg-1: rgba(0, 0, 0, 0.02);
  --layer-bg-2: rgba(0, 0, 0, 0.04);
  --layer-bg-3: rgba(0, 0, 0, 0.06);
  --layer-bg-4: rgba(0, 0, 0, 0.08);
  --layer-bg-5: rgba(0, 0, 0, 0.1);
  --shadow-neutral: rgba(0, 0, 0, 0.15);
  --shadow-neutral-dark: rgba(0, 0, 0, 0.25);
}
/* Animation Timing Tokens */
:root {
  /* Animation Durations */
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 300ms;
  --animation-duration-slow: 500ms;

  /* Animation Timing Functions */
  --animation-ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --animation-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-ease-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);

  /* Animation Delays */
  --animation-delay-short: 50ms;
  --animation-delay-medium: 100ms;
  --animation-delay-long: 200ms;
}
/* Layout System */
/* Container and Wrapper Styles */
.container-layer-base {
  background: var(--layer-bg-1);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  position: relative;
}
html:not(.dark) .container-layer-base {
  border-color: rgba(0, 0, 0, 0.05);
}
.container-layer-elevated {
  background: var(--layer-bg-2);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px var(--shadow-neutral);
  position: relative;
  z-index: 10;
}
html:not(.dark) .container-layer-elevated {
  border-color: rgba(0, 0, 0, 0.08);
}
.container-layer-floating {
  background: var(--layer-bg-3);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px var(--shadow-neutral);
  position: relative;
  z-index: 20;
}
html:not(.dark) .container-layer-floating {
  border-color: rgba(0, 0, 0, 0.12);
}
.container-layer-modal {
  background: var(--layer-bg-4);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 0.5rem;
  box-shadow: 0 8px 25px var(--shadow-neutral-dark);
  position: relative;
  z-index: 50;
}
html:not(.dark) .container-layer-modal {
  border-color: rgba(0, 0, 0, 0.15);
}
/* Grid System */
/* This file will contain grid system utilities */
/* Responsive Breakpoint System */
@media (max-width: 768px) {
  pre[class*="language-"] {
    max-width: calc(100vw - 6rem);
    font-size: 0.85rem;
  }

  .code-title {
    max-width: calc(100vw - 6rem);
    font-size: 0.75rem;
  }

  .thinking-spoiler-bubble {
    width: 95%;
    max-width: 95%;
  }

  .spoiler-header {
    padding: 0.5rem 0.75rem;
  }

  .thinking-spoiler-bubble.expanded .spoiler-content {
    max-height: 500px;
  }

  .spoiler-header-text {
    font-size: 0.9rem;
  }
}
@media (max-width: 640px) {
  pre[class*="language-"] {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.2;
  }

  code[class*="language-"] {
    line-height: 1.2;
  }

  .code-language-badge {
    top: 0.5rem;
    right: 2.5rem;
    padding: 0.125rem 0.5rem;
    font-size: 0.625rem;
  }

  .code-copy-btn-enhanced {
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.375rem 0.5rem;
    font-size: 0.625rem;
  }

  .code-block-enhanced pre[class*="language-"] {
    padding: 2.5rem 0.75rem 0.75rem;
  }
}
/* Utilities */
/* Color Utility Classes */
.text-blue-primary {
  color: var(--color-blue-primary);
}
.text-blue-secondary {
  color: var(--color-blue-secondary);
}
.text-blue-accent {
  color: var(--color-blue-accent);
}
.text-purple-primary {
  color: var(--color-purple-primary);
}
.text-purple-secondary {
  color: var(--color-purple-secondary);
}
.text-green-primary {
  color: var(--color-green-primary);
}
.text-green-secondary {
  color: var(--color-green-secondary);
}
.text-orange-primary {
  color: var(--color-orange-primary);
}
.text-orange-secondary {
  color: var(--color-orange-secondary);
}
.bg-blue-primary {
  background-color: var(--color-blue-primary);
}
.bg-blue-secondary {
  background-color: var(--color-blue-secondary);
}
.bg-purple-primary {
  background-color: var(--color-purple-primary);
}
.bg-purple-secondary {
  background-color: var(--color-purple-secondary);
}
.bg-green-primary {
  background-color: var(--color-green-primary);
}
.bg-green-secondary {
  background-color: var(--color-green-secondary);
}
.bg-orange-primary {
  background-color: var(--color-orange-primary);
}
.bg-orange-secondary {
  background-color: var(--color-orange-secondary);
}
.border-blue-primary {
  border-color: var(--color-blue-primary);
}
.border-blue-secondary {
  border-color: var(--color-blue-secondary);
}
.border-purple-primary {
  border-color: var(--color-purple-primary);
}
.border-purple-secondary {
  border-color: var(--color-purple-secondary);
}
.border-green-primary {
  border-color: var(--color-green-primary);
}
.border-green-secondary {
  border-color: var(--color-green-secondary);
}
.border-orange-primary {
  border-color: var(--color-orange-primary);
}
.border-orange-secondary {
  border-color: var(--color-orange-secondary);
}
/* Semantic Color Utility Classes */
.text-user-message {
  color: var(--color-user-message);
}
.text-assistant-message {
  color: var(--color-assistant-message);
}
.text-system-message {
  color: var(--color-system-message);
}
.text-success {
  color: var(--color-success);
}
.text-warning {
  color: var(--color-warning);
}
.text-error {
  color: var(--color-error);
}
.text-info {
  color: var(--color-info);
}
.text-accent {
  color: var(--color-accent);
}
.bg-user-message {
  background-color: var(--color-user-message);
}
.bg-assistant-message {
  background-color: var(--color-assistant-message);
}
.bg-system-message {
  background-color: var(--color-system-message);
}
.bg-success {
  background-color: var(--color-success);
}
.bg-warning {
  background-color: var(--color-warning);
}
.bg-error {
  background-color: var(--color-error);
}
.bg-info {
  background-color: var(--color-info);
}
.bg-accent {
  background-color: var(--color-accent);
}
.border-user-message {
  border-color: var(--color-user-message);
}
.border-assistant-message {
  border-color: var(--color-assistant-message);
}
.border-system-message {
  border-color: var(--color-system-message);
}
.border-success {
  border-color: var(--color-success);
}
.border-warning {
  border-color: var(--color-warning);
}
.border-error {
  border-color: var(--color-error);
}
.border-info {
  border-color: var(--color-info);
}
.border-accent {
  border-color: var(--color-accent);
}
/* Background Tint Utility Classes */
.bg-blue-tint {
  background-color: rgba(56, 189, 248, 0.1);
}
.bg-purple-tint {
  background-color: rgba(139, 92, 246, 0.1);
}
.bg-green-tint {
  background-color: rgba(16, 185, 129, 0.1);
}
.bg-orange-tint {
  background-color: rgba(245, 158, 11, 0.1);
}
/* Layout and Positioning Utilities */
/* This file will contain layout and positioning utility classes */
/* Margin and Padding Utilities */
/* This file will contain margin and padding utility classes */
/* Text Styling Utilities */
/* This file will contain text styling utility classes */
/* Visual Effects Utilities */
/* Colored Shadow Utilities */
.shadow-blue-sm {
  box-shadow:
    0 1px 3px var(--shadow-blue),
    0 1px 2px rgba(0, 0, 0, 0.1);
}
.shadow-blue {
  box-shadow:
    0 4px 6px var(--shadow-blue),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
.shadow-blue-lg {
  box-shadow:
    0 10px 15px var(--shadow-blue),
    0 4px 6px rgba(0, 0, 0, 0.1);
}
.shadow-purple-sm {
  box-shadow:
    0 1px 3px var(--shadow-purple),
    0 1px 2px rgba(0, 0, 0, 0.1);
}
.shadow-purple {
  box-shadow:
    0 4px 6px var(--shadow-purple),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
.shadow-purple-lg {
  box-shadow:
    0 10px 15px var(--shadow-purple),
    0 4px 6px rgba(0, 0, 0, 0.1);
}
.shadow-green-sm {
  box-shadow:
    0 1px 3px var(--shadow-green),
    0 1px 2px rgba(0, 0, 0, 0.1);
}
.shadow-green {
  box-shadow:
    0 4px 6px var(--shadow-green),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
.shadow-green-lg {
  box-shadow:
    0 10px 15px var(--shadow-green),
    0 4px 6px rgba(0, 0, 0, 0.1);
}
.shadow-orange-sm {
  box-shadow:
    0 1px 3px var(--shadow-orange),
    0 1px 2px rgba(0, 0, 0, 0.1);
}
.shadow-orange {
  box-shadow:
    0 4px 6px var(--shadow-orange),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
.shadow-orange-lg {
  box-shadow:
    0 10px 15px var(--shadow-orange),
    0 4px 6px rgba(0, 0, 0, 0.1);
}
.shadow-neutral-sm {
  box-shadow:
    0 1px 3px var(--shadow-neutral),
    0 1px 2px rgba(0, 0, 0, 0.05);
}
.shadow-neutral {
  box-shadow:
    0 4px 6px var(--shadow-neutral),
    0 2px 4px rgba(0, 0, 0, 0.05);
}
.shadow-neutral-lg {
  box-shadow:
    0 10px 15px var(--shadow-neutral),
    0 4px 6px rgba(0, 0, 0, 0.05);
}
/* Colored Glow Utilities */
.glow-blue-sm {
  box-shadow: 0 0 4px var(--glow-blue);
}
.glow-blue {
  box-shadow: 0 0 8px var(--glow-blue);
}
.glow-blue-lg {
  box-shadow: 0 0 16px var(--glow-blue);
}
.glow-purple-sm {
  box-shadow: 0 0 4px var(--glow-purple);
}
.glow-purple {
  box-shadow: 0 0 8px var(--glow-purple);
}
.glow-purple-lg {
  box-shadow: 0 0 16px var(--glow-purple);
}
.glow-green-sm {
  box-shadow: 0 0 4px var(--glow-green);
}
.glow-green {
  box-shadow: 0 0 8px var(--glow-green);
}
.glow-green-lg {
  box-shadow: 0 0 16px var(--glow-green);
}
.glow-orange-sm {
  box-shadow: 0 0 4px var(--glow-orange);
}
.glow-orange {
  box-shadow: 0 0 8px var(--glow-orange);
}
.glow-orange-lg {
  box-shadow: 0 0 16px var(--glow-orange);
}
/* Combined Shadow and Glow Effects */
.shadow-glow-blue {
  box-shadow:
    0 4px 6px var(--shadow-blue),
    0 0 8px var(--glow-blue),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
.shadow-glow-purple {
  box-shadow:
    0 4px 6px var(--shadow-purple),
    0 0 8px var(--glow-purple),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
.shadow-glow-green {
  box-shadow:
    0 4px 6px var(--shadow-green),
    0 0 8px var(--glow-green),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
.shadow-glow-orange {
  box-shadow:
    0 4px 6px var(--shadow-orange),
    0 0 8px var(--glow-orange),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
/* Components */
/* Button Component Styles */
.settings-button {
  transition: all 0.2s ease-in-out;
}
/* Theme buttons - smaller size */
#settingsDarkBtn,
#settingsLightBtn {
  padding: 0.5rem 1rem !important; /* Smaller padding: py-2 px-4 -> py-2 px-4 but smaller */
  font-size: 0.875rem !important; /* Smaller font size */
  min-height: 2.5rem; /* Consistent height */
}
/* General button size improvements */
.settings-button.ui {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}
.settings-button.api {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}
/* Save Settings button - smaller size */
button:has(i.fa-save),
button[class*="Save"],
#saveSettingsBtn {
  padding: 0.75rem 1.5rem !important;
  font-size: 0.875rem !important;
}
/* Expand button - smaller size */
#expandSystemPromptBtn {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
}
/* Copy button - smaller size */
#copyFilePromptBtn {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
}
/* Prompt Library button - smaller size */
#promptLibraryBtn {
  padding: 0.5rem 1rem !important;
  font-size: 0.75rem !important;
}
.settings-button.api {
  border-color: var(--color-api-settings);
  color: var(--color-api-settings);
}
.settings-button.api:hover {
  background-color: rgba(56, 189, 248, 0.1);
  border-color: var(--color-blue-light);
  color: var(--color-blue-light);
}
.settings-button.ui {
  border-color: var(--color-ui-settings);
  color: var(--color-ui-settings);
}
.settings-button.ui:hover {
  background-color: rgba(139, 92, 246, 0.1);
  border-color: var(--color-purple-light);
  color: var(--color-purple-light);
}
.settings-button.prompt {
  border-color: var(--color-prompt-settings);
  color: var(--color-prompt-settings);
}
.settings-button.prompt:hover {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: var(--color-green-light);
  color: var(--color-green-light);
}
.settings-button.advanced {
  border-color: var(--color-advanced-settings);
  color: var(--color-advanced-settings);
}
.settings-button.advanced:hover {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: var(--color-orange-light);
  color: var(--color-orange-light);
}
.chat-send-button {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
#sendBtn {
  position: absolute !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10;
}
#clearInputBtn {
  position: absolute !important;
  right: 4rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10;
}
#sendBtn,
#clearInputBtn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
.code-copy-btn {
  opacity: 0;
  transition: all 0.2s ease-in-out;
  z-index: 20;
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(51, 51, 51, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}
pre:hover .code-copy-btn {
  opacity: 0.8;
}
.code-copy-btn:hover {
  opacity: 1 !important;
  transform: scale(1.05);
  background-color: rgba(51, 51, 51, 0.9);
}
.code-copy-btn:active {
  transform: scale(0.95);
}
.code-copy-btn-enhanced {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(56, 189, 248, 0.3);
  background-color: rgba(56, 189, 248, 0.1);
  color: var(--color-blue-primary);
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease-in-out;
  z-index: 10;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  gap: 0.375rem;
}
.code-copy-btn-enhanced i {
  font-size: 0.625rem;
}
.code-block-enhanced:hover .code-copy-btn-enhanced {
  opacity: 0.8;
}
.code-copy-btn-enhanced:hover {
  opacity: 1 !important;
  background-color: rgba(56, 189, 248, 0.2);
  border-color: rgba(56, 189, 248, 0.5);
  color: var(--color-blue-light);
  transform: scale(1.05);
}
.code-copy-btn-enhanced:active {
  transform: scale(0.95);
  background-color: rgba(56, 189, 248, 0.3);
}
.code-copy-btn-enhanced.copied {
  background-color: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
  color: var(--color-success);
}
.code-copy-btn-enhanced.copied:hover {
  background-color: rgba(16, 185, 129, 0.3);
  border-color: rgba(16, 185, 129, 0.6);
  color: var(--color-green-light);
}
.code-copy-btn-enhanced.error {
  background-color: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  color: var(--color-error);
}
.code-copy-btn-enhanced.error:hover {
  background-color: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.6);
  color: #f87171;
}
.btn-enhanced {
  position: relative;
  transition: all 0.2s ease-in-out;
  transform-origin: center;
  overflow: hidden;
}
.btn-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease-in-out;
  z-index: 1;
}
.btn-enhanced:hover::before {
  left: 100%;
}
.btn-blue-enhanced {
  background: linear-gradient(135deg, var(--color-blue-primary), var(--color-blue-secondary));
  color: white;
  border: 1px solid var(--color-blue-primary);
  transition: all 0.2s ease-in-out;
}
.btn-blue-enhanced:hover {
  background: linear-gradient(135deg, var(--color-blue-light), var(--color-blue-primary));
  border-color: var(--color-blue-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(56, 189, 248, 0.3);
}
.btn-blue-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(56, 189, 248, 0.4);
}
.btn-purple-enhanced {
  background: linear-gradient(135deg, var(--color-purple-primary), var(--color-purple-secondary));
  color: white;
  border: 1px solid var(--color-purple-primary);
  transition: all 0.2s ease-in-out;
}
.btn-purple-enhanced:hover {
  background: linear-gradient(135deg, var(--color-purple-light), var(--color-purple-primary));
  border-color: var(--color-purple-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}
.btn-purple-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.4);
}
.btn-green-enhanced {
  background: linear-gradient(135deg, var(--color-green-primary), var(--color-green-secondary));
  color: white;
  border: 1px solid var(--color-green-primary);
  transition: all 0.2s ease-in-out;
}
.btn-green-enhanced:hover {
  background: linear-gradient(135deg, var(--color-green-light), var(--color-green-primary));
  border-color: var(--color-green-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}
.btn-green-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.4);
}
.btn-orange-enhanced {
  background: linear-gradient(135deg, var(--color-orange-primary), var(--color-orange-secondary));
  color: white;
  border: 1px solid var(--color-orange-primary);
  transition: all 0.2s ease-in-out;
}
.btn-orange-enhanced:hover {
  background: linear-gradient(135deg, var(--color-orange-light), var(--color-orange-primary));
  border-color: var(--color-orange-light);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}
.btn-orange-enhanced:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(245, 158, 11, 0.4);
}
.btn-bounce {
  transition:
    transform var(--animation-duration-normal) var(--animation-ease-bounce), color var(--animation-duration-fast) var(--animation-ease-out), background-color var(--animation-duration-fast) var(--animation-ease-out), border-color var(--animation-duration-fast) var(--animation-ease-out), box-shadow var(--animation-duration-fast) var(--animation-ease-out);
}
.btn-bounce:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.btn-bounce:active {
  transform: scale(0.98);
  transition-duration: var(--animation-duration-fast);
}
.btn-spring {
  transition:
    transform var(--animation-duration-normal) var(--animation-ease-spring), color var(--animation-duration-fast) var(--animation-ease-out), background-color var(--animation-duration-fast) var(--animation-ease-out), border-color var(--animation-duration-fast) var(--animation-ease-out);
}
.btn-spring:hover {
  transform: scale(1.08);
}
.btn-spring:active {
  transform: scale(0.95);
  transition-duration: var(--animation-duration-fast);
}
.btn-scale {
  transition:
    transform var(--animation-duration-normal) var(--animation-ease-out), color var(--animation-duration-fast) var(--animation-ease-out), background-color var(--animation-duration-fast) var(--animation-ease-out), border-color var(--animation-duration-fast) var(--animation-ease-out);
}
.btn-scale:hover {
  transform: scale(1.02);
}
.btn-scale:active {
  transform: scale(0.98);
}
.btn-blue-animated {
  color: var(--color-blue-primary);
  border-color: var(--color-blue-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}
.btn-blue-animated:hover {
  color: var(--color-blue-light);
  border-color: var(--color-blue-light);
  background-color: rgba(56, 189, 248, 0.1);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
  transform: translateY(-1px);
}
.btn-purple-animated {
  color: var(--color-purple-primary);
  border-color: var(--color-purple-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}
.btn-purple-animated:hover {
  color: var(--color-purple-light);
  border-color: var(--color-purple-light);
  background-color: rgba(139, 92, 246, 0.1);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}
.btn-green-animated {
  color: var(--color-green-primary);
  border-color: var(--color-green-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}
.btn-green-animated:hover {
  color: var(--color-green-light);
  border-color: var(--color-green-light);
  background-color: rgba(16, 185, 129, 0.1);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  transform: translateY(-1px);
}
.btn-orange-animated {
  color: var(--color-orange-primary);
  border-color: var(--color-orange-primary);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}
.btn-orange-animated:hover {
  color: var(--color-orange-light);
  border-color: var(--color-orange-light);
  background-color: rgba(245, 158, 11, 0.1);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  transform: translateY(-1px);
}
/* Form Component Styles */
.chat-input-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}
.chat-input-field {
  flex: 1;
  display: flex;
  align-items: center;
}
#userInput {
  padding-right: 5.5rem !important; /* Space for both clear and send buttons - optimized spacing */
}
.input-container-relative {
  position: relative;
  display: flex;
  align-items: center;
}
.input-container-relative {
  min-height: 3rem; /* Ensure consistent height */
}
#userInput {
  min-height: 3rem !important;
  line-height: 1.5 !important;
}
.settings-input.api:focus {
  border-color: var(--color-api-settings);
  box-shadow: 0 0 0 1px var(--color-api-settings);
}
.settings-input.ui:focus {
  border-color: var(--color-ui-settings);
  box-shadow: 0 0 0 1px var(--color-ui-settings);
}
.settings-input.prompt:focus {
  border-color: var(--color-prompt-settings);
  box-shadow: 0 0 0 1px var(--color-prompt-settings);
}
.settings-input.advanced:focus {
  border-color: var(--color-advanced-settings);
  box-shadow: 0 0 0 1px var(--color-advanced-settings);
}
.input-enhanced {
  transition: all 0.2s ease-in-out;
  border: 2px solid transparent;
}
.input-enhanced:focus {
  transform: scale(1.01);
}
.input-enhanced.input-valid {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}
.input-enhanced.input-invalid {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
.input-enhanced.input-warning {
  border-color: var(--color-warning);
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}
/* Range Slider Improvements */
input[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  background: transparent;
  cursor: pointer;
}
/* Slider Track */
input[type="range"]::-webkit-slider-track {
  background: #e5e7eb !important; /* Light gray for light mode */
  height: 8px;
  border-radius: 4px;
}
html.dark input[type="range"]::-webkit-slider-track {
  background: #4b5563 !important; /* Better contrast for dark mode */
}
/* Slider Thumb */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--color-blue-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
html.dark input[type="range"]::-webkit-slider-thumb {
  border: 2px solid #000;
  background: var(--color-blue-primary);
}
/* Firefox */
input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 8px;
  border-radius: 4px;
  border: none;
}
html.dark input[type="range"]::-moz-range-track {
  background: #4b5563;
}
input[type="range"]::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--color-blue-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
html.dark input[type="range"]::-moz-range-thumb {
  border: 2px solid #000;
}
/* Checkbox Improvements */
input[type="checkbox"] {
  accent-color: var(--color-blue-primary);
}
html.dark input[type="checkbox"] {
  accent-color: var(--color-blue-primary);
  background-color: #374151 !important; /* Better background for dark mode */
  border-color: #6b7280 !important; /* Better border for dark mode */
}
html.dark input[type="checkbox"]:checked {
  background-color: var(--color-blue-primary) !important;
  border-color: var(--color-blue-primary) !important;
}
/* Select Dropdown Improvements */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}
html.dark select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}
/* Modal Component Styles */
#promptLibraryModal {
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.modal-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.modal-overlay,
#paramsPanel {
  z-index: 1000 !important;
}
#systemPromptEditorModal {
  z-index: 1100 !important;
}
#promptLibraryModal {
  z-index: 1200 !important;
}
.modal-content,
#paramsPanel > div,
#systemPromptEditorModal > div,
#promptLibraryModal > div {
  position: relative;
  z-index: 10;
}
.modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 1rem;
  z-index: 1000;
}
.modal-content {
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
  margin: auto;
  z-index: 10;
}
.modal-open {
  overflow: hidden;
}
#paramsPanel:not(.invisible),
#systemPromptEditorModal:not(.invisible),
#promptLibraryModal:not(.invisible) {
  opacity: 1 !important;
  transform: scale(1) !important;
  pointer-events: auto !important;
}
.modal-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.modal-enhanced.modal-enter {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}
.modal-enhanced.modal-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
}
.modal-enhanced.modal-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}
.modal-enhanced.modal-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}
/* Message Component Styles */
.message-stream {
  white-space: pre-wrap;
  word-wrap: break-word;
}
.message-user {
  border-left: 4px solid var(--color-user-message);
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.05) 0%, rgba(14, 165, 233, 0.08) 100%);
  position: relative;
}
/* Removed duplicate user icon - using JavaScript-generated icon instead */
.message-assistant {
  border-left: 4px solid var(--color-assistant-message);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(168, 85, 247, 0.08) 100%);
  position: relative;
}
/* Removed duplicate assistant icon - using JavaScript-generated icon instead */
.message-system {
  border-left: 4px solid var(--color-system-message);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.08) 100%);
  position: relative;
}
/* Removed duplicate system icon - using JavaScript-generated icon instead */
.message-user:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.08) 0%, rgba(14, 165, 233, 0.12) 100%);
  border-left-color: var(--color-blue-light);
  transition: all 0.2s ease-in-out;
}
.message-assistant:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(168, 85, 247, 0.12) 100%);
  border-left-color: var(--color-purple-light);
  transition: all 0.2s ease-in-out;
}
.message-system:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.12) 100%);
  border-left-color: var(--color-green-light);
  transition: all 0.2s ease-in-out;
}
/* Removed hover effects for ::before elements since they no longer exist */
.message-role-user {
  border-left: 4px solid var(--color-user-message);
  background-color: rgba(56, 189, 248, 0.05);
}
.message-role-assistant {
  border-left: 4px solid var(--color-assistant-message);
  background-color: rgba(139, 92, 246, 0.05);
}
.message-role-system {
  border-left: 4px solid var(--color-system-message);
  background-color: rgba(16, 185, 129, 0.05);
}
.message-icon-user {
  color: var(--color-user-message);
}
.message-icon-assistant {
  color: var(--color-assistant-message);
}
.message-icon-system {
  color: var(--color-system-message);
}
/* Settings Component Styles */
.settings-api {
  color: var(--color-api-settings);
  border-color: var(--color-api-settings);
}
.settings-ui {
  color: var(--color-ui-settings);
  border-color: var(--color-ui-settings);
}
.settings-prompt {
  color: var(--color-prompt-settings);
  border-color: var(--color-prompt-settings);
}
.settings-advanced {
  color: var(--color-advanced-settings);
  border-color: var(--color-advanced-settings);
}
.settings-section-header {
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  border-left: 3px solid;
  transition: all 0.2s ease-in-out;
  display: block;
  width: 100%;
  position: relative;
}
.settings-section-header .flex {
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}
.settings-section-header label {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
}
/* Improved form element spacing within settings sections */
.settings-section-header > div {
  margin-bottom: 1rem;
}
.settings-section-header > div:last-child {
  margin-bottom: 0;
}
/* Better alignment for input groups */
.settings-section-header .relative {
  display: flex;
  align-items: center;
  position: relative;
}
/* Consistent button alignment */
.settings-section-header button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
.settings-section-header.api {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.08) 0%, rgba(14, 165, 233, 0.12) 100%);
  border-left-color: var(--color-api-settings);
  color: var(--color-api-settings);
  box-shadow: 0 1px 3px rgba(56, 189, 248, 0.1);
}
.settings-section-header.ui {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(168, 85, 247, 0.12) 100%);
  border-left-color: var(--color-ui-settings);
  color: var(--color-ui-settings);
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.1);
}
.settings-section-header.prompt {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(5, 150, 105, 0.12) 100%);
  border-left-color: var(--color-prompt-settings);
  color: var(--color-prompt-settings);
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.1);
}
.settings-section-header.advanced {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(217, 119, 6, 0.12) 100%);
  border-left-color: var(--color-advanced-settings);
  color: var(--color-advanced-settings);
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.1);
}
/* Subtle hover effects for better balance */
.settings-section-header.api:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.12) 0%, rgba(14, 165, 233, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(56, 189, 248, 0.15);
}
.settings-section-header.ui:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.12) 0%, rgba(168, 85, 247, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
}
.settings-section-header.prompt:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(5, 150, 105, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}
.settings-section-header.advanced:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.12) 0%, rgba(217, 119, 6, 0.18) 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.15);
}
.settings-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
  transition: color 0.2s ease-in-out;
}
.settings-icon.api {
  color: var(--color-api-settings);
}
.settings-icon.ui {
  color: var(--color-ui-settings);
}
.settings-icon.prompt {
  color: var(--color-prompt-settings);
}
.settings-icon.advanced {
  color: var(--color-advanced-settings);
}
.settings-divider {
  height: 1px;
  margin: 1.5rem 0;
  border: none;
  background: linear-gradient(90deg, transparent 0%, currentcolor 50%, transparent 100%);
  opacity: 0.3;
}
.settings-divider.api {
  color: var(--color-api-settings);
}
.settings-divider.ui {
  color: var(--color-ui-settings);
}
.settings-divider.prompt {
  color: var(--color-prompt-settings);
}
.settings-divider.advanced {
  color: var(--color-advanced-settings);
}
.settings-section-header:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
/* Dark theme adjustments for better balance */
html.dark .settings-section-header.api {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.06) 0%, rgba(14, 165, 233, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(56, 189, 248, 0.08);
}
html.dark .settings-section-header.ui {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.06) 0%, rgba(168, 85, 247, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.08);
}
html.dark .settings-section-header.prompt {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.06) 0%, rgba(5, 150, 105, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.08);
}
html.dark .settings-section-header.advanced {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.06) 0%, rgba(217, 119, 6, 0.1) 100%);
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.08);
}
html.dark .settings-section-header:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
}
html.dark .settings-section-header.api:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.1) 0%, rgba(14, 165, 233, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(56, 189, 248, 0.12);
}
html.dark .settings-section-header.ui:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.12);
}
html.dark .settings-section-header.prompt:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.12);
}
html.dark .settings-section-header.advanced:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.12);
}
.settings-section-header + .settings-section-header {
  margin-top: 1.5rem;
}
/* Modal-specific settings improvements - Compact Design */
#paramsPanel .settings-section-header {
  padding: 0.75rem;
  margin-bottom: 1rem;
}
#paramsPanel .settings-section-header h3 {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: 600;
}
#paramsPanel .settings-section-header h4 {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: 500;
}
#paramsPanel .settings-section-header label {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
  font-weight: 500;
}
/* Compact spacing for form elements in modal */
#paramsPanel .mb-3 {
  margin-bottom: 0.75rem;
}
#paramsPanel .mb-4 {
  margin-bottom: 0.75rem;
}
#paramsPanel .space-y-3 > * + * {
  margin-top: 0.75rem;
}
#paramsPanel .space-y-4 > * + * {
  margin-top: 0.75rem;
}
/* Improved input field alignment */
#paramsPanel input,
#paramsPanel select,
#paramsPanel textarea {
  width: 100%;
  display: block;
}
/* Better button alignment */
#paramsPanel .flex {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}
#paramsPanel .flex.justify-between {
  justify-content: space-between;
}
#paramsPanel .flex.items-center {
  align-items: center;
}
/* Range slider improvements */
#paramsPanel input[type="range"] {
  width: 100%;
  margin: 0.5rem 0;
}
/* Checkbox alignment */
#paramsPanel .flex.items-center input[type="checkbox"] {
  margin-right: 0.5rem;
  flex-shrink: 0;
}
/* Button group improvements */
#paramsPanel .flex.space-x-4 button {
  flex: 1;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* Input field with button alignment */
#paramsPanel .relative input {
  padding-right: 3rem;
}
#paramsPanel .relative button {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}
/* Status indicator positioning */
#paramsPanel .status-dot {
  position: absolute;
  right: 2.5rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 5;
}
/* Action buttons at bottom of sections */
#paramsPanel .settings-section-header .flex.justify-between {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}
html.dark #paramsPanel .settings-section-header .flex.justify-between {
  border-top-color: rgba(255, 255, 255, 0.05);
}
/* Consistent spacing for all form elements */
#paramsPanel .settings-section-header > * {
  margin-bottom: 1rem;
}
#paramsPanel .settings-section-header > *:last-child {
  margin-bottom: 0;
}
/* Perfect alignment for expand button */
#paramsPanel #expandSystemPromptBtn {
  margin-left: auto;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
  display: inline-flex;
  align-items: center;
}
/* Better alignment for prompt library button and status */
#paramsPanel .flex.justify-between {
  align-items: center;
  min-height: 2rem;
}
/* Improved divider styling */
#paramsPanel .settings-divider {
  margin: 1.5rem 0;
  opacity: 0.4;
}
/* Compact modal content spacing */
#paramsPanel .p-6 {
  padding: 1rem;
}
#paramsPanel .space-y-6 {
  gap: 1rem;
}
/* Compact button heights */
#paramsPanel button {
  min-height: 2.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
/* Compact range slider label alignment */
#paramsPanel .flex.justify-between.items-center {
  margin-bottom: 0.25rem;
}
/* Compact save button section */
#paramsPanel .pt-4.border-t {
  padding-top: 1rem;
  margin-top: 1rem;
}
/* Reduce divider spacing */
#paramsPanel .settings-divider {
  margin: 1rem 0;
}
/* Icon spacing consistency */
#paramsPanel i.fas {
  margin-right: 0.5rem;
  width: 1rem;
  text-align: center;
}
/* Ultra-compact design for maximum efficiency */
#paramsPanel .settings-section-header {
  border-radius: 0.375rem;
  border-left-width: 3px;
}
#paramsPanel .settings-section-header.mt-4 {
  margin-top: 0.75rem;
}
/* Compact input styling */
#paramsPanel input,
#paramsPanel select,
#paramsPanel textarea {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}
#paramsPanel input[type="range"] {
  height: 0.375rem;
  margin: 0.25rem 0;
}
/* Compact checkbox styling */
#paramsPanel input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
}
/* Compact button styling */
#paramsPanel button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
}
#paramsPanel button.rounded-full {
  border-radius: 9999px;
}
/* Reduce overall modal padding for compactness */
#paramsPanel .max-w-md {
  max-width: 26rem;
}
/* Compact status indicators */
#paramsPanel .status-dot {
  width: 0.5rem;
  height: 0.5rem;
}
.settings-section-header.prompt .flex.justify-between.items-center {
  flex-direction: column !important;
  align-items: flex-start !important;
  gap: 0.5rem;
}
.settings-section-header.prompt .flex.justify-between.items-center > label {
  width: 100% !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}
.settings-section-header.prompt .flex.justify-between.items-center > button {
  align-self: flex-start !important;
  margin-top: 0 !important;
}
/* Expanded Settings Panel Styles */
#paramsPanel .max-w-md {
  transition: max-width 0.3s ease-in-out, width 0.3s ease-in-out;
}
/* Expanded settings panel */
#paramsPanel .max-w-2xl {
  max-width: 42rem !important;
  width: 90vw !important;
}
/* Enhanced textarea in expanded mode */
#paramsPanel[data-expanded="true"] #systemPromptInput,
#paramsPanel .max-w-2xl #systemPromptInput {
  min-height: 200px !important;
  resize: vertical !important;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
}
/* Smooth transition for expand/collapse */
#paramsPanel .bg-white,
#paramsPanel .dark\\:bg-sora-gray-dark {
  transition: max-width 0.3s ease-in-out, width 0.3s ease-in-out;
}
/* Better spacing in expanded mode */
#paramsPanel[data-expanded="true"] .settings-section-header.prompt,
#paramsPanel .max-w-2xl .settings-section-header.prompt {
  padding: 1.5rem !important;
}
/* Expand button state styling */
#expandSystemPromptBtn[data-expanded="true"] {
  background-color: rgba(16, 185, 129, 0.1) !important;
  color: var(--color-prompt-settings) !important;
  border-color: var(--color-prompt-settings) !important;
}
/* Status Indicator Components */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  border: 1px solid transparent;
}
.status-indicator i {
  margin-right: 0.5rem;
  font-size: 0.75rem;
}
.status-connected {
  color: var(--color-connected);
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}
.status-connected i {
  color: var(--color-connected);
  animation: pulse-green 2s infinite;
}
.status-error,
.status-disconnected {
  color: var(--color-error);
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}
.status-error i,
.status-disconnected i {
  color: var(--color-error);
  animation: shake-red 1s infinite;
}
.status-warning {
  color: var(--color-warning);
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}
.status-warning i {
  color: var(--color-warning);
  animation: blink-orange 1.5s infinite;
}
/* Token validation indicators - smaller and less intrusive */
.visual-indicator-alt {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  border: none !important;
  background-color: var(--color-connected) !important;
  opacity: 0.7;
  transition: opacity 0.2s ease-in-out;
}
.visual-indicator-alt:hover {
  opacity: 1;
}
.visual-indicator-alt.status-error {
  background-color: var(--color-error) !important;
}
.visual-indicator-alt.status-warning {
  background-color: var(--color-warning) !important;
}
.status-loading,
.status-connecting {
  color: var(--color-loading);
  background-color: rgba(56, 189, 248, 0.1);
  border-color: rgba(56, 189, 248, 0.2);
}
.status-loading i,
.status-connecting i {
  color: var(--color-loading);
  animation: spin-blue 1s linear infinite;
}
.status-indicator-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
.status-indicator-sm i {
  font-size: 0.625rem;
  margin-right: 0.25rem;
}
.status-indicator-lg {
  padding: 0.5rem 1rem;
  font-size: 1rem;
}
.status-indicator-lg i {
  font-size: 0.875rem;
  margin-right: 0.75rem;
}
.status-indicator-outline {
  background-color: transparent;
  border-width: 1px;
}
.status-indicator-solid {
  border: none;
}
.status-indicator-solid.status-connected {
  background-color: var(--color-connected);
  color: white;
}
.status-indicator-solid.status-error,
.status-indicator-solid.status-disconnected {
  background-color: var(--color-error);
  color: white;
}
.status-indicator-solid.status-warning {
  background-color: var(--color-warning);
  color: white;
}
.status-indicator-solid.status-loading,
.status-indicator-solid.status-connecting {
  background-color: var(--color-loading);
  color: white;
}
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 2px solid transparent;
  transition: all 0.3s ease-in-out;
}
.status-badge.status-connected {
  background-color: var(--color-connected);
  border-color: rgba(16, 185, 129, 0.3);
  animation: pulse-green-badge 2s infinite;
}
.status-badge.status-error,
.status-badge.status-disconnected {
  background-color: var(--color-error);
  border-color: rgba(239, 68, 68, 0.3);
  animation: pulse-red-badge 1s infinite;
}
.status-badge.status-warning {
  background-color: var(--color-warning);
  border-color: rgba(245, 158, 11, 0.3);
  animation: pulse-orange-badge 1.5s infinite;
}
.status-badge.status-loading,
.status-badge.status-connecting {
  background-color: var(--color-loading);
  border-color: rgba(56, 189, 248, 0.3);
  animation: pulse-blue-badge 1s infinite;
}
.status-dot {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease-in-out;
}
.status-dot.status-connected {
  background-color: var(--color-connected);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  animation: glow-green 2s infinite;
}
.status-dot.status-error,
.status-dot.status-disconnected {
  background-color: var(--color-error);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  animation: glow-red 1s infinite;
}
.status-dot.status-warning {
  background-color: var(--color-warning);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: glow-orange 1.5s infinite;
}
.status-dot.status-loading,
.status-dot.status-connecting {
  background-color: var(--color-loading);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
  animation: glow-blue 1s infinite;
}
.status-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
.status-transition.status-changing {
  transform: scale(1.05);
  filter: brightness(1.1);
}
.status-text-connected {
  color: var(--color-connected);
}
.status-text-error,
.status-text-disconnected {
  color: var(--color-error);
}
.status-text-warning {
  color: var(--color-warning);
}
.status-text-loading,
.status-text-connecting {
  color: var(--color-loading);
}
.status-bg-connected {
  background-color: rgba(16, 185, 129, 0.1);
}
.status-bg-error,
.status-bg-disconnected {
  background-color: rgba(239, 68, 68, 0.1);
}
.status-bg-warning {
  background-color: rgba(245, 158, 11, 0.1);
}
.status-bg-loading,
.status-bg-connecting {
  background-color: rgba(56, 189, 248, 0.1);
}
.status-border-connected {
  border-color: rgba(16, 185, 129, 0.3);
}
.status-border-error,
.status-border-disconnected {
  border-color: rgba(239, 68, 68, 0.3);
}
.status-border-warning {
  border-color: rgba(245, 158, 11, 0.3);
}
.status-border-loading,
.status-border-connecting {
  border-color: rgba(56, 189, 248, 0.3);
}
/* Code Block Component Styles */
pre[class*="language-"] {
  margin: 1em 0;
  border-radius: 0.5rem;
  max-height: 500px;
  overflow: auto;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}
html:not(.dark) pre[class*="language-"] {
  background: #2d2d2d;
  color: #ccc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
html.dark pre[class*="language-"] {
  background: #1a1a1a;
  border: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
code[class*="language-"],
pre[class*="language-"] {
  color: #f8f8f2;
  background: none;
  text-shadow: 0 1px rgba(0, 0, 0, 0.3);
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.3;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  hyphens: none;
}
pre[class*="language-"] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
  white-space: pre;
  line-height: 1.3;
}
:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #272822;
}
:not(pre) > code[class*="language-"] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #8292a2;
}
.token.punctuation {
  color: #f8f8f2;
}
.token.namespace {
  opacity: 0.7;
}
.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
  color: #f92672;
}
.token.boolean,
.token.number {
  color: #ae81ff;
}
.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #a6e22e;
}
.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
  color: #f8f8f2;
}
.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
  color: #e6db74;
}
.token.keyword {
  color: #66d9ef;
}
.token.regex,
.token.important {
  color: #fd971f;
}
.token.important,
.token.bold {
  font-weight: bold;
}
.token.italic {
  font-style: italic;
}
.token.entity {
  cursor: help;
}
.line-numbers .line-numbers-rows {
  border-right: 1px solid #999;
  padding-right: 0.8em;
}
:not(pre) > code {
  background: rgba(135, 131, 120, 0.15);
  color: #eb5757;
  padding: 0.1em 0.4em;
  border-radius: 0.3em;
  white-space: normal;
  font-size: 0.9em;
}
html.dark :not(pre) > code {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
}
.code-title {
  background: #444;
  color: white;
  font-family: monospace;
  padding: 0.5em 1em;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  font-size: 0.8em;
  border-bottom: 1px solid #555;
}
html.dark .code-title {
  background: #333;
  border-bottom: 1px solid #444;
}
pre[class*="language-"] {
  position: relative;
}
pre[class*="language-"]::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
pre[class*="language-"]::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
pre[class*="language-"]::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}
pre[class*="language-"]::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
.code-block-enhanced {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease-in-out;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.5) 100%);
}
html:not(.dark) .code-block-enhanced {
  border-color: rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
}
.code-block-enhanced:hover {
  border-color: rgba(56, 189, 248, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}
html:not(.dark) .code-block-enhanced:hover {
  border-color: rgba(56, 189, 248, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.code-language-badge {
  position: absolute;
  top: 0.75rem;
  right: 3.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  z-index: 10;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
.code-language-badge.lang-javascript {
  background-color: rgba(247, 223, 30, 0.15);
  color: var(--color-lang-javascript);
  border-color: rgba(247, 223, 30, 0.3);
}
.code-language-badge.lang-python {
  background-color: rgba(55, 118, 171, 0.15);
  color: var(--color-lang-python);
  border-color: rgba(55, 118, 171, 0.3);
}
.code-language-badge.lang-html {
  background-color: rgba(227, 79, 38, 0.15);
  color: var(--color-lang-html);
  border-color: rgba(227, 79, 38, 0.3);
}
.code-language-badge.lang-css {
  background-color: rgba(21, 114, 182, 0.15);
  color: var(--color-lang-css);
  border-color: rgba(21, 114, 182, 0.3);
}
.code-language-badge.lang-java {
  background-color: rgba(237, 139, 0, 0.15);
  color: var(--color-lang-java);
  border-color: rgba(237, 139, 0, 0.3);
}
.code-language-badge.lang-cpp,
.code-language-badge.lang-c {
  background-color: rgba(0, 89, 156, 0.15);
  color: var(--color-lang-cpp);
  border-color: rgba(0, 89, 156, 0.3);
}
.code-language-badge.lang-csharp {
  background-color: rgba(35, 145, 32, 0.15);
  color: var(--color-lang-csharp);
  border-color: rgba(35, 145, 32, 0.3);
}
.code-language-badge.lang-php {
  background-color: rgba(119, 123, 180, 0.15);
  color: var(--color-lang-php);
  border-color: rgba(119, 123, 180, 0.3);
}
.code-language-badge.lang-ruby {
  background-color: rgba(204, 52, 45, 0.15);
  color: var(--color-lang-ruby);
  border-color: rgba(204, 52, 45, 0.3);
}
.code-language-badge.lang-go {
  background-color: rgba(0, 173, 216, 0.15);
  color: var(--color-lang-go);
  border-color: rgba(0, 173, 216, 0.3);
}
.code-language-badge.lang-rust {
  background-color: rgba(0, 0, 0, 0.15);
  color: #ce422b;
  border-color: rgba(206, 66, 43, 0.3);
}
.code-language-badge.lang-typescript {
  background-color: rgba(49, 120, 198, 0.15);
  color: var(--color-lang-typescript);
  border-color: rgba(49, 120, 198, 0.3);
}
.code-language-badge.lang-json {
  background-color: rgba(56, 189, 248, 0.15);
  color: var(--color-blue-primary);
  border-color: rgba(56, 189, 248, 0.3);
}
.code-language-badge.lang-xml {
  background-color: rgba(255, 102, 0, 0.15);
  color: var(--color-lang-xml);
  border-color: rgba(255, 102, 0, 0.3);
}
.code-language-badge.lang-sql {
  background-color: rgba(51, 103, 145, 0.15);
  color: var(--color-lang-sql);
  border-color: rgba(51, 103, 145, 0.3);
}
.code-language-badge.lang-bash,
.code-language-badge.lang-shell {
  background-color: rgba(78, 170, 37, 0.15);
  color: var(--color-lang-bash);
  border-color: rgba(78, 170, 37, 0.3);
}
.code-language-badge.lang-powershell {
  background-color: rgba(1, 36, 86, 0.15);
  color: #5391fe;
  border-color: rgba(83, 145, 254, 0.3);
}
.code-language-badge.lang-default {
  background-color: rgba(56, 189, 248, 0.15);
  color: var(--color-lang-default);
  border-color: rgba(56, 189, 248, 0.3);
}
.code-language-badge:hover {
  transform: scale(1.05);
  opacity: 0.9;
}
.code-block-enhanced.lang-javascript {
  border-left: 4px solid var(--color-lang-javascript);
}
.code-block-enhanced.lang-python {
  border-left: 4px solid var(--color-lang-python);
}
.code-block-enhanced.lang-html {
  border-left: 4px solid var(--color-lang-html);
}
.code-block-enhanced.lang-css {
  border-left: 4px solid var(--color-lang-css);
}
.code-block-enhanced.lang-java {
  border-left: 4px solid var(--color-lang-java);
}
.code-block-enhanced.lang-cpp,
.code-block-enhanced.lang-c {
  border-left: 4px solid var(--color-lang-cpp);
}
.code-block-enhanced.lang-csharp {
  border-left: 4px solid var(--color-lang-csharp);
}
.code-block-enhanced.lang-php {
  border-left: 4px solid var(--color-lang-php);
}
.code-block-enhanced.lang-ruby {
  border-left: 4px solid var(--color-lang-ruby);
}
.code-block-enhanced.lang-go {
  border-left: 4px solid var(--color-lang-go);
}
.code-block-enhanced.lang-rust {
  border-left: 4px solid #ce422b;
}
.code-block-enhanced.lang-typescript {
  border-left: 4px solid var(--color-lang-typescript);
}
.code-block-enhanced.lang-json {
  border-left: 4px solid var(--color-blue-primary);
}
.code-block-enhanced.lang-xml {
  border-left: 4px solid var(--color-lang-xml);
}
.code-block-enhanced.lang-sql {
  border-left: 4px solid var(--color-lang-sql);
}
.code-block-enhanced.lang-bash,
.code-block-enhanced.lang-shell {
  border-left: 4px solid var(--color-lang-bash);
}
.code-block-enhanced.lang-powershell {
  border-left: 4px solid #5391fe;
}
.code-block-enhanced.lang-default {
  border-left: 4px solid var(--color-lang-default);
}
.code-block-enhanced pre[class*="language-"]:hover {
  background: rgba(0, 0, 0, 0.1);
}
html:not(.dark) .code-block-enhanced pre[class*="language-"]:hover {
  background: rgba(255, 255, 255, 0.1);
}
.code-block-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rem;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, transparent 100%);
  pointer-events: none;
  z-index: 5;
}
html:not(.dark) .code-block-header {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}
.code-block-header > * {
  pointer-events: auto;
}
.code-block-enhanced pre[class*="language-"] {
  margin: 0;
  padding: 3rem 1rem 1rem;
  border-radius: 0;
  background: transparent;
}
/* Prompt Library Component Styles */
#promptLibraryModal {
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.prompt-category {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}
.prompt-category-builtin {
  color: var(--color-prompt-builtin);
  background-color: rgba(56, 189, 248, 0.1);
  border-color: rgba(56, 189, 248, 0.2);
}
.prompt-category-builtin:hover {
  background-color: rgba(56, 189, 248, 0.15);
  border-color: rgba(56, 189, 248, 0.3);
  color: var(--color-blue-light);
  transform: translateY(-1px);
}
.prompt-category-manual {
  color: var(--color-prompt-manual);
  background-color: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.2);
}
.prompt-category-manual:hover {
  background-color: rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
  color: var(--color-purple-light);
  transform: translateY(-1px);
}
.prompt-category-library {
  color: var(--color-prompt-library);
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}
.prompt-category-library:hover {
  background-color: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
  color: var(--color-green-light);
  transform: translateY(-1px);
}
.prompt-category-custom {
  color: var(--color-prompt-custom);
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}
.prompt-category-custom:hover {
  background-color: rgba(245, 158, 11, 0.15);
  border-color: rgba(245, 158, 11, 0.3);
  color: var(--color-orange-light);
  transform: translateY(-1px);
}
.prompt-category i {
  margin-right: 0.375rem;
  font-size: 0.625rem;
}
.prompt-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  background: transparent;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  position: relative;
}
.prompt-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
html.dark .prompt-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
.prompt-item-selected {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-color: rgba(56, 189, 248, 0.3);
  box-shadow: 0 0 0 1px rgba(56, 189, 248, 0.2);
}
.prompt-item-selected:hover {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%);
  border-color: rgba(56, 189, 248, 0.4);
  box-shadow:
    0 0 0 1px rgba(56, 189, 248, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
}
.prompt-item-active {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
}
.prompt-item-active:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.15) 100%);
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow:
    0 0 0 1px rgba(16, 185, 129, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
}
.prompt-item-modified {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.2);
}
.prompt-item-modified:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.15) 100%);
  border-color: rgba(245, 158, 11, 0.4);
  box-shadow:
    0 0 0 1px rgba(245, 158, 11, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
}
.prompt-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}
.prompt-item-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: inherit;
  margin: 0;
  line-height: 1.25;
}
.prompt-item-category {
  flex-shrink: 0;
  margin-left: 0.5rem;
}
.prompt-item-preview {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  max-height: 2.8rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 0.5rem;
}
html:not(.dark) .prompt-item-preview {
  color: rgba(0, 0, 0, 0.6);
}
.prompt-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.5);
}
html:not(.dark) .prompt-item-footer {
  color: rgba(0, 0, 0, 0.5);
}
.prompt-item-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.prompt-item-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}
.prompt-item:hover .prompt-item-actions {
  opacity: 1;
}
.prompt-item-action {
  padding: 0.25rem;
  border-radius: 0.25rem;
  background: transparent;
  border: none;
  color: inherit;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}
.prompt-item-action:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}
/* Animations */
/* Animation Keyframes */
@keyframes pulse-green {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}
@keyframes shake-red {
  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-2px);
  }

  75% {
    transform: translateX(2px);
  }
}
@keyframes blink-orange {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}
@keyframes spin-blue {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse-green-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}
@keyframes pulse-red-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}
@keyframes pulse-orange-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.15);
    opacity: 0.75;
  }
}
@keyframes pulse-blue-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.3);
    opacity: 0.6;
  }
}
@keyframes glow-green {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.4);
  }
}
@keyframes glow-red {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.4);
  }
}
@keyframes glow-orange {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.4);
  }
}
@keyframes glow-blue {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(56, 189, 248, 0.4);
  }
}
@keyframes copy-success {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}
@keyframes typing {
  0% {
    content: ".";
  }

  33% {
    content: "..";
  }

  66% {
    content: "...";
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
@keyframes promptSourceFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes pulse-blue {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(56, 189, 248, 0.7);
    background-color: var(--color-blue-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(56, 189, 248, 0);
    background-color: var(--color-blue-light);
  }
}
@keyframes pulse-purple {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
    background-color: var(--color-purple-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
    background-color: var(--color-purple-light);
  }
}
@keyframes pulse-green {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    background-color: var(--color-green-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    background-color: var(--color-green-light);
  }
}
@keyframes pulse-orange {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    background-color: var(--color-orange-primary);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
    background-color: var(--color-orange-light);
  }
}
@keyframes loading-sweep {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}
@keyframes gradientPulse {
  0%,
  100% {
    background: linear-gradient(
      90deg,
      var(--color-blue-primary) 0%,
      var(--color-purple-primary) 50%,
      var(--color-green-primary) 100%
    );
    background-size: 200% 100%;
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}
@keyframes rotateGradient {
  0% {
    background: conic-gradient(
      from 0deg,
      var(--color-blue-primary),
      var(--color-purple-primary),
      var(--color-green-primary),
      var(--color-orange-primary),
      var(--color-blue-primary)
    );
  }

  100% {
    background: conic-gradient(
      from 360deg,
      var(--color-blue-primary),
      var(--color-purple-primary),
      var(--color-green-primary),
      var(--color-orange-primary),
      var(--color-blue-primary)
    );
  }
}
@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}
@keyframes typingDot {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }

  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}
@keyframes progressGradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
@keyframes focusRingPulse {
  0% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }
}
@keyframes focusRingPulseBlue {
  0% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(56, 189, 248, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-blue-accent);
  }
}
@keyframes focusRingPulsePurple {
  0% {
    box-shadow: 0 0 0 2px var(--color-purple-primary);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-purple-primary);
  }
}
@keyframes focusRingPulseGreen {
  0% {
    box-shadow: 0 0 0 2px var(--color-green-primary);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3);
  }

  100% {
    box-shadow: 0 0 0 2px var(--color-green-primary);
  }
}
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}
@keyframes glowPulse {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(56, 189, 248, 0.5);
  }

  50% {
    box-shadow: 0 0 25px rgba(56, 189, 248, 0.8);
  }
}
@keyframes glowPulsePurple {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
  }

  50% {
    box-shadow: 0 0 25px rgba(139, 92, 246, 0.8);
  }
}
@keyframes glowPulseGreen {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
  }

  50% {
    box-shadow: 0 0 25px rgba(16, 185, 129, 0.8);
  }
}
@keyframes themeSwitch {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(0.98);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes colorMorph {
  0% {
    filter: hue-rotate(0deg) brightness(1);
  }

  50% {
    filter: hue-rotate(180deg) brightness(1.1);
  }

  100% {
    filter: hue-rotate(360deg) brightness(1);
  }
}
@keyframes progress-shine {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}
/* Transition Classes */
.color-transition {
  transition:
    color var(--animation-duration-normal) var(--animation-ease-in-out), background-color var(--animation-duration-normal) var(--animation-ease-in-out), border-color var(--animation-duration-normal) var(--animation-ease-in-out), box-shadow var(--animation-duration-normal) var(--animation-ease-in-out);
}
.color-transition-fast {
  transition:
    color var(--animation-duration-fast) var(--animation-ease-out), background-color var(--animation-duration-fast) var(--animation-ease-out), border-color var(--animation-duration-fast) var(--animation-ease-out), box-shadow var(--animation-duration-fast) var(--animation-ease-out);
}
.color-transition-slow {
  transition:
    color var(--animation-duration-slow) var(--animation-ease-in-out), background-color var(--animation-duration-slow) var(--animation-ease-in-out), border-color var(--animation-duration-slow) var(--animation-ease-in-out), box-shadow var(--animation-duration-slow) var(--animation-ease-in-out);
}
.theme-transition {
  transition:
    background-color var(--animation-duration-slow) var(--animation-ease-in-out), color var(--animation-duration-slow) var(--animation-ease-in-out), border-color var(--animation-duration-slow) var(--animation-ease-in-out), box-shadow var(--animation-duration-slow) var(--animation-ease-in-out);
}
/* Interaction Animation Classes */
.interactive-element {
  transition: all var(--animation-duration-normal) var(--animation-ease-in-out);
}
.interactive-element:hover {
  transform: translateY(-1px);
}
.interactive-element:active {
  transform: translateY(0);
  transition-duration: var(--animation-duration-fast);
}
.focus-ring-animated {
  position: relative;
  transition: all var(--animation-duration-fast) var(--animation-ease-out);
}
.focus-ring-animated:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-blue-accent);
  animation: focusRingPulse 1s ease-in-out;
}
.focus-ring-blue:focus {
  box-shadow: 0 0 0 2px var(--color-blue-accent);
  animation: focusRingPulseBlue 1s ease-in-out;
}
.focus-ring-purple:focus {
  box-shadow: 0 0 0 2px var(--color-purple-primary);
  animation: focusRingPulsePurple 1s ease-in-out;
}
.focus-ring-green:focus {
  box-shadow: 0 0 0 2px var(--color-green-primary);
  animation: focusRingPulseGreen 1s ease-in-out;
}
.hover-glow {
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
}
.hover-glow:hover {
  box-shadow: 0 0 20px rgba(56, 189, 248, 0.3);
  transform: translateY(-2px);
}
.hover-glow-purple:hover {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
}
.hover-glow-green:hover {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
}
.hover-glow-orange:hover {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  transform: translateY(-2px);
}
/* Accessibility */
/* Focus Management Styles */
.focus-blue:focus {
  outline: 2px solid var(--color-focus-blue);
  outline-offset: 2px;
}
.focus-purple:focus {
  outline: 2px solid var(--color-focus-purple);
  outline-offset: 2px;
}
.focus-green:focus {
  outline: 2px solid var(--color-focus-green);
  outline-offset: 2px;
}
.focus-orange:focus {
  outline: 2px solid var(--color-focus-orange);
  outline-offset: 2px;
}
.focus-enhanced:focus {
  outline: 2px solid var(--color-blue-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(56, 189, 248, 0.2);
  border-radius: 0.25rem;
}
.focus-enhanced.focus-purple:focus {
  outline-color: var(--color-purple-primary);
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
}
.focus-enhanced.focus-green:focus {
  outline-color: var(--color-green-primary);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
}
.focus-enhanced.focus-orange:focus {
  outline-color: var(--color-orange-primary);
  box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.2);
}
.keyboard-focusable {
  position: relative;
}
.keyboard-focusable:focus::after {
  content: "";
  position: absolute;
  top: -2px;
  right: -2px;
  bottom: -2px;
  left: -2px;
  border: 2px solid var(--color-blue-primary);
  border-radius: 0.375rem;
  pointer-events: none;
}
/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    /* High contrast color overrides */
    --color-blue-primary: #06f;
    --color-blue-secondary: #0052cc;
    --color-purple-primary: #60c;
    --color-purple-secondary: #5200a3;
    --color-green-primary: #0c6;
    --color-green-secondary: #00a352;
    --color-orange-primary: #f60;
    --color-orange-secondary: #cc5200;
    --color-error: #c00;
  }

  .status-indicator,
  .settings-section-header,
  .code-block-enhanced,
  .message-user,
  .message-assistant,
  .message-system {
    border-width: 2px;
  }

  .status-indicator,
  .settings-section-header h3,
  .code-language-badge {
    font-weight: 700;
  }

  button:focus,
  input:focus,
  select:focus,
  textarea:focus,
  [tabindex]:focus {
    outline: 3px solid currentcolor;
    outline-offset: 2px;
  }

  .focus-enhanced:focus {
    outline-width: 3px;
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(56, 189, 248, 0.4);
  }

  .focus-enhanced.focus-purple:focus {
    box-shadow: 0 0 0 6px rgba(139, 92, 246, 0.4);
  }

  .focus-enhanced.focus-green:focus {
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.4);
  }

  .focus-enhanced.focus-orange:focus {
    box-shadow: 0 0 0 6px rgba(245, 158, 11, 0.4);
  }

  button,
  .button,
  input[type="button"],
  input[type="submit"] {
    border: 2px solid currentcolor;
    font-weight: 700;
  }

  button:hover,
  .button:hover,
  input[type="button"]:hover,
  input[type="submit"]:hover {
    background-color: currentcolor;
    color: white;
  }

  button:focus,
  .button:focus,
  input[type="button"]:focus,
  input[type="submit"]:focus {
    outline: 3px solid currentcolor;
    outline-offset: 3px;
  }

  .text-blue-primary,
  .text-purple-primary,
  .text-green-primary,
  .text-orange-primary {
    filter: contrast(1.2);
  }

  .bg-blue-tint,
  .bg-purple-tint,
  .bg-green-tint,
  .bg-orange-tint {
    opacity: 0.2;
    border: 1px solid currentcolor;
  }
}
/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .code-block-enhanced:hover,
  .settings-section-header:hover,
  .code-copy-btn-enhanced:hover,
  .code-language-badge:hover {
    transform: none;
  }

  .status-indicator i,
  .status-badge,
  .status-dot {
    animation: none;
  }

  .status-indicator.status-connected {
    border-left: 4px solid var(--color-connected);
  }

  .status-indicator.status-error,
  .status-indicator.status-disconnected {
    border-left: 4px solid var(--color-error);
  }

  .status-indicator.status-warning {
    border-left: 4px solid var(--color-warning);
  }

  .status-indicator.status-loading,
  .status-indicator.status-connecting {
    border-left: 4px solid var(--color-loading);
  }

  .btn-enhanced,
  .btn-blue-enhanced,
  .btn-purple-enhanced,
  .btn-green-enhanced,
  .btn-orange-enhanced,
  .interactive-hover,
  .card-enhanced,
  .link-enhanced,
  .toggle-enhanced,
  .input-enhanced {
    transition: none;
    animation: none;
    transform: none;
  }

  .btn-enhanced::before {
    display: none;
  }

  .loading-pulse-blue::after,
  .loading-pulse-purple::after,
  .loading-pulse-green::after,
  .loading-pulse-orange::after {
    display: none;
  }

  .pulse-blue,
  .pulse-purple,
  .pulse-green,
  .pulse-orange {
    animation: none;
  }
}
