/* Nebula AI Interface - Modular CSS Architecture */

/* Main entry point with organized imports */

/* Base Styles */
@import "./base/reset.css";
@import "./base/typography.css";
@import "./base/root.css";

/* Design Tokens */
@import "./tokens/colors.css";
@import "./tokens/spacing.css";
@import "./tokens/shadows.css";
@import "./tokens/animations.css";

/* Layout System */
@import "./layout/containers.css";
@import "./layout/grid.css";
@import "./layout/responsive.css";

/* Utilities */
@import "./utilities/colors.css";
@import "./utilities/layout.css";
@import "./utilities/spacing.css";
@import "./utilities/typography.css";
@import "./utilities/effects.css";

/* Components */
@import "./components/buttons.css";
@import "./components/forms.css";
@import "./components/modals.css";
@import "./components/messages.css";
@import "./components/settings.css";
@import "./components/status.css";
@import "./components/code-blocks.css";
@import "./components/prompts.css";

/* Animations */
@import "./animations/keyframes.css";
@import "./animations/transitions.css";
@import "./animations/interactions.css";

/* Accessibility */
@import "./accessibility/focus.css";
@import "./accessibility/contrast.css";
@import "./accessibility/motion.css";
