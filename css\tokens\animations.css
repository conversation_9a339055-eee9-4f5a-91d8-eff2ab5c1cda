/* Animation Timing Tokens */
:root {
  /* Animation Durations */
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 300ms;
  --animation-duration-slow: 500ms;

  /* Animation Timing Functions */
  --animation-ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --animation-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-ease-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);

  /* Animation Delays */
  --animation-delay-short: 50ms;
  --animation-delay-medium: 100ms;
  --animation-delay-long: 200ms;
}
