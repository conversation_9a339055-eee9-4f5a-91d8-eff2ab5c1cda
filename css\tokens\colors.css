/* Color Palette and Semantic Colors */
:root {
  /* Enhanced Color Palette */
  --color-blue-primary: #38bdf8;
  --color-blue-secondary: #0ea5e9;
  --color-blue-accent: rgb(59, 130, 246);
  --color-blue-light: #7dd3fc;
  --color-blue-dark: #0284c7;
  --color-purple-primary: #8b5cf6;
  --color-purple-secondary: #a855f7;
  --color-purple-light: #c084fc;
  --color-purple-dark: #7c3aed;
  --color-green-primary: #10b981;
  --color-green-secondary: #059669;
  --color-green-light: #34d399;
  --color-green-dark: #047857;
  --color-orange-primary: #f59e0b;
  --color-orange-secondary: #d97706;
  --color-orange-light: #fbbf24;
  --color-orange-dark: #b45309;

  /* Semantic Color Variables */
  --color-user-message: var(--color-blue-primary);
  --color-assistant-message: var(--color-purple-primary);
  --color-system-message: var(--color-green-primary);
  --color-warning: var(--color-orange-primary);
  --color-success: var(--color-green-primary);
  --color-error: #ef4444;
  --color-info: var(--color-blue-primary);
  --color-accent: var(--color-purple-primary);

  /* UI Context Colors */
  --color-api-settings: var(--color-blue-primary);
  --color-ui-settings: var(--color-purple-primary);
  --color-prompt-settings: var(--color-green-primary);
  --color-advanced-settings: var(--color-orange-primary);

  /* Status Colors */
  --color-connected: var(--color-green-primary);
  --color-disconnected: var(--color-error);
  --color-connecting: var(--color-blue-primary);
  --color-loading: var(--color-blue-primary);

  /* Interactive State Colors */
  --color-hover-blue: var(--color-blue-light);
  --color-hover-purple: var(--color-purple-light);
  --color-hover-green: var(--color-green-light);
  --color-hover-orange: var(--color-orange-light);
  --color-focus-blue: var(--color-blue-accent);
  --color-focus-purple: var(--color-purple-secondary);
  --color-focus-green: var(--color-green-secondary);
  --color-focus-orange: var(--color-orange-secondary);

  /* Language-specific color variables */
  --color-lang-javascript: #f7df1e;
  --color-lang-python: #3776ab;
  --color-lang-html: #e34f26;
  --color-lang-css: #1572b6;
  --color-lang-java: #ed8b00;
  --color-lang-cpp: #00599c;
  --color-lang-csharp: #239120;
  --color-lang-php: #777bb4;
  --color-lang-ruby: #cc342d;
  --color-lang-go: #00add8;
  --color-lang-rust: #000;
  --color-lang-typescript: #3178c6;
  --color-lang-json: #000;
  --color-lang-xml: #f60;
  --color-lang-sql: #336791;
  --color-lang-bash: #4eaa25;
  --color-lang-powershell: #012456;
  --color-lang-default: var(--color-blue-primary);

  /* Prompt Category Color Variables */
  --color-prompt-builtin: var(--color-blue-primary);
  --color-prompt-manual: var(--color-purple-primary);
  --color-prompt-library: var(--color-green-primary);
  --color-prompt-custom: var(--color-orange-primary);

  /* Prompt State Colors */
  --color-prompt-active: var(--color-green-primary);
  --color-prompt-modified: var(--color-orange-primary);
  --color-prompt-error: var(--color-error);
  --color-prompt-inactive: var(--color-sora-gray-light);
}

@media (prefers-contrast: high) {
  :root {
    /* High contrast color overrides */
    --color-blue-primary: #06f;
    --color-blue-secondary: #0052cc;
    --color-purple-primary: #60c;
    --color-purple-secondary: #5200a3;
    --color-green-primary: #0c6;
    --color-green-secondary: #00a352;
    --color-orange-primary: #f60;
    --color-orange-secondary: #cc5200;
    --color-error: #c00;
  }
}
