/* Shadow and Depth Tokens */
:root {
  /* Shadow Colors */
  --shadow-blue: rgba(56, 189, 248, 0.3);
  --shadow-purple: rgba(139, 92, 246, 0.3);
  --shadow-green: rgba(16, 185, 129, 0.3);
  --shadow-orange: rgba(245, 158, 11, 0.3);
  --shadow-neutral: rgba(0, 0, 0, 0.1);
  --shadow-neutral-dark: rgba(0, 0, 0, 0.3);

  /* Glow Colors */
  --glow-blue: rgba(56, 189, 248, 0.4);
  --glow-purple: rgba(139, 92, 246, 0.4);
  --glow-green: rgba(16, 185, 129, 0.4);
  --glow-orange: rgba(245, 158, 11, 0.4);

  /* Layer Background Colors */
  --layer-bg-1: rgba(255, 255, 255, 0.02);
  --layer-bg-2: rgba(255, 255, 255, 0.04);
  --layer-bg-3: rgba(255, 255, 255, 0.06);
  --layer-bg-4: rgba(255, 255, 255, 0.08);
  --layer-bg-5: rgba(255, 255, 255, 0.1);
}

html:not(.dark) {
  --layer-bg-1: rgba(0, 0, 0, 0.02);
  --layer-bg-2: rgba(0, 0, 0, 0.04);
  --layer-bg-3: rgba(0, 0, 0, 0.06);
  --layer-bg-4: rgba(0, 0, 0, 0.08);
  --layer-bg-5: rgba(0, 0, 0, 0.1);
  --shadow-neutral: rgba(0, 0, 0, 0.15);
  --shadow-neutral-dark: rgba(0, 0, 0, 0.25);
}
