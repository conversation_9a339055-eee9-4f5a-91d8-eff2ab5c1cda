/* Color Utility Classes */
.text-blue-primary {
  color: var(--color-blue-primary);
}

.text-blue-secondary {
  color: var(--color-blue-secondary);
}

.text-blue-accent {
  color: var(--color-blue-accent);
}

.text-purple-primary {
  color: var(--color-purple-primary);
}

.text-purple-secondary {
  color: var(--color-purple-secondary);
}

.text-green-primary {
  color: var(--color-green-primary);
}

.text-green-secondary {
  color: var(--color-green-secondary);
}

.text-orange-primary {
  color: var(--color-orange-primary);
}

.text-orange-secondary {
  color: var(--color-orange-secondary);
}

.bg-blue-primary {
  background-color: var(--color-blue-primary);
}

.bg-blue-secondary {
  background-color: var(--color-blue-secondary);
}

.bg-purple-primary {
  background-color: var(--color-purple-primary);
}

.bg-purple-secondary {
  background-color: var(--color-purple-secondary);
}

.bg-green-primary {
  background-color: var(--color-green-primary);
}

.bg-green-secondary {
  background-color: var(--color-green-secondary);
}

.bg-orange-primary {
  background-color: var(--color-orange-primary);
}

.bg-orange-secondary {
  background-color: var(--color-orange-secondary);
}

.border-blue-primary {
  border-color: var(--color-blue-primary);
}

.border-blue-secondary {
  border-color: var(--color-blue-secondary);
}

.border-purple-primary {
  border-color: var(--color-purple-primary);
}

.border-purple-secondary {
  border-color: var(--color-purple-secondary);
}

.border-green-primary {
  border-color: var(--color-green-primary);
}

.border-green-secondary {
  border-color: var(--color-green-secondary);
}

.border-orange-primary {
  border-color: var(--color-orange-primary);
}

.border-orange-secondary {
  border-color: var(--color-orange-secondary);
}

/* Semantic Color Utility Classes */
.text-user-message {
  color: var(--color-user-message);
}

.text-assistant-message {
  color: var(--color-assistant-message);
}

.text-system-message {
  color: var(--color-system-message);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-error {
  color: var(--color-error);
}

.text-info {
  color: var(--color-info);
}

.text-accent {
  color: var(--color-accent);
}

.bg-user-message {
  background-color: var(--color-user-message);
}

.bg-assistant-message {
  background-color: var(--color-assistant-message);
}

.bg-system-message {
  background-color: var(--color-system-message);
}

.bg-success {
  background-color: var(--color-success);
}

.bg-warning {
  background-color: var(--color-warning);
}

.bg-error {
  background-color: var(--color-error);
}

.bg-info {
  background-color: var(--color-info);
}

.bg-accent {
  background-color: var(--color-accent);
}

.border-user-message {
  border-color: var(--color-user-message);
}

.border-assistant-message {
  border-color: var(--color-assistant-message);
}

.border-system-message {
  border-color: var(--color-system-message);
}

.border-success {
  border-color: var(--color-success);
}

.border-warning {
  border-color: var(--color-warning);
}

.border-error {
  border-color: var(--color-error);
}

.border-info {
  border-color: var(--color-info);
}

.border-accent {
  border-color: var(--color-accent);
}

/* Background Tint Utility Classes */
.bg-blue-tint {
  background-color: rgba(56, 189, 248, 0.1);
}

.bg-purple-tint {
  background-color: rgba(139, 92, 246, 0.1);
}

.bg-green-tint {
  background-color: rgba(16, 185, 129, 0.1);
}

.bg-orange-tint {
  background-color: rgba(245, 158, 11, 0.1);
}
