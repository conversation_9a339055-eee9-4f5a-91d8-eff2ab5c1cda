/* Visual Effects Utilities */

/* Colored Shadow Utilities */
.shadow-blue-sm {
  box-shadow:
    0 1px 3px var(--shadow-blue),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow-blue {
  box-shadow:
    0 4px 6px var(--shadow-blue),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-blue-lg {
  box-shadow:
    0 10px 15px var(--shadow-blue),
    0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-purple-sm {
  box-shadow:
    0 1px 3px var(--shadow-purple),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow-purple {
  box-shadow:
    0 4px 6px var(--shadow-purple),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-purple-lg {
  box-shadow:
    0 10px 15px var(--shadow-purple),
    0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-green-sm {
  box-shadow:
    0 1px 3px var(--shadow-green),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow-green {
  box-shadow:
    0 4px 6px var(--shadow-green),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-green-lg {
  box-shadow:
    0 10px 15px var(--shadow-green),
    0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-orange-sm {
  box-shadow:
    0 1px 3px var(--shadow-orange),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow-orange {
  box-shadow:
    0 4px 6px var(--shadow-orange),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-orange-lg {
  box-shadow:
    0 10px 15px var(--shadow-orange),
    0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-neutral-sm {
  box-shadow:
    0 1px 3px var(--shadow-neutral),
    0 1px 2px rgba(0, 0, 0, 0.05);
}

.shadow-neutral {
  box-shadow:
    0 4px 6px var(--shadow-neutral),
    0 2px 4px rgba(0, 0, 0, 0.05);
}

.shadow-neutral-lg {
  box-shadow:
    0 10px 15px var(--shadow-neutral),
    0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Colored Glow Utilities */
.glow-blue-sm {
  box-shadow: 0 0 4px var(--glow-blue);
}

.glow-blue {
  box-shadow: 0 0 8px var(--glow-blue);
}

.glow-blue-lg {
  box-shadow: 0 0 16px var(--glow-blue);
}

.glow-purple-sm {
  box-shadow: 0 0 4px var(--glow-purple);
}

.glow-purple {
  box-shadow: 0 0 8px var(--glow-purple);
}

.glow-purple-lg {
  box-shadow: 0 0 16px var(--glow-purple);
}

.glow-green-sm {
  box-shadow: 0 0 4px var(--glow-green);
}

.glow-green {
  box-shadow: 0 0 8px var(--glow-green);
}

.glow-green-lg {
  box-shadow: 0 0 16px var(--glow-green);
}

.glow-orange-sm {
  box-shadow: 0 0 4px var(--glow-orange);
}

.glow-orange {
  box-shadow: 0 0 8px var(--glow-orange);
}

.glow-orange-lg {
  box-shadow: 0 0 16px var(--glow-orange);
}

/* Combined Shadow and Glow Effects */
.shadow-glow-blue {
  box-shadow:
    0 4px 6px var(--shadow-blue),
    0 0 8px var(--glow-blue),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-glow-purple {
  box-shadow:
    0 4px 6px var(--shadow-purple),
    0 0 8px var(--glow-purple),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-glow-green {
  box-shadow:
    0 4px 6px var(--shadow-green),
    0 0 8px var(--glow-green),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-glow-orange {
  box-shadow:
    0 4px 6px var(--shadow-orange),
    0 0 8px var(--glow-orange),
    0 2px 4px rgba(0, 0, 0, 0.1);
}
