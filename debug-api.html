<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug API Issues</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 API Debug Tool</h1>
        
        <div class="debug-section info">
            <h3>Current Issues Found:</h3>
            <ul>
                <li>✅ <strong>Fixed:</strong> <code>selected.provider</code> → <code>selectedProvider</code> in api.js:863</li>
                <li>⚠️ <strong>Potential:</strong> API token might not be configured</li>
                <li>⚠️ <strong>Potential:</strong> Server configuration not loading (404 error)</li>
            </ul>
        </div>

        <div class="debug-section">
            <h3>Steps to Fix:</h3>
            <ol>
                <li><strong>Refresh the browser</strong> to load the fixed JavaScript</li>
                <li><strong>Open Settings</strong> (⚙️ gear icon)</li>
                <li><strong>Configure API:</strong>
                    <ul>
                        <li>Select API Provider (CHUTES_AI or OpenRouter)</li>
                        <li>Enter your API token</li>
                        <li>Select a model</li>
                        <li>Click "Test Connection" to verify</li>
                        <li>Click "Save Settings"</li>
                    </ul>
                </li>
                <li><strong>Try sending a message</strong></li>
            </ol>
        </div>

        <div class="debug-section warning">
            <h3>Console Errors to Watch For:</h3>
            <pre>
❌ ReferenceError: selected is not defined (FIXED)
❌ API token missing
❌ No valid model available
❌ Failed to load server configuration
            </pre>
        </div>

        <div class="debug-section">
            <h3>Quick Actions:</h3>
            <button onclick="window.open('http://localhost:3000', '_blank')">Open Main App</button>
            <button onclick="checkConsole()">Check Console</button>
            <button onclick="testAPI()">Test API Setup</button>
        </div>

        <div id="test-results" class="debug-section" style="display: none;">
            <h3>Test Results:</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        function checkConsole() {
            alert('Open browser DevTools (F12) and check the Console tab for errors');
        }

        function testAPI() {
            const resultsDiv = document.getElementById('test-results');
            const contentDiv = document.getElementById('results-content');
            
            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = '<p>Testing API setup...</p>';
            
            // Simple test to check if main app elements exist
            try {
                const mainWindow = window.open('http://localhost:3000', '_blank');
                setTimeout(() => {
                    contentDiv.innerHTML = `
                        <div class="info">
                            <p><strong>Manual Test Required:</strong></p>
                            <ol>
                                <li>Check if the main app opened</li>
                                <li>Open Settings and configure API</li>
                                <li>Try sending a test message</li>
                                <li>Check browser console for errors</li>
                            </ol>
                        </div>
                    `;
                }, 1000);
            } catch (error) {
                contentDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Auto-refresh reminder
        setTimeout(() => {
            if (confirm('Would you like to open the main app to test the fix?')) {
                window.open('http://localhost:3000', '_blank');
            }
        }, 2000);
    </script>
</body>
</html>
