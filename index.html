<!DOCTYPE html>
<html lang="en" class="dark"> <!-- Add dark class by default -->
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nebula AI - Dark LLM Interface</title>
    
    <!-- Skip Links for Keyboard Navigation -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#user-input" class="skip-link">Skip to input</a>
    <a href="#settings" class="skip-link">Skip to settings</a>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script> <!-- Added marked.js -->
    <!-- Added KaTeX for Math Rendering -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css" integrity="sha384-5TcZemv2l/9On385z///+d7MSYlvIEw9FuZTIdZ14vJLqWphw7e7ZPuOiCHJcFCP" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js" integrity="sha384-cMkvdD8LoxVzGF/RPUKAcvmm49FQ0oxwDF3BGKtDXcEc+T1b2N+teh/OJfpU0jr6" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/contrib/auto-render.min.js" integrity="sha384-hCXGrW6PitJEwbkoStFjeJxv+fSOOQKOPbJxSfM6G5sWZjAyWhXiTIIAmQqnlLlh" crossorigin="anonymous"></script>
    <!-- End KaTeX -->
    <!-- Added Prism.js for Code Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-okaidia.min.css">
<link rel="stylesheet" href="css/main.compiled.css">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-css.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-json.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-bash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-jsx.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-typescript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-tsx.min.js"></script>
    <!-- Removed PHP component due to errors -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-php.min.js"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-java.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-c.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-cpp.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-csharp.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-go.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-ruby.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-rust.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-sql.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-yaml.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-markdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-markup.min.js"></script>
    <script>
        // Initialize Prism.js when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if (window.Prism) {
                console.log('Initializing Prism.js on DOMContentLoaded');
                document.querySelectorAll('pre code').forEach(function(block) {
                    try {
                        // Check if the language is supported
                        const langClass = block.className.match(/language-(\w+)/);
                        if (langClass && langClass[1]) {
                            const lang = langClass[1];
                            // Skip PHP highlighting as it causes errors
                            if (lang === 'php') {
                                console.log('Skipping PHP highlighting due to known issues');
                                return;
                            }

                            // Only highlight if the language grammar is loaded
                            if (Prism.languages[lang]) {
                                Prism.highlightElement(block);
                            } else {
                                console.log(`Language grammar for ${lang} not loaded, using plaintext`);
                                block.className = 'language-plaintext';
                                Prism.highlightElement(block);
                            }
                        } else {
                            // No language specified, use plaintext
                            block.className = 'language-plaintext';
                            Prism.highlightElement(block);
                        }
                    } catch (e) {
                        console.warn('Error highlighting code block:', e);
                    }
                });
            }
        });
    </script>
    <!-- End Prism.js -->
    <!-- Added Tailwind Typography Plugin -->
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: { // Keep light mode primary colors
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        // Sora-like dark theme colors (updated to match samples)
                        black: '#000000',
                        white: '#FFFFFF',
                        'sora-gray': {
                            dark: '#1A1A1A',    // Darker gray for cards, selected items
                            medium: '#333333',  // Medium gray for borders, inputs, secondary elements
                            light: '#E0E0E0',   // Light gray for subtle text/icons - improved contrast
                            lighter: '#F0F0F0'  // Very light gray for hover states - improved contrast
                        },
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-white dark:bg-black"> <!-- Updated base bg -->
    <div class="flex flex-col h-screen px-4"> <!-- Changed for full-screen flex column -->
        <!-- Header -->
        <header class="sticky top-0 z-20 bg-white dark:bg-black py-4 flex justify-between items-center container-layer-elevated depth-transition" data-ui-context="header" data-importance="high"> <!-- Updated bg -->
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary-500 to-primary-700 dark:bg-sora-gray-dark flex items-center justify-center shadow-blue-sm glow-blue-sm depth-hover"> <!-- Updated dark bg -->
                    <i class="fas fa-robot text-white"></i>
                </div>
                <h1 class="text-2xl font-bold gradient-text importance-medium">Nebula<span class="text-gray-900 dark:text-white">AI</span></h1>
            </div>
            <div class="flex space-x-4 items-center" data-ui-context="header-actions"> <!-- Added items-center -->
                 <!-- Connection Status Indicator -->
                <div id="headerConnectionStatus" class="status-indicator status-indicator-sm status-connected hidden focus-enhanced" 
                     data-status="connected" 
                     role="status" 
                     aria-live="polite" 
                     aria-label="API connection status"
                     tabindex="0">
                    <i class="fas fa-wifi" aria-hidden="true"></i>
                    <span class="status-text-indicator status-connected">Connected</span>
                    <span class="sr-only">API connection is active</span>
                </div>
                 <!-- Placeholder for Parameters Button -->
                <button id="toggleParamsBtnHeader" 
                        class="p-2 rounded-full text-gray-500 hover:bg-gray-100 dark:text-white dark:hover:bg-sora-gray-dark transition-colors depth-hover depth-focus focus-enhanced" 
                        data-ui-context="settings-toggle" 
                        data-importance="medium"
                        aria-label="Open settings panel"
                        aria-expanded="false"
                        aria-controls="paramsPanel"> <!-- Updated colors -->
                     <i class="fas fa-sliders-h text-primary-500 dark:text-white" aria-hidden="true"></i>
                     <span class="sr-only">Settings</span>
                </button>
                 <!-- End Placeholder -->
                <button id="themeToggle" 
                        class="p-2 rounded-full text-gray-500 hover:bg-gray-100 dark:text-white dark:hover:bg-sora-gray-dark transition-colors depth-hover depth-focus focus-enhanced" 
                        data-ui-context="theme-toggle" 
                        data-importance="low"
                        aria-label="Toggle dark/light theme"
                        aria-pressed="true"> <!-- Updated colors -->
                    <i class="fas fa-moon text-primary-500 dark:text-white" aria-hidden="true"></i> <!-- Icon class updated by JS -->
                    <span class="sr-only">Dark mode active</span>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <!-- Main Chat Area (occupies remaining vertical space) -->
        <main id="main-content" class="flex-1 overflow-hidden flex flex-col" role="main" aria-label="Chat interface"> <!-- Added classes for flex grow and vertical column flow -->
             <div class="relative flex-1 bg-gray-50 dark:bg-black rounded-t-xl overflow-hidden flex flex-col"> <!-- Added relative positioning -->
                    <!-- Chat Header -->
                    <div class="bg-gray-100 dark:bg-black px-6 py-4 border-b border-gray-200 dark:border-sora-gray-dark flex justify-between items-center max-w-4xl w-full mx-auto container-layer-base depth-transition" data-ui-context="chat-header" data-importance="medium"> <!-- Updated bg -->
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white importance-medium">Hey there! What's on your mind today?</h2> <!-- Updated text to match sample -->
                        <div class="flex space-x-2" data-ui-context="chat-actions">
                            <!-- Chat Status Indicators -->
                            <div id="chatStatusContainer" class="flex items-center space-x-2 mr-2" role="status" aria-live="polite">
                                <div id="messageCountBadge" 
                                     class="status-badge status-connected hidden" 
                                     data-status="active" 
                                     title="Active conversation"
                                     aria-label="Message count">
                                    <span class="text-xs font-medium">0</span>
                                    <span class="sr-only">messages in conversation</span>
                                </div>
                                <div id="typingIndicator" 
                                     class="status-dot status-loading hidden" 
                                     data-status="typing" 
                                     title="AI is thinking..."
                                     aria-label="AI typing indicator"
                                     role="status"
                                     aria-live="polite">
                                    <span class="sr-only">AI is typing</span>
                                </div>
                            </div>
                            <button id="clearChat" 
                                    class="text-xs bg-gray-200 hover:bg-gray-300 text-gray-600 dark:bg-sora-gray-dark dark:hover:bg-sora-gray-medium dark:text-white px-3 py-1 rounded-full transition-colors depth-hover depth-focus focus-enhanced" 
                                    data-ui-context="clear-chat" 
                                    data-importance="low"
                                    aria-label="Clear all messages from conversation"> <!-- Updated text color -->
                                <i class="fas fa-trash-alt mr-1" aria-hidden="true"></i> Clear
                            </button>
                        </div>
                    </div>

                    <!-- Messages Container -->
                    <div id="messagesContainer" 
                         class="flex-1 overflow-y-auto p-6 space-y-4 scrollbar-hide max-w-4xl w-full mx-auto container-layer-base" 
                         data-ui-context="messages" 
                         data-importance="critical"
                         role="log"
                         aria-live="polite"
                         aria-label="Conversation messages"> <!-- Added width constraint -->
                        <!-- Messages will be dynamically inserted with visual hierarchy -->
                        <!-- Section separator for conversation start -->
                        <div class="section-separator blue hidden" 
                             id="conversationStart"
                             role="separator"
                             aria-label="Conversation started">
                            <span class="section-separator-text">
                                <i class="fas fa-comments mr-1" aria-hidden="true"></i>
                                Conversation Started
                            </span>
                        </div>
                    </div>

                    <!-- Input Area -->
                    <div class="relative sticky bottom-0 z-10 p-4 border-t border-gray-200 dark:border-sora-gray-dark bg-gray-50 dark:bg-black container-layer-elevated depth-transition" data-ui-context="input-area" data-importance="critical"> <!-- Updated background -->
                        <!-- System Prompt Indicator - Positioned over the top border -->
                        <!-- Old System Prompt Indicator Removed -->
                        <div class="max-w-4xl w-full mx-auto"> <!-- Added wrapper for width constraint -->
                            <div class="relative">
                            <textarea id="userInput" 
                                      class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-full px-4 py-3 pr-28 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-sora-gray-light focus:outline-none focus:ring-1 focus:ring-primary-500 dark:focus:ring-sora-gray-light focus:border-transparent resize-none depth-focus depth-transition focus-enhanced" 
                                      placeholder="What do you want to know?" 
                                      data-ui-context="user-input" 
                                      data-importance="critical"
                                      aria-label="Type your message here"
                                      aria-describedby="charCount inputValidationStatus"
                                      role="textbox"
                                      aria-multiline="true"></textarea>
                            <button id="clearInputBtn" 
                                    class="absolute top-1/2 -translate-y-1/2 right-16 text-gray-400 hover:text-gray-600 dark:text-sora-gray-light dark:hover:text-white p-1.5 rounded-full transition-colors hidden depth-hover focus-enhanced" 
                                    title="Clear input" 
                                    data-ui-context="clear-input" 
                                    data-importance="low"
                                    aria-label="Clear input text">
                                <i class="fas fa-times text-sm" aria-hidden="true"></i>
                            </button>
                            <button id="sendBtn" 
                                    class="absolute right-3 top-1/2 -translate-y-1/2 bg-primary-600 hover:bg-primary-500 dark:bg-sora-gray-dark dark:hover:bg-sora-gray-medium text-white dark:text-white p-2 rounded-full transition-colors shadow-blue-sm glow-blue-sm depth-hover depth-focus depth-active focus-enhanced" 
                                    data-ui-context="send-message" 
                                    data-importance="high"
                                    aria-label="Send message"
                                    type="submit"> <!-- Updated to rounded-full -->
                                <i class="fas fa-paper-plane" aria-hidden="true"></i>
                                <span class="sr-only">Send</span>
                            </button>
                        </div>
                        <div class="flex justify-between items-center mt-2 text-xs text-gray-500 dark:text-sora-gray-light"> <!-- Updated dark text -->
                            <!-- Input Status Indicators -->
                            <div class="flex items-center space-x-2">
                                <!-- System Prompt Indicator - Moved below input -->
                                <div id="systemPromptIndicator" class="hidden text-primary-500 dark:text-sora-gray-light cursor-pointer transition-opacity mr-2 status-indicator-sm" title="" data-ui-context="system-prompt-status" data-importance="medium">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <!-- Input validation status -->
                                <div id="inputValidationStatus" class="status-dot status-connected hidden" data-status="valid" title="Input is valid"></div>
                            </div>
                            <!-- Character Count -->
                            <span id="charCount" class="ml-auto">0/1000</span>
                            </div>
                        </div> <!-- End wrapper for width constraint -->
                    </div>
                </div>
            </div>

            <!-- Parameters Panel (Collapsible) -->
            <!-- Parameters Modal -->
            <div id="paramsPanel" 
                 class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 opacity-0 scale-95 invisible pointer-events-none transition-all duration-200 ease-in-out" 
                 data-ui-context="settings-modal" 
                 data-importance="high"
                 role="dialog"
                 aria-modal="true"
                 aria-labelledby="paramsModalTitle"
                 aria-describedby="paramsModalDescription"> <!-- Updated opacity -->
                <div class="bg-white dark:bg-sora-gray-dark rounded-xl shadow-xl w-full max-w-md border border-gray-200 dark:border-sora-gray-medium max-h-[90vh] flex flex-col transform transition-all duration-200 ease-in-out container-layer-modal shadow-glow-blue">
                <div class="bg-gray-100 dark:bg-sora-gray-dark px-6 py-4 border-b border-gray-200 dark:border-sora-gray-medium rounded-t-xl layer-bg-2" data-ui-context="settings-header" data-importance="medium"> <!-- Updated background -->
                    <div class="flex justify-between items-center"> <!-- Added wrapper for title and close button -->
                        <h2 id="paramsModalTitle" class="text-lg font-semibold text-gray-900 dark:text-white importance-high">API Parameters</h2> <!-- Updated text -->
                        <button id="closeParamsModal" 
                                class="p-2 rounded-full text-gray-400 hover:text-gray-600 dark:text-sora-gray-light dark:hover:text-white hover:bg-gray-200 dark:hover:bg-sora-gray-medium transition-colors depth-hover depth-focus focus-enhanced" 
                                data-ui-context="close-settings" 
                                data-importance="low"
                                aria-label="Close settings panel"> <!-- Updated colors -->
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                    <p id="paramsModalDescription" class="sr-only">Configure API settings, system prompts, and model parameters</p>
                </div>
                <div class="p-6 space-y-6 overflow-y-auto scrollbar-hide"> <!-- Made content scrollable -->
                    <!-- System Prompt Input -->
                    <div class="settings-section-header prompt layer-bg-green-1 depth-transition" data-ui-context="prompt-settings" data-importance="high">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-md font-semibold text-gray-500 dark:text-sora-gray-light importance-high">
                                <i class="fas fa-comment-dots settings-icon prompt mr-2"></i>
                                System Prompt Configuration
                            </h3>
                            <button id="expandSystemPromptBtn" title="Edit in larger window" class="text-xs bg-gray-200 hover:bg-gray-300 text-gray-600 dark:bg-sora-gray-dark dark:hover:bg-sora-gray-medium dark:text-white px-3 py-1.5 rounded-full transition-colors depth-hover depth-focus flex items-center" data-ui-context="expand-prompt" data-importance="low">
                                <i class="fas fa-expand-alt mr-1"></i> Expand
                            </button>
                        </div>
                        
                        <!-- System Prompt Source Selector -->
                        <div class="mb-3">
                            <label for="systemPromptSource" class="block text-xs font-medium text-gray-600 dark:text-sora-gray-light mb-1">Prompt Source</label>
                            <select id="systemPromptSource" 
                                    class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-3 py-1.5 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus-enhanced"
                                    aria-describedby="systemPromptSourceHelp">
                                <option value="file">📁 Built-in (NSFW Base)</option>
                                <option value="manual">✏️ Manual Input</option>
                                <option value="library">📚 From Library</option>
                            </select>
                            <div id="systemPromptSourceHelp" class="sr-only">Choose the source for your system prompt: built-in prompts, manual input, or from your prompt library</div>
                        </div>
                        
                        <!-- System Prompt Container -->
                        <div id="systemPromptContainer">
                            <!-- For File Source -->
                            <div id="filePromptPreview" class="hidden">
                                <div class="bg-gray-50 dark:bg-sora-gray-medium border border-gray-300 dark:border-sora-gray-medium rounded-lg p-3 mb-2">
                                    <div class="flex justify-between items-start mb-2">
                                        <span class="text-xs font-medium text-gray-600 dark:text-sora-gray-light">Active Built-in Prompt:</span>
                                        <button id="copyFilePromptBtn" class="text-xs bg-blue-100 hover:bg-blue-200 dark:bg-blue-600 dark:hover:bg-blue-500 text-blue-700 dark:text-white px-2 py-1 rounded transition-colors">
                                            <i class="fas fa-copy mr-1"></i> Copy to Settings
                                        </button>
                                    </div>
                                    <div id="filePromptText" class="text-sm text-gray-700 dark:text-gray-300 max-h-20 overflow-y-auto scrollbar-thin">
                                        <!-- File prompt content will be shown here -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- For Manual Input -->
                            <div id="manualPromptInput">
                                <textarea id="systemPromptInput" rows="2" class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-sora-gray-light focus:outline-none focus:ring-1 focus:ring-primary-500 dark:focus:ring-sora-gray-light focus:border-transparent resize-none transition-all duration-200 ease-in-out focus:rows-6" placeholder="Enter system prompt... (e.g., You are a helpful assistant)"></textarea>
                            </div>
                            
                            <!-- For Library Source -->
                            <div id="libraryPromptSelector" class="hidden">
                                <select id="libraryPromptSelect" class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary-500 mb-2">
                                    <option value="">Select a prompt from library...</option>
                                    <!-- Library prompts will be populated here -->
                                </select>
                                <div id="libraryPromptPreview" class="bg-gray-50 dark:bg-sora-gray-medium border border-gray-300 dark:border-sora-gray-medium rounded-lg p-3 text-sm text-gray-700 dark:text-gray-300 max-h-20 overflow-y-auto scrollbar-thin hidden">
                                    <!-- Selected library prompt preview -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="mt-3 flex justify-between items-center">
                            <button id="promptLibraryBtn" class="text-xs bg-gray-200 hover:bg-gray-300 text-gray-600 dark:bg-sora-gray-dark dark:hover:bg-sora-gray-medium dark:text-white px-4 py-2 rounded-full transition-colors flex items-center">
                                <i class="fas fa-book-open mr-1"></i> Prompt Library
                            </button>

                            <!-- Status indicator -->
                            <div id="systemPromptStatus" class="text-xs text-gray-500 dark:text-sora-gray-light flex items-center">
                                <i class="fas fa-info-circle mr-1"></i>
                                <span id="systemPromptStatusText">Using built-in prompt</span>
                            </div>
                        </div>
                    </div>
                    <!-- End System Prompt Input -->
                    
                    <!-- API Settings Section -->
                    <hr class="settings-divider api">
                    <div class="settings-section-header api layer-bg-blue-1 depth-transition" data-ui-context="api-settings" data-importance="high">
                        <h3 class="text-md font-semibold text-gray-500 dark:text-sora-gray-light mb-4 importance-high">
                            <i class="fas fa-plug settings-icon api mr-2"></i>
                            API Configuration
                        </h3>

                        <!-- API Provider Selection -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-sora-gray-light mb-2">API Provider</label>
                            <select id="apiProviderSelect" class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary-500 dark:focus:ring-sora-gray-light focus:border-transparent settings-input api depth-focus depth-transition" data-ui-context="api-provider" data-importance="high">
                                <option value="CHUTES_AI">Chutes AI</option>
                                <option value="OPENROUTER">OpenRouter.ai</option>
                            </select>
                        </div>

                        <!-- API Token Fields -->
                        <div id="chutesApiTokenField" class="mb-4 semantic-settings" data-category="api">
                            <label for="apiToken" class="block text-sm font-medium text-gray-700 dark:text-sora-gray-light mb-2">
                                <span class="settings-category-text api">Chutes AI API Token</span>
                            </label>
                            <div class="relative">
                                <input id="apiToken"
                                       type="password"
                                       class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 pr-12 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary-500 dark:focus:ring-sora-gray-light focus:border-transparent settings-input api depth-focus depth-transition focus-enhanced"
                                       placeholder="Enter your Chutes AI API token"
                                       data-ui-context="api-token"
                                       data-importance="critical"
                                       aria-describedby="apiTokenHelp tokenValidationStatus"
                                       autocomplete="off">
                                <button id="toggleTokenVisibility"
                                        class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:text-sora-gray-light dark:hover:text-white depth-hover focus-enhanced"
                                        data-ui-context="toggle-token"
                                        data-importance="low"
                                        aria-label="Toggle token visibility"
                                        type="button">
                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                    <span class="sr-only">Show token</span>
                                </button>
                                <!-- Token validation status -->
                                <div id="tokenValidationStatus"
                                     class="status-dot status-connected hidden absolute right-10 top-1/2 -translate-y-1/2 visual-indicator-alt status-connected"
                                     data-status="valid"
                                     title="Token is valid"
                                     role="status"
                                     aria-live="polite"
                                     aria-label="Token validation status">
                                    <span class="sr-only">Token is valid</span>
                                </div>
                            </div>
                            <div id="apiTokenHelp" class="sr-only">Enter your API token to authenticate with the Chutes AI service</div>
                        </div>

                        <div id="openrouterApiTokenField" class="hidden mb-4 semantic-settings" data-category="api">
                            <label for="openrouterApiToken" class="block text-sm font-medium text-gray-700 dark:text-sora-gray-light mb-2">
                                <span class="settings-category-text api">OpenRouter API Key</span>
                            </label>
                            <div class="relative">
                                <input id="openrouterApiToken"
                                       type="password"
                                       class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 pr-12 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary-500 dark:focus:ring-sora-gray-light focus:border-transparent settings-input api depth-focus depth-transition focus-enhanced"
                                       placeholder="Enter your OpenRouter API key"
                                       data-ui-context="openrouter-token"
                                       data-importance="critical"
                                       aria-describedby="openrouterTokenHelp openrouterTokenValidationStatus"
                                       autocomplete="off">
                                <button id="toggleOpenrouterTokenVisibility"
                                        class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:text-sora-gray-light dark:hover:text-white depth-hover focus-enhanced"
                                        data-ui-context="toggle-openrouter-token"
                                        data-importance="low"
                                        aria-label="Toggle OpenRouter token visibility"
                                        type="button">
                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                    <span class="sr-only">Show OpenRouter token</span>
                                </button>
                                <!-- Token validation status -->
                                <div id="openrouterTokenValidationStatus"
                                     class="status-dot status-connected hidden absolute right-10 top-1/2 -translate-y-1/2 visual-indicator-alt status-connected"
                                     data-status="valid"
                                     title="OpenRouter token is valid"
                                     role="status"
                                     aria-live="polite"
                                     aria-label="OpenRouter token validation status">
                                    <span class="sr-only">OpenRouter token is valid</span>
                                </div>
                            </div>
                            <div id="openrouterTokenHelp" class="sr-only">Enter your OpenRouter API key to authenticate with the OpenRouter service</div>
                        </div>

                        <!-- Model Selection -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-sora-gray-light mb-2">Model</label>
                            <select id="modelSelect" class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary-500 dark:focus:ring-sora-gray-light focus:border-transparent settings-input api depth-focus depth-transition" data-ui-context="model-select" data-importance="high">
                                <!-- Models will be populated dynamically based on selected provider -->
                            </select>
                        </div>

                        <!-- Advanced Parameters -->
                        <div class="space-y-4 mb-4">
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <label class="text-sm font-medium text-gray-700 dark:text-sora-gray-light">Temperature: <span id="tempValue">0.7</span></label>
                                </div>
                                <input id="temperature" type="range" min="0" max="2" step="0.1" value="0.7" class="w-full h-2 bg-gray-200 dark:bg-sora-gray-dark rounded-lg appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary-500 dark:[&::-webkit-slider-thumb]:bg-white depth-transition" data-ui-context="temperature-slider" data-importance="medium">
                            </div>

                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <label class="text-sm font-medium text-gray-700 dark:text-sora-gray-light">Max Tokens: <span id="maxTokensValue">32768</span></label>
                                </div>
                                <input id="maxTokens" type="range" min="128" max="65536" step="128" value="32768" class="w-full h-2 bg-gray-200 dark:bg-sora-gray-dark rounded-lg appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary-500 dark:[&::-webkit-slider-thumb]:bg-white depth-transition" data-ui-context="max-tokens-slider" data-importance="medium">
                            </div>
                        </div>

                        <!-- Stream Option -->
                        <div class="flex items-center mb-4">
                            <input id="streamCheckbox" type="checkbox" class="w-4 h-4 text-primary-600 dark:text-white bg-gray-100 dark:bg-black border-gray-300 dark:border-sora-gray-medium rounded focus:ring-primary-500 dark:focus:ring-sora-gray-light depth-focus" checked data-ui-context="stream-toggle" data-importance="medium">
                            <label for="streamCheckbox" class="ml-2 text-sm text-gray-700 dark:text-white">Stream Response</label>
                        </div>

                        <!-- Test Connection Button -->
                        <button id="testConnectionBtn" class="w-full bg-primary-600 hover:bg-primary-500 dark:bg-sora-gray-dark dark:hover:bg-sora-gray-medium text-white dark:text-white py-2 px-4 rounded-full transition-colors flex items-center justify-center space-x-2 settings-button api shadow-blue-sm depth-hover depth-focus depth-active" data-ui-context="test-connection" data-importance="high">
                            <i class="fas fa-plug"></i>
                            <span>Test Connection</span>
                            <!-- Connection test status -->
                            <div id="connectionTestStatus" class="status-dot status-connected hidden ml-2" data-status="testing" title="Testing connection..."></div>
                        </button>
                    </div>
                    <!-- UI Settings Section -->
                    <hr class="divider-purple">
                    <div class="settings-section-header ui layer-bg-purple-1 depth-transition" data-ui-context="ui-settings" data-importance="medium">
                        <h3 class="text-md font-semibold text-gray-500 dark:text-sora-gray-light mb-3 importance-medium">
                            <i class="fas fa-palette settings-icon ui mr-2"></i>
                            UI Settings
                        </h3>
                        <!-- Font Size Setting Removed -->
                        <!-- Theme Setting -->
                         <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-sora-gray-light mb-2">Theme</label>
                            <div class="flex space-x-3">
                                <button id="settingsDarkBtn" class="flex-1 bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-full px-4 py-2.5 text-gray-900 dark:text-white hover:border-primary-500 dark:hover:border-white transition-colors settings-button ui depth-hover depth-focus flex items-center justify-center" data-ui-context="theme-dark" data-importance="medium">
                                    <i class="fas fa-moon mr-2"></i>Dark
                                </button>
                                <button id="settingsLightBtn" class="flex-1 bg-gray-100 border border-gray-300 rounded-full px-4 py-2.5 text-gray-800 hover:border-primary-500 transition-colors settings-button ui depth-hover depth-focus flex items-center justify-center" data-ui-context="theme-light" data-importance="medium">
                                    <i class="fas fa-sun mr-2"></i>Light
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- End UI Settings Section -->
                    <!-- Save Button -->
                    <div class="pt-4 border-t border-gray-200 dark:border-sora-gray-medium mt-4 layer-bg-1" data-ui-context="save-section" data-importance="high"> <!-- Updated border -->
                         <button id="saveSettingsBtn" class="w-full bg-primary-600 hover:bg-primary-500 dark:bg-white dark:hover:bg-sora-gray-lighter text-white dark:text-black py-2 px-4 rounded-full transition-colors shadow-green-sm glow-green-sm depth-hover depth-focus depth-active importance-high" data-ui-context="save-settings" data-importance="critical"> <!-- Updated to rounded-full and hover color -->
                            <i class="fas fa-save mr-2"></i>
                            Save Settings
                            <!-- Save status indicator -->
                            <div id="saveStatusIndicator" class="status-dot status-connected hidden ml-2" data-status="saved" title="Settings saved"></div>
                        </button>
                    </div>
                </div> <!-- End scrollable content -->
            </div> <!-- End Modal Box -->
            </div>

    <!-- System Prompt Editor Modal -->
    <div id="systemPromptEditorModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-60 opacity-0 scale-95 invisible pointer-events-none transition-all duration-200 ease-in-out"> <!-- Higher z-index -->
        <div class="bg-white dark:bg-sora-gray-dark rounded-xl shadow-xl w-full max-w-2xl border border-gray-200 dark:border-sora-gray-medium h-[80vh] flex flex-col transform transition-all duration-200 ease-in-out"> <!-- Set explicit height, removed max-h -->
            <!-- Header -->
            <div class="bg-gray-100 dark:bg-sora-gray-medium px-6 py-4 border-b border-gray-200 dark:border-sora-gray-dark rounded-t-xl">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Edit System Prompt</h2>
                    <button id="closeSystemPromptEditorBtn" class="p-2 rounded-full text-gray-400 hover:text-gray-600 dark:text-sora-gray-light dark:hover:text-white hover:bg-gray-200 dark:hover:bg-sora-gray-medium transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Content -->
            <div class="p-6 flex-1 flex flex-col overflow-hidden"> <!-- Flex column for textarea growth -->
                <textarea id="systemPromptEditorTextarea" class="w-full flex-1 bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-sora-gray-light focus:outline-none focus:ring-1 focus:ring-primary-500 dark:focus:ring-sora-gray-light focus:border-transparent resize-none" placeholder="Enter system prompt..."></textarea> <!-- Updated background and focus ring -->
            </div>
            <!-- Footer -->
            <div class="bg-gray-50 dark:bg-sora-gray-dark px-6 py-4 border-t border-gray-200 dark:border-sora-gray-medium rounded-b-xl flex justify-end space-x-3">
                 <button id="cancelSystemPromptEditorBtn" class="bg-gray-200 hover:bg-gray-300 dark:bg-sora-gray-dark dark:hover:bg-sora-gray-medium text-gray-700 dark:text-white px-4 py-2 rounded-full transition-colors">
                    Cancel
                </button>
                <button id="saveSystemPromptEditorBtn" class="bg-primary-600 hover:bg-primary-500 dark:bg-white dark:hover:bg-sora-gray-lighter text-white dark:text-black px-4 py-2 rounded-full transition-colors">
                    Save Prompt
                </button>
            </div>
        </div>
    </div>
    <!-- End System Prompt Editor Modal -->
    <!-- Prompt Library Modal -->
    <div id="promptLibraryModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center opacity-0 scale-95 invisible pointer-events-none transition-all duration-200 ease-in-out" style="z-index: 1200;">
        <div class="bg-white dark:bg-sora-gray-dark rounded-xl shadow-xl w-full max-w-4xl border border-gray-200 dark:border-sora-gray-medium h-[80vh] flex flex-col transform transition-all duration-200 ease-in-out">
            <!-- Header -->
            <div class="bg-gray-100 dark:bg-sora-gray-medium px-6 py-4 border-b border-gray-200 dark:border-sora-gray-dark rounded-t-xl flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Prompt Library</h2>
                <button id="closePromptLibraryBtn" class="p-2 rounded-full text-gray-400 hover:text-gray-600 dark:text-sora-gray-light dark:hover:text-white hover:bg-gray-200 dark:hover:bg-sora-gray-medium transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <!-- Main Content -->
            <div class="flex-1 flex overflow-hidden">
                <!-- Prompt List (Left Side) -->
                <div class="w-1/3 border-r border-gray-200 dark:border-sora-gray-medium flex flex-col">
                    <div class="p-4 border-b border-gray-200 dark:border-sora-gray-medium">
                        <button id="addNewPromptBtn" class="w-full bg-primary-600 hover:bg-primary-500 text-white px-4 py-2 rounded-full text-sm">
                            <i class="fas fa-plus mr-1"></i> Add New Prompt
                        </button>
                    </div>
                    <div id="promptListContainer" class="flex-1 overflow-y-auto p-2 space-y-1">
                        <!-- Prompt items will be dynamically inserted here -->
                    </div>
                </div>
                <!-- Prompt Editor (Right Side) -->
                <div class="w-2/3 flex flex-col p-6">
                    <div class="mb-4">
                        <label for="promptTitleInput" class="block text-sm font-medium text-gray-700 dark:text-sora-gray-light mb-1">Prompt Title</label>
                        <input type="text" id="promptTitleInput" class="w-full bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary-500" placeholder="Enter a title for your prompt...">
                    </div>
                    <div class="flex-1 flex flex-col mb-4">
                        <label for="promptContentTextarea" class="block text-sm font-medium text-gray-700 dark:text-sora-gray-light mb-1">Prompt Content</label>
                        <textarea id="promptContentTextarea" class="w-full flex-1 bg-white dark:bg-black border border-gray-300 dark:border-sora-gray-medium rounded-lg px-4 py-2 text-gray-900 dark:text-white resize-none" placeholder="Enter the prompt content here..."></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button id="deletePromptBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-full transition-colors hidden">
                            <i class="fas fa-trash-alt mr-1"></i> Delete
                        </button>
                        <button id="savePromptBtn" class="bg-primary-600 hover:bg-primary-500 dark:bg-white dark:text-black px-4 py-2 rounded-full transition-colors">
                            Save Prompt
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <!-- Old Settings Modal Removed -->

    <!-- Scripts will be inserted here by the refactoring process -->



    <!-- Additional script to ensure KaTeX and Prism.js are initialized after page load -->
    <script>
        // Initialize KaTeX and Prism.js after the page has fully loaded
        window.addEventListener('load', function() {
            // Wait a bit to ensure all resources are loaded
            setTimeout(function() {
                // Initialize KaTeX
                if (typeof renderMathInElement === 'function') {
                    try {
                        console.log('Initializing KaTeX after full page load');
                        renderMathInElement(document.body, {
                            delimiters: [
                                {left: '$$', right: '$$', display: true},
                                {left: '$', right: '$', display: false},
                                {left: '\\(', right: '\\)', display: false},
                                {left: '\\[', right: '\\]', display: true}
                            ],
                            throwOnError: false,
                            trust: true,
                            strict: false
                        });
                        console.log('KaTeX initialization after page load complete');
                    } catch (e) {
                        console.warn('KaTeX initialization after page load error:', e);
                    }
                } else {
                    console.warn('KaTeX renderMathInElement function not available after page load');
                }

                // Initialize Prism.js for syntax highlighting
                if (window.Prism) {
                    try {
                        console.log('Initializing Prism.js after full page load');
                        // Force Prism to highlight all code blocks
                        document.querySelectorAll('pre code').forEach(function(block) {
                            try {
                                // Check if the language is supported
                                const langClass = block.className.match(/language-(\w+)/);
                                if (langClass && langClass[1]) {
                                    const lang = langClass[1];
                                    // Skip PHP highlighting as it causes errors
                                    if (lang === 'php') {
                                        console.log('Skipping PHP highlighting due to known issues');
                                        return;
                                    }

                                    // Only highlight if the language grammar is loaded
                                    if (Prism.languages[lang]) {
                                        Prism.highlightElement(block);
                                    } else {
                                        console.log(`Language grammar for ${lang} not loaded, using plaintext`);
                                        block.className = 'language-plaintext';
                                        Prism.highlightElement(block);
                                    }
                                } else {
                                    // No language specified, use plaintext
                                    block.className = 'language-plaintext';
                                    Prism.highlightElement(block);
                                }
                            } catch (e) {
                                console.warn('Error highlighting code block:', e);
                            }
                        });
                        console.log('Prism.js initialization after page load complete');
                    } catch (e) {
                        console.warn('Prism.js initialization after page load error:', e);
                    }
                } else {
                    console.warn('Prism.js not available after page load');
                }
            }, 1000);
        });
    </script>

    <!-- Direct Prism.js initialization script -->
    <script>
        // Force Prism.js to highlight all code blocks immediately
        if (window.Prism) {
            console.log('Direct Prism.js initialization');
            document.querySelectorAll('pre code').forEach(function(block) {
                try {
                    // Check if the language is supported
                    const langClass = block.className.match(/language-(\w+)/);
                    if (langClass && langClass[1]) {
                        const lang = langClass[1];
                        // Skip PHP highlighting as it causes errors
                        if (lang === 'php') {
                            console.log('Skipping PHP highlighting due to known issues');
                            return;
                        }

                        // Only highlight if the language grammar is loaded
                        if (Prism.languages[lang]) {
                            Prism.highlightElement(block);
                        } else {
                            console.log(`Language grammar for ${lang} not loaded, using plaintext`);
                            block.className = 'language-plaintext';
                            Prism.highlightElement(block);
                        }
                    } else {
                        // No language specified, use plaintext
                        block.className = 'language-plaintext';
                        Prism.highlightElement(block);
                    }
                } catch (e) {
                    console.warn('Error highlighting code block:', e);
                }
            });

            // Set up a MutationObserver to watch for new code blocks
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        // Check if any of the added nodes contain code blocks
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                const codeBlocks = node.querySelectorAll('pre code');
                                if (codeBlocks.length > 0) {
                                    console.log('New code blocks detected, applying syntax highlighting');
                                    codeBlocks.forEach(function(block) {
                                        try {
                                            // Check if the language is supported
                                            const langClass = block.className.match(/language-(\w+)/);
                                            if (langClass && langClass[1]) {
                                                const lang = langClass[1];
                                                // Skip PHP highlighting as it causes errors
                                                if (lang === 'php') {
                                                    console.log('Skipping PHP highlighting due to known issues');
                                                    return;
                                                }

                                                // Only highlight if the language grammar is loaded
                                                if (Prism.languages[lang]) {
                                                    Prism.highlightElement(block);
                                                } else {
                                                    console.log(`Language grammar for ${lang} not loaded, using plaintext`);
                                                    block.className = 'language-plaintext';
                                                    Prism.highlightElement(block);
                                                }
                                            } else {
                                                // No language specified, use plaintext
                                                block.className = 'language-plaintext';
                                                Prism.highlightElement(block);
                                            }
                                        } catch (e) {
                                            console.warn('Error highlighting code block:', e);
                                        }
                                    });
                                }
                            }
                        });
                    }
                });
            });

            // Start observing the document with the configured parameters
            observer.observe(document.body, { childList: true, subtree: true });
        }
    </script>
        <script src="js/config.js" onerror="console.error('Failed to load config.js')"></script>
        <script src="js/utils.js" onerror="console.error('Failed to load utils.js')"></script>
        <script src="js/uiElements.js" onerror="console.error('Failed to load uiElements.js')"></script>
        <script src="js/systemPrompt_nsfwbase.js" onerror="console.error('Failed to load systemPrompt_nsfwbase.js')"></script>
        <script src="js/systemPromptManager.js" onerror="console.error('Failed to load systemPromptManager.js')"></script>
        <script src="js/settings.js" onerror="console.error('Failed to load settings.js')"></script>
        <script src="js/rendering.js" onerror="console.error('Failed to load rendering.js')"></script>
        <script src="js/thinkingUI.js" onerror="console.error('Failed to load thinkingUI.js')"></script>
        <script src="js/messageHandler.js" onerror="console.error('Failed to load messageHandler.js')"></script>
        <script src="js/api.js" onerror="console.error('Failed to load api.js')"></script>
        <script src="js/uiInteractions.js" onerror="console.error('Failed to load uiInteractions.js')"></script>
        <script src="js/main.js" onerror="console.error('Failed to load main.js')"></script>
        <script>
            // Script validation block
            document.addEventListener('DOMContentLoaded', () => {
                const criticalFunctions = ['initializeUIInteractionsDependencies', 'sendMessage', 'loadSettings'];
                criticalFunctions.forEach(fn => {
                    if (typeof window[fn] !== 'function') {
                        console.error(`Critical function ${fn} is not available after scripts have loaded.`);
                        // Optionally, display a user-facing error message
                        const body = document.querySelector('body');
                        if (body) {
                            const errorDiv = document.createElement('div');
                            errorDiv.innerHTML = `
                                <div style="position: fixed; top: 0; left: 0; width: 100%; background-color: #ff4444; color: white; text-align: center; padding: 10px; z-index: 10000;">
                                    A critical error occurred. The application may not function correctly. Please check the console for details.
                                </div>
                            `;
                            body.prepend(errorDiv);
                        }
                    }
                });
            });
        </script>
    </body>
</html>
