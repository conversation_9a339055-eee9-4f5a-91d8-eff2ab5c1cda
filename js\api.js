// Global AbortController storage for stream cancellation
const activeStreams = new Map();

// Provider-specific error parsing functions
function parseProviderError(provider, errorResponse, statusCode) {
    let errorMessage = `${statusCode} ${getStatusText(statusCode)}`;
    let errorType = 'api_error';
    
    try {
        const errorData = typeof errorResponse === 'string' ? JSON.parse(errorResponse) : errorResponse;
        
        switch (provider) {
            case 'OPENROUTER':
                if (errorData.error) {
                    if (typeof errorData.error === 'string') {
                        errorMessage = errorData.error;
                    } else if (errorData.error.message) {
                        errorMessage = errorData.error.message;
                        errorType = errorData.error.type || 'api_error';
                    }
                    
                    // Handle specific OpenRouter error codes
                    if (statusCode === 401) {
                        errorMessage = 'Invalid OpenRouter API key. Please check your credentials.';
                        errorType = 'authentication_error';
                    } else if (statusCode === 402) {
                        errorMessage = 'Insufficient credits on OpenRouter account.';
                        errorType = 'billing_error';
                    } else if (statusCode === 429) {
                        errorMessage = 'OpenRouter rate limit exceeded. Please try again later.';
                        errorType = 'rate_limit_error';
                    }
                }
                break;
                
            case 'CHUTES_AI':
                if (errorData.error) {
                    if (typeof errorData.error === 'string') {
                        errorMessage = errorData.error;
                    } else if (errorData.error.message) {
                        errorMessage = errorData.error.message;
                        errorType = errorData.error.type || 'api_error';
                    }
                    
                    // Handle specific Chutes AI error codes
                    if (statusCode === 401) {
                        errorMessage = 'Invalid Chutes AI API key. Please check your credentials.';
                        errorType = 'authentication_error';
                    } else if (statusCode === 429) {
                        errorMessage = 'Chutes AI rate limit exceeded. Please try again later.';
                        errorType = 'rate_limit_error';
                    }
                }
                break;
                
            default:
                // Generic error parsing
                if (errorData.error) {
                    errorMessage = errorData.error.message || errorData.error || errorMessage;
                }
        }
    } catch (parseError) {
        // If JSON parsing fails, use the raw error response
        if (typeof errorResponse === 'string' && errorResponse.trim()) {
            errorMessage += ` - ${errorResponse}`;
        }
    }
    
    return {
        message: errorMessage,
        type: errorType,
        provider: provider,
        statusCode: statusCode
    };
}

// Helper function to get HTTP status text
function getStatusText(statusCode) {
    const statusTexts = {
        400: 'Bad Request',
        401: 'Unauthorized',
        402: 'Payment Required',
        403: 'Forbidden',
        404: 'Not Found',
        429: 'Too Many Requests',
        500: 'Internal Server Error',
        502: 'Bad Gateway',
        503: 'Service Unavailable',
        504: 'Gateway Timeout'
    };
    return statusTexts[statusCode] || 'Unknown Error';
}

// Enhanced error display with provider context
function showProviderError(error, provider) {
    const providerConfig = API_PROVIDERS[provider];
    const providerName = providerConfig ? providerConfig.name : provider;
    
    let alertMessage = `${providerName}: ${error.message}`;
    let alertType = 'error';
    
    // Customize alert based on error type
    switch (error.type) {
        case 'authentication_error':
            alertMessage = `${providerName} Authentication Error: ${error.message}`;
            break;
        case 'billing_error':
            alertMessage = `${providerName} Billing Error: ${error.message}`;
            break;
        case 'rate_limit_error':
            alertMessage = `${providerName} Rate Limit: ${error.message}`;
            alertType = 'warning';
            break;
        case 'network_error':
            alertMessage = `${providerName} Network Error: ${error.message}`;
            break;
        default:
            alertMessage = `${providerName} Error: ${error.message}`;
    }
    
    if (typeof showAlert === 'function') {
        showAlert(alertMessage, alertType);
    }
    
    console.error(`${providerName} Error Details:`, error);
}

// Check if a provider is available and has valid credentials
function isProviderAvailable(provider) {
    const providerConfig = API_PROVIDERS[provider];
    if (!providerConfig) {
        return false;
    }
    
    const token = getProviderToken(provider);
    return isValidToken(token, provider);
}

// Enhanced error recovery with retry logic
function handleErrorRecovery(provider, error, requestData, loadingId, retryCount = 0) {
    const maxRetries = 2;
    
    // Don't retry for authentication errors or user cancellation
    if (error.type === 'authentication_error' || error.name === 'AbortError' || retryCount >= maxRetries) {
        return false;
    }
    
    // Retry for rate limit errors with exponential backoff
    if (error.type === 'rate_limit_error' && retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s...
        
        if (typeof showAlert === 'function') {
            showAlert(`Rate limited. Retrying in ${delay/1000} seconds...`, 'warning');
        }
        
        setTimeout(() => {
            console.log(`Retrying ${provider} request (attempt ${retryCount + 1})`);
            createApiCall(provider, requestData, loadingId);
        }, delay);
        
        return true;
    }
    
    // For network errors, try once more after a short delay
    if (error.type === 'network_error' && retryCount === 0) {
        if (typeof showAlert === 'function') {
            showAlert('Network error detected. Retrying...', 'warning');
        }
        
        setTimeout(() => {
            console.log(`Retrying ${provider} request after network error`);
            createApiCall(provider, requestData, loadingId);
        }, 2000);
        
        return true;
    }
    
    return false;
}

// Graceful fallback mechanism when provider is unavailable
function handleProviderFallback(currentProvider, error, requestData, loadingId) {
    console.warn(`Provider ${currentProvider} failed:`, error);
    
    // Don't attempt fallback for authentication errors or user cancellation
    if (error.type === 'authentication_error' || error.name === 'AbortError') {
        return false;
    }
    
    // Get alternative provider
    const availableProviders = Object.keys(API_PROVIDERS).filter(p => p !== currentProvider);
    
    if (availableProviders.length === 0) {
        console.error('No alternative providers available for fallback');
        return false;
    }
    
    // Try to find a provider with valid credentials
    for (const provider of availableProviders) {
        const token = getProviderToken(provider);
        const hasValidToken = isValidToken(token, provider);
        
        if (hasValidToken) {
            console.log(`Attempting fallback to provider: ${provider}`);
            
            // Update UI to show fallback attempt
            if (typeof showAlert === 'function') {
                const providerName = API_PROVIDERS[provider].name;
                showAlert(`Switching to ${providerName} due to ${API_PROVIDERS[currentProvider].name} error...`, 'info');
            }
            
            // Update model if needed for the fallback provider
            const currentModel = requestData.model;
            const fallbackModel = getModelWithFallback(provider, currentModel);
            if (fallbackModel && fallbackModel !== currentModel) {
                requestData.model = fallbackModel;
                console.log(`Using fallback model: ${fallbackModel}`);
            }
            
            // Attempt the API call with the fallback provider
            setTimeout(() => {
                createApiCall(provider, requestData, loadingId);
            }, 1000); // Small delay to show the fallback message
            
            return true;
        }
    }
    
    console.warn('No providers with valid credentials available for fallback');
    return false;
}

// API Abstraction Layer - Factory function that routes to appropriate provider
function createApiCall(provider, requestData, loadingId) {
    const providerConfig = typeof API_PROVIDERS !== 'undefined' ? API_PROVIDERS[provider] : null;
    
    if (!providerConfig) {
        if (typeof showAlert === 'function') showAlert('Invalid API provider selected.', 'error');
        if (typeof completeLoadingIndicator === 'function') {
            completeLoadingIndicator(loadingId, "Error: Invalid API provider.");
        }
        return;
    }

    // Create AbortController for this request
    const abortController = new AbortController();
    activeStreams.set(loadingId, abortController);

    // Route to provider-specific implementation
    switch (provider) {
        case 'CHUTES_AI':
            return callChuteAI(requestData, loadingId, providerConfig, abortController);
        case 'OPENROUTER':
            return callOpenRouter(requestData, loadingId, providerConfig, abortController);
        default:
            if (typeof showAlert === 'function') showAlert('Unsupported API provider.', 'error');
            if (typeof completeLoadingIndicator === 'function') {
                completeLoadingIndicator(loadingId, "Error: Unsupported API provider.");
            }
            activeStreams.delete(loadingId);
            return;
    }
}

// Function to cancel a specific stream
function cancelStream(loadingId) {
    const abortController = activeStreams.get(loadingId);
    if (abortController) {
        console.log(`Cancelling stream for loadingId: ${loadingId}`);
        abortController.abort();
        activeStreams.delete(loadingId);
        
        // Clean up the loading indicator
        if (typeof completeLoadingIndicator === 'function') {
            completeLoadingIndicator(loadingId, "[Stream cancelled by user]");
        }
        
        return true;
    }
    return false;
}

// Function to cancel all active streams
function cancelAllStreams() {
    console.log(`Cancelling ${activeStreams.size} active streams`);
    activeStreams.forEach((abortController, loadingId) => {
        abortController.abort();
        if (typeof completeLoadingIndicator === 'function') {
            completeLoadingIndicator(loadingId, "[Stream cancelled by user]");
        }
    });
    activeStreams.clear();
}

// Cleanup function to be called on page unload or chat clear
function cleanupActiveStreams() {
    if (activeStreams.size > 0) {
        console.log('Cleaning up active streams on page unload/chat clear');
        cancelAllStreams();
    }
}

// Chutes AI specific implementation
function callChuteAI(requestData, loadingId, providerConfig, abortController) {
    const token = getProviderToken('CHUTES_AI');
    const endpoint = providerConfig.endpoint;

    const thinkingModels = ["deepseek-ai/DeepSeek-R1", "Qwen/Qwen3-235B-A22B", "deepseek-ai/DeepSeek-R1-0528", "tngtech/deepseek-r1t2-chimera:free", "deepseek/deepseek-r1-0528:free", "qwen/qwen3-235b-a22b:free"];
    const isThinkingModel = thinkingModels.includes(requestData.model);
    console.log(`callAPI: isThinkingModel = ${isThinkingModel} for model ${requestData.model}`);

    // Scope variables for thinking model state to this callAPI invocation
    let isInsideThinkTag = false;
    let thinkingStartTime = null;
    let accumulatedThinkContent = "";
    let finalAnswerContent = "";
    let answerId = null; // This will be the ID for the final answer bubble
    let bufferForTagDetection = "";
    let thinkingSpoilerCreated = false;


    if (isThinkingModel) {
        console.log("Creating initial thinking spoiler for thinking model");
        // Ensure addOrUpdateThinkingSpoilerUI is accessible
        if (typeof addOrUpdateThinkingSpoilerUI === 'function') {
            addOrUpdateThinkingSpoilerUI(loadingId, "Waiting for model to start thinking...", null);
        } else {
            console.error("addOrUpdateThinkingSpoilerUI function not found in callAPI");
        }
    }

    if (!isValidToken(token, 'CHUTES_AI')) {
        if (typeof showAlert === 'function') showAlert('API token missing.', 'error');
        // Ensure completeLoadingIndicator is accessible
        if (typeof completeLoadingIndicator === 'function') {
            completeLoadingIndicator(loadingId, "Error: API token is required.");
        }
        activeStreams.delete(loadingId);
        return;
    }

    fetch(endpoint, {
        method: "POST",
        headers: {
            "Authorization": "Bearer " + token,
            "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: abortController.signal
    })
    .then(response => {
        if (!response.ok) {
            if (!requestData.stream) {
                return response.text().then(text => {
                    const parsedError = parseProviderError('CHUTES_AI', text, response.status);
                    showProviderError(parsedError, 'CHUTES_AI');
                    
                    // Attempt fallback if appropriate
                    if (!handleProviderFallback('CHUTES_AI', parsedError, requestData, loadingId)) {
                        if (typeof completeLoadingIndicator === 'function') {
                            completeLoadingIndicator(loadingId, `Error: ${parsedError.message}`);
                        }
                    }
                    throw new Error(parsedError.message);
                });
            } else {
                const parsedError = parseProviderError('CHUTES_AI', '', response.status);
                showProviderError(parsedError, 'CHUTES_AI');
                
                // Attempt fallback if appropriate
                if (!handleProviderFallback('CHUTES_AI', parsedError, requestData, loadingId)) {
                    if (typeof completeLoadingIndicator === 'function') {
                        completeLoadingIndicator(loadingId, `Error: ${parsedError.message}`);
                    }
                }
                throw new Error(parsedError.message);
            }
        }
        if (requestData.stream) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let partialLine = '';
            let fullResponse = ""; // For non-thinking models

            function readStream() {
                reader.read().then(({done, value}) => {
                    if (done) {
                        // Clean up the active stream reference on successful completion
                        activeStreams.delete(loadingId);
                        
                        if (isThinkingModel && answerId) {
                            if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(answerId, finalAnswerContent);
                            document.querySelectorAll('.message-chunk').forEach(chunk => chunk.remove());
                        } else if (!isThinkingModel) {
                            if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, fullResponse);
                        }
                        return;
                    }

                    const chunk = decoder.decode(value, {stream: true});
                    const lines = (partialLine + chunk).split('\n');
                    partialLine = lines.pop() || '';

                    for (const line of lines) {
                        if (!line.trim() || line === 'data: [DONE]') {
                            if (line === 'data: [DONE]') {
                                if (isThinkingModel && answerId) {
                                     if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(answerId, finalAnswerContent);
                                     document.querySelectorAll('.message-chunk').forEach(chunk => chunk.remove());
                                } else if (!isThinkingModel) {
                                     if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, fullResponse);
                                }
                            }
                            continue;
                        }

                        if (line.startsWith('data: ')) {
                            try {
                                const jsonData = line.substring(6);
                                const data = JSON.parse(jsonData);

                                if (data.choices && data.choices[0].delta && data.choices[0].delta.content) {
                                    const chunkContent = data.choices[0].delta.content;
                                    bufferForTagDetection = (bufferForTagDetection.slice(-20) + chunkContent);

                                    if (isThinkingModel) {
                                        if (!isInsideThinkTag && bufferForTagDetection.includes('<think>')) {
                                            isInsideThinkTag = true;
                                            thinkingStartTime = Date.now();
                                            const thinkTagIndex = bufferForTagDetection.indexOf('<think>');
                                            const tagInCurrentChunk = chunkContent.includes('<think>');
                                            let contentAfterTag = tagInCurrentChunk ? chunkContent.substring(chunkContent.indexOf('<think>') + 7) : chunkContent;
                                            accumulatedThinkContent += contentAfterTag;

                                            if (!thinkingSpoilerCreated) {
                                                if (typeof addOrUpdateThinkingSpoilerUI === 'function') addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, null);
                                                thinkingSpoilerCreated = true;
                                                const loadingElement = document.getElementById(loadingId);
                                                if (loadingElement) loadingElement.style.display = 'none';
                                            }
                                        } else if (isInsideThinkTag && bufferForTagDetection.includes('</think>')) {
                                            const tagInCurrentChunk = chunkContent.includes('</think>');
                                            let contentBeforeEndTag = tagInCurrentChunk ? chunkContent.substring(0, chunkContent.indexOf('</think>')) : chunkContent;
                                            accumulatedThinkContent += contentBeforeEndTag;
                                            
                                            const thinkingEndTime = Date.now();
                                            const thinkingDuration = (thinkingEndTime - thinkingStartTime) / 1000.0;

                                            if (thinkingSpoilerCreated && typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                                addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, thinkingDuration);
                                            } else if (typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                                addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, thinkingDuration);
                                                thinkingSpoilerCreated = true;
                                                const loadingElement = document.getElementById(loadingId);
                                                if (loadingElement) loadingElement.style.display = 'none';
                                            }
                                            
                                            isInsideThinkTag = false;
                                            let contentAfterEndTag = tagInCurrentChunk ? chunkContent.substring(chunkContent.indexOf('</think>') + 9) : "";
                                            finalAnswerContent += contentAfterEndTag;

                                            if (contentAfterEndTag.trim()) {
                                                answerId = "answer-" + Date.now();
                                                if (typeof addLoadingIndicator === 'function') addLoadingIndicator(answerId, true);
                                                if (typeof updateStreamingContent === 'function') updateStreamingContent(answerId, contentAfterEndTag);
                                            }
                                        } else if (isInsideThinkTag) {
                                            accumulatedThinkContent += chunkContent;
                                            if (thinkingSpoilerCreated && typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                                addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, null);
                                            } else if (typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                                addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, null);
                                                thinkingSpoilerCreated = true;
                                                const loadingElement = document.getElementById(loadingId);
                                                if (loadingElement) loadingElement.style.display = 'none';
                                            }
                                        } else { // Not inside think tag, and no start/end tag detected in this chunk
                                            if (!answerId) { // Create answer bubble if not already done
                                                answerId = "answer-" + Date.now();
                                                if (typeof addLoadingIndicator === 'function') addLoadingIndicator(answerId, true);
                                            }
                                            finalAnswerContent += chunkContent;
                                            if (typeof updateStreamingContent === 'function') updateStreamingContent(answerId, chunkContent);
                                            document.querySelectorAll('.message-chunk').forEach(elChunk => {
                                                if (elChunk.id !== answerId) elChunk.remove();
                                            });
                                        }
                                    } else { // Not a thinking model
                                        fullResponse += chunkContent;
                                        if (typeof updateStreamingContent === 'function') updateStreamingContent(loadingId, chunkContent);
                                    }
                                }
                            } catch (e) {
                                console.error('Chutes AI - Error parsing stream chunk:', line, e);
                                const errorMsg = `\n\n[Chutes AI - Error parsing stream data: ${e.message}]`;
                                if (isThinkingModel && answerId && typeof updateStreamingContent === 'function') {
                                    updateStreamingContent(answerId, errorMsg);
                                } else if (typeof updateStreamingContent === 'function') {
                                    updateStreamingContent(loadingId, errorMsg);
                                }
                            }
                        }
                    }
                    readStream();
                }).catch(streamError => {
                    // Clean up the active stream reference
                    activeStreams.delete(loadingId);
                    
                    if (streamError.name === 'AbortError') {
                        console.log('Chutes AI stream cancelled by user');
                        // Don't show error for user-initiated cancellation
                        return;
                    }
                    
                    console.error('Chutes AI stream reading error:', streamError);
                    const errorMsg = `\n\n[Chutes AI - Stream reading error: ${streamError.message}]`;
                    if (isThinkingModel && answerId && typeof completeLoadingIndicator === 'function') {
                        completeLoadingIndicator(answerId, finalAnswerContent + errorMsg);
                        document.querySelectorAll('.message-chunk').forEach(chunk => chunk.remove());
                    } else if (typeof completeLoadingIndicator === 'function') {
                        completeLoadingIndicator(loadingId, fullResponse + errorMsg);
                    }
                    if (typeof showAlert === 'function') showAlert(`Chutes AI Stream Error: ${streamError.message}`, 'error');
                });
            }
            readStream(); // Start reading
        } else { // Non-streaming
            return response.json();
        }
    })
    .then(data => {
        if (!requestData.stream && data) {
            if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
                 if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, data.choices[0].message.content);
            } else {
                 console.error("Unexpected non-stream response format:", data);
                 if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, "[Received unexpected data format]");
                 if (typeof showAlert === 'function') showAlert("Received unexpected API response format", 'error');
            }
        }
    })
    .catch(error => {
        // Clean up the active stream reference
        activeStreams.delete(loadingId);
        
        if (error.name === 'AbortError') {
            console.log('Chutes AI request cancelled by user');
            // Don't show error for user-initiated cancellation
            return;
        }
        
        console.error('Chutes AI Call Error:', error);
        
        // Create error object for consistent handling
        const errorObj = {
            message: error.message,
            type: error.name === 'TypeError' && error.message.includes('fetch') ? 'network_error' : 'api_error',
            provider: 'CHUTES_AI',
            statusCode: null
        };
        
        // Try error recovery first, then fallback if recovery fails
        if (!handleErrorRecovery('CHUTES_AI', errorObj, requestData, loadingId)) {
            if (!handleProviderFallback('CHUTES_AI', errorObj, requestData, loadingId)) {
                if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, `Error: ${error.message}`);
                showProviderError(errorObj, 'CHUTES_AI');
            }
        }
    });
}

// OpenRouter specific implementation
function callOpenRouter(requestData, loadingId, providerConfig, abortController) {
    const token = getProviderToken('OPENROUTER');
    const endpoint = providerConfig.endpoint;

    const thinkingModels = ["deepseek/deepseek-r1", "deepseek-ai/DeepSeek-R1", "Qwen/Qwen3-235B-A22B", "deepseek-ai/DeepSeek-R1-0528", "tngtech/deepseek-r1t2-chimera:free", "deepseek/deepseek-r1-0528:free", "qwen/qwen3-235b-a22b:free", "moonshotai/kimi-k2:free", "deepseek/deepseek-chat-v3-0324:free"];
    const isThinkingModel = thinkingModels.includes(requestData.model);
    console.log(`callOpenRouter: isThinkingModel = ${isThinkingModel} for model ${requestData.model}`);

    // Scope variables for thinking model state to this callOpenRouter invocation
    let isInsideThinkTag = false;
    let thinkingStartTime = null;
    let accumulatedThinkContent = "";
    let finalAnswerContent = "";
    let answerId = null;
    let bufferForTagDetection = "";
    let thinkingSpoilerCreated = false;

    if (isThinkingModel) {
        console.log("Creating initial thinking spoiler for thinking model (OpenRouter)");
        if (typeof addOrUpdateThinkingSpoilerUI === 'function') {
            addOrUpdateThinkingSpoilerUI(loadingId, "Waiting for model to start thinking...", null);
        } else {
            console.error("addOrUpdateThinkingSpoilerUI function not found in callOpenRouter");
        }
    }

    if (!isValidToken(token, 'OPENROUTER')) {
        if (typeof showAlert === 'function') showAlert('OpenRouter API token missing.', 'error');
        if (typeof completeLoadingIndicator === 'function') {
            completeLoadingIndicator(loadingId, "Error: OpenRouter API token is required.");
        }
        activeStreams.delete(loadingId);
        return;
    }

    fetch(endpoint, {
        method: "POST",
        headers: {
            "Authorization": "Bearer " + token,
            "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: abortController.signal
    })
    .then(response => {
        if (!response.ok) {
            if (!requestData.stream) {
                return response.text().then(text => {
                    const parsedError = parseProviderError('OPENROUTER', text, response.status);
                    showProviderError(parsedError, 'OPENROUTER');
                    
                    // Attempt fallback if appropriate
                    if (!handleProviderFallback('OPENROUTER', parsedError, requestData, loadingId)) {
                        if (typeof completeLoadingIndicator === 'function') {
                            completeLoadingIndicator(loadingId, `Error: ${parsedError.message}`);
                        }
                    }
                    throw new Error(parsedError.message);
                });
            } else {
                const parsedError = parseProviderError('OPENROUTER', '', response.status);
                showProviderError(parsedError, 'OPENROUTER');
                
                // Attempt fallback if appropriate
                if (!handleProviderFallback('OPENROUTER', parsedError, requestData, loadingId)) {
                    if (typeof completeLoadingIndicator === 'function') {
                        completeLoadingIndicator(loadingId, `Error: ${parsedError.message}`);
                    }
                }
                throw new Error(parsedError.message);
            }
        }
        if (requestData.stream) {
            return handleOpenRouterStream(response, loadingId, isThinkingModel);
        } else {
            return response.json();
        }
    })
    .then(data => {
        if (!requestData.stream && data) {
            if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
                if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, data.choices[0].message.content);
            } else {
                console.error("Unexpected OpenRouter non-stream response format:", data);
                if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, "[Received unexpected data format]");
                if (typeof showAlert === 'function') showAlert("Received unexpected OpenRouter API response format", 'error');
            }
        }
    })
    .catch(error => {
        // Clean up the active stream reference
        activeStreams.delete(loadingId);
        
        if (error.name === 'AbortError') {
            console.log('OpenRouter request cancelled by user');
            // Don't show error for user-initiated cancellation
            return;
        }
        
        console.error('OpenRouter API Call Error:', error);
        
        // Create error object for consistent handling
        const errorObj = {
            message: error.message,
            type: error.name === 'TypeError' && error.message.includes('fetch') ? 'network_error' : 'api_error',
            provider: 'OPENROUTER',
            statusCode: null
        };
        
        // Try error recovery first, then fallback if recovery fails
        if (!handleErrorRecovery('OPENROUTER', errorObj, requestData, loadingId)) {
            if (!handleProviderFallback('OPENROUTER', errorObj, requestData, loadingId)) {
                if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, `Error: ${error.message}`);
                showProviderError(errorObj, 'OPENROUTER');
            }
        }
    });
}

// OpenRouter streaming handler - handles OpenRouter SSE format including comments
function handleOpenRouterStream(response, loadingId, isThinkingModel) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let partialLine = '';
    let fullResponse = "";

    // Thinking model state variables
    let isInsideThinkTag = false;
    let thinkingStartTime = null;
    let accumulatedThinkContent = "";
    let finalAnswerContent = "";
    let answerId = null;
    let bufferForTagDetection = "";
    let thinkingSpoilerCreated = false;

    function readStream() {
        reader.read().then(({done, value}) => {
            if (done) {
                // Clean up the active stream reference on successful completion
                activeStreams.delete(loadingId);
                
                if (isThinkingModel && answerId) {
                    if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(answerId, finalAnswerContent);
                    document.querySelectorAll('.message-chunk').forEach(chunk => chunk.remove());
                } else if (!isThinkingModel) {
                    if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, fullResponse);
                }
                return;
            }

            const chunk = decoder.decode(value, {stream: true});
            const lines = (partialLine + chunk).split('\n');
            partialLine = lines.pop() || '';

            for (const line of lines) {
                // Handle OpenRouter comments (: OPENROUTER PROCESSING)
                if (line.startsWith(':')) {
                    console.log('OpenRouter comment:', line);
                    continue;
                }

                if (!line.trim() || line === 'data: [DONE]') {
                    if (line === 'data: [DONE]') {
                        if (isThinkingModel && answerId) {
                            if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(answerId, finalAnswerContent);
                            document.querySelectorAll('.message-chunk').forEach(chunk => chunk.remove());
                        } else if (!isThinkingModel) {
                            if (typeof completeLoadingIndicator === 'function') completeLoadingIndicator(loadingId, fullResponse);
                        }
                    }
                    continue;
                }

                if (line.startsWith('data: ')) {
                    try {
                        const jsonData = line.substring(6);
                        const data = JSON.parse(jsonData);

                        if (data.choices && data.choices[0].delta && data.choices[0].delta.content) {
                            const chunkContent = data.choices[0].delta.content;
                            bufferForTagDetection = (bufferForTagDetection.slice(-20) + chunkContent);

                            if (isThinkingModel) {
                                if (!isInsideThinkTag && bufferForTagDetection.includes('<think>')) {
                                    isInsideThinkTag = true;
                                    thinkingStartTime = Date.now();
                                    const tagInCurrentChunk = chunkContent.includes('<think>');
                                    let contentAfterTag = tagInCurrentChunk ? chunkContent.substring(chunkContent.indexOf('<think>') + 7) : chunkContent;
                                    accumulatedThinkContent += contentAfterTag;

                                    if (!thinkingSpoilerCreated) {
                                        if (typeof addOrUpdateThinkingSpoilerUI === 'function') addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, null);
                                        thinkingSpoilerCreated = true;
                                        const loadingElement = document.getElementById(loadingId);
                                        if (loadingElement) loadingElement.style.display = 'none';
                                    }
                                } else if (isInsideThinkTag && bufferForTagDetection.includes('</think>')) {
                                    const tagInCurrentChunk = chunkContent.includes('</think>');
                                    let contentBeforeEndTag = tagInCurrentChunk ? chunkContent.substring(0, chunkContent.indexOf('</think>')) : chunkContent;
                                    accumulatedThinkContent += contentBeforeEndTag;
                                    
                                    const thinkingEndTime = Date.now();
                                    const thinkingDuration = (thinkingEndTime - thinkingStartTime) / 1000.0;

                                    if (thinkingSpoilerCreated && typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                        addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, thinkingDuration);
                                    } else if (typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                        addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, thinkingDuration);
                                        thinkingSpoilerCreated = true;
                                        const loadingElement = document.getElementById(loadingId);
                                        if (loadingElement) loadingElement.style.display = 'none';
                                    }
                                    
                                    isInsideThinkTag = false;
                                    let contentAfterEndTag = tagInCurrentChunk ? chunkContent.substring(chunkContent.indexOf('</think>') + 9) : "";
                                    finalAnswerContent += contentAfterEndTag;

                                    if (contentAfterEndTag.trim()) {
                                        answerId = "answer-" + Date.now();
                                        if (typeof addLoadingIndicator === 'function') addLoadingIndicator(answerId, true);
                                        if (typeof updateStreamingContent === 'function') updateStreamingContent(answerId, contentAfterEndTag);
                                    }
                                } else if (isInsideThinkTag) {
                                    accumulatedThinkContent += chunkContent;
                                    if (thinkingSpoilerCreated && typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                        addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, null);
                                    } else if (typeof addOrUpdateThinkingSpoilerUI === 'function') {
                                        addOrUpdateThinkingSpoilerUI(loadingId, accumulatedThinkContent, null);
                                        thinkingSpoilerCreated = true;
                                        const loadingElement = document.getElementById(loadingId);
                                        if (loadingElement) loadingElement.style.display = 'none';
                                    }
                                } else {
                                    if (!answerId) {
                                        answerId = "answer-" + Date.now();
                                        if (typeof addLoadingIndicator === 'function') addLoadingIndicator(answerId, true);
                                    }
                                    finalAnswerContent += chunkContent;
                                    if (typeof updateStreamingContent === 'function') updateStreamingContent(answerId, chunkContent);
                                    document.querySelectorAll('.message-chunk').forEach(elChunk => {
                                        if (elChunk.id !== answerId) elChunk.remove();
                                    });
                                }
                            } else {
                                fullResponse += chunkContent;
                                if (typeof updateStreamingContent === 'function') updateStreamingContent(loadingId, chunkContent);
                            }
                        }
                    } catch (e) {
                        console.error('OpenRouter - Error parsing stream chunk:', line, e);
                        const errorMsg = `\n\n[OpenRouter - Error parsing stream data: ${e.message}]`;
                        if (isThinkingModel && answerId && typeof updateStreamingContent === 'function') {
                            updateStreamingContent(answerId, errorMsg);
                        } else if (typeof updateStreamingContent === 'function') {
                            updateStreamingContent(loadingId, errorMsg);
                        }
                    }
                }
            }
            readStream();
        }).catch(streamError => {
            // Clean up the active stream reference
            activeStreams.delete(loadingId);
            
            if (streamError.name === 'AbortError') {
                console.log('OpenRouter stream cancelled by user');
                // Don't show error for user-initiated cancellation
                return;
            }
            
            console.error('OpenRouter stream reading error:', streamError);
            const errorMsg = `\n\n[OpenRouter - Stream reading error: ${streamError.message}]`;
            if (isThinkingModel && answerId && typeof completeLoadingIndicator === 'function') {
                completeLoadingIndicator(answerId, finalAnswerContent + errorMsg);
                document.querySelectorAll('.message-chunk').forEach(chunk => chunk.remove());
            } else if (typeof completeLoadingIndicator === 'function') {
                completeLoadingIndicator(loadingId, fullResponse + errorMsg);
            }
            if (typeof showAlert === 'function') showAlert(`OpenRouter Stream Error: ${streamError.message}`, 'error');
        });
    }
    readStream();
}

// Main API Call Function - uses the factory pattern
function callAPI(requestData, loadingId) {
    const selectedProvider = apiProviderSelect.value || (typeof getDefaultProvider === 'function' ? getDefaultProvider() : 'CHUTES_AI');
    
    // Validate the model before making the API call
    if (requestData.model && typeof validateModel === 'function') {
        const validation = validateModel(selectedProvider, requestData.model);
        if (!validation.valid) {
            console.warn(`Invalid model for API call: ${validation.error}`);
            
            // Try to get a fallback model
            if (typeof getModelWithFallback === 'function') {
                const fallbackModel = getModelWithFallback(selectedProvider, requestData.model);
                if (fallbackModel) {
                    console.log(`Using fallback model: ${fallbackModel}`);
                    requestData.model = fallbackModel;
                    
                    // Update the UI to reflect the fallback model
                    if (modelSelect && modelSelect.value !== fallbackModel) {
                        modelSelect.value = fallbackModel;
                    }
                } else {
                    if (typeof showAlert === 'function') showAlert('No valid model available for the selected provider.', 'error');
                    if (typeof completeLoadingIndicator === 'function') {
                        completeLoadingIndicator(loadingId, "Error: No valid model available.");
                    }
                    return;
                }
            }
        }
    }
    
    return createApiCall(selectedProvider, requestData, loadingId);
}

function testConnection() {
    const selectedProvider = apiProviderSelect.value || (typeof getDefaultProvider === 'function' ? getDefaultProvider() : 'CHUTES_AI');
    return createTestConnection(selectedProvider);
}

// Provider-agnostic test connection factory
function createTestConnection(provider) {
    const providerConfig = typeof API_PROVIDERS !== 'undefined' ? API_PROVIDERS[provider] : null;
    
    if (!providerConfig) {
        if (typeof showAlert === 'function') showAlert('Invalid API provider selected.', 'error');
        return;
    }

    // Validate provider configuration
    if (typeof validateProviderConfig === 'function') {
        const configValidation = validateProviderConfig(provider);
        if (!configValidation.valid) {
            if (typeof showAlert === 'function') showAlert(`Provider configuration error: ${configValidation.error}`, 'error');
            return;
        }
    }
    
    // Get the appropriate token based on provider
    const token = getProviderToken(provider);
    
    if (!isValidToken(token, provider)) {
        if (typeof showAlert === 'function') showAlert(`${providerConfig.name} API token missing.`, 'error');
        return;
    }

    // Validate selected model for the provider
    const selectedModel = modelSelect.value;
    if (typeof validateModel === 'function') {
        const modelValidation = validateModel(provider, selectedModel);
        if (!modelValidation.valid) {
            console.warn(`Model validation failed: ${modelValidation.error}`);
            // Try to get a fallback model
            const fallbackModel = typeof getModelWithFallback === 'function' ? getModelWithFallback(provider, selectedModel) : null;
            if (!fallbackModel) {
                if (typeof showAlert === 'function') showAlert(`No valid model available for ${providerConfig.name}`, 'error');
                return;
            }
        }
    }

    // Update UI to show testing state
    testConnectionBtn.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i> Testing ${providerConfig.name}...`;
    testConnectionBtn.disabled = true;

    // Create test request with provider-specific headers
    const headers = {
        "Authorization": "Bearer " + token,
        "Content-Type": "application/json"
    };

    // Add provider-specific headers if needed
    if (provider === 'OPENROUTER') {
        headers["HTTP-Referer"] = window.location.origin;
        headers["X-Title"] = "Nebula AI Interface";
    }

    const testModel = selectedModel || (typeof getDefaultModel === 'function' ? getDefaultModel(provider) : 'gpt-4o');

    fetch(providerConfig.endpoint, {
        method: "POST",
        headers: headers,
        body: JSON.stringify({
            model: testModel,
            messages: [{ role: "user", content: "Test connection." }],
            stream: false,
            max_tokens: 5,
            temperature: 0.1
        })
    })
    .then(response => {
        if (response.ok) {
            return response.json().then(data => {
                // Validate response format
                if (data.choices && data.choices[0] && data.choices[0].message) {
                    if (typeof showAlert === 'function') showAlert(`${providerConfig.name} connection successful!`, 'success');
                    testConnectionBtn.innerHTML = '<i class="fas fa-check mr-2"></i> Success';
                    console.log(`${providerConfig.name} test response:`, data);
                } else {
                    console.warn(`${providerConfig.name} returned unexpected response format:`, data);
                    if (typeof showAlert === 'function') showAlert(`${providerConfig.name} connection successful but response format unexpected`, 'warning');
                    testConnectionBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i> Warning';
                }
            });
        } else {
            return response.text().then(errorText => {
                // Use the enhanced provider-specific error parsing
                const parsedError = parseProviderError(provider, errorText, response.status);
                
                if (typeof showAlert === 'function') {
                    showAlert(`${providerConfig.name} connection failed: ${parsedError.message}`, 'error');
                }
                testConnectionBtn.innerHTML = '<i class="fas fa-times mr-2"></i> Failed';
                console.error(`${providerConfig.name} connection test failed:`, parsedError);
            });
        }
    })
    .catch(error => {
        console.error(`${providerConfig.name} Test Connection Error:`, error);
        
        // Create error object for consistent handling
        const errorObj = {
            message: error.message,
            type: error.name === 'TypeError' && error.message.includes('fetch') ? 'network_error' : 
                  error.name === 'AbortError' ? 'timeout_error' : 'api_error',
            provider: provider,
            statusCode: null
        };
        
        // Provide more specific error messages
        if (errorObj.type === 'network_error') {
            errorObj.message = 'Network error - check your internet connection';
        } else if (errorObj.type === 'timeout_error') {
            errorObj.message = 'Request timed out';
        }
        
        showProviderError(errorObj, provider);
        testConnectionBtn.innerHTML = '<i class="fas fa-times mr-2"></i> Error';
    })
    .finally(() => {
        // Reset button after delay
        setTimeout(() => {
            testConnectionBtn.innerHTML = '<i class="fas fa-plug mr-2"></i> Test Connection';
            testConnectionBtn.disabled = false;
        }, 2000);
    });
}

// Ensure functions are globally accessible
if (typeof window !== 'undefined') {
    window.callAPI = callAPI;
    window.testConnection = testConnection;
    window.cancelStream = cancelStream;
}
