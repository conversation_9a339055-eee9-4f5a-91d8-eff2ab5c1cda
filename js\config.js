// --- Configuration ---
// Default API key - should be set by user in the interface
// For development, you can set this in .env file as DEFAULT_API_KEY
const DEFAULT_API_KEY = "YOUR_DEFAULT_API_KEY_HERE";

// Default OpenRouter API key - should be set by user in the interface
// For development, you can set this in .env file as OPENROUTER_API_KEY
const DEFAULT_OPENROUTER_API_KEY = "YOUR_DEFAULT_OPENROUTER_API_KEY_HERE";

// Centralized function to get API token for a provider
function getProviderToken(provider) {
    if (provider === 'CHUTES_AI') {
        let token = apiToken.value.trim();
        if (!token && window.SERVER_DEFAULT_API_KEY) {
            token = window.SERVER_DEFAULT_API_KEY;
        }
        if (!token) {
            token = DEFAULT_API_KEY;
        }
        return token;
    } else if (provider === 'OPENROUTER') {
        let token = openrouterApiToken.value.trim();
        if (!token && window.SERVER_DEFAULT_OPENROUTER_API_KEY) {
            token = window.SERVER_DEFAULT_OPENROUTER_API_KEY;
        }
        if (!token) {
            token = DEFAULT_OPENROUTER_API_KEY;
        }
        return token;
    }
    return null;
}

// Centralized function to validate if a token is valid (not placeholder)
function isValidToken(token, provider) {
    if (!token) return false;

    const invalidTokens = [
        "YOUR_DEFAULT_API_KEY_HERE",
        "YOUR_DEFAULT_OPENROUTER_API_KEY_HERE"
    ];

    return !invalidTokens.includes(token);
}

// --- API Providers Configuration ---
const API_PROVIDERS = {
    CHUTES_AI: {
        name: 'Chutes AI',
        endpoint: 'https://llm.chutes.ai/v1/chat/completions',
        requiresAuth: true,
        models: [
            'deepseek-ai/deepseek-r1-0528',
            'gpt-4o',
            'claude-3-5-sonnet-20241022'
        ]
    },
    OPENROUTER: {
        name: 'OpenRouter.ai',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        requiresAuth: true,
        models: [
            'moonshotai/kimi-k2:free',
            'tngtech/deepseek-r1t2-chimera:free',
            'deepseek/deepseek-r1-0528:free',
            'qwen/qwen3-235b-a22b:free',
            'deepseek/deepseek-chat-v3-0324:free',
            'mistralai/mistral-small-3.2-24b-instruct:free',
            'cognitivecomputations/dolphin-mistral-24b-venice-edition:free',
            'arliai/qwq-32b-arliai-rpr-v1:free'
        ]
    }
};

// Provider validation functions
function validateProvider(provider) {
    if (!provider || typeof provider !== 'string') {
        return false;
    }
    return Object.keys(API_PROVIDERS).includes(provider);
}

function validateProviderConfig(providerKey) {
    const provider = API_PROVIDERS[providerKey];
    if (!provider) {
        return { valid: false, error: 'Provider not found' };
    }

    if (!provider.name || !provider.endpoint) {
        return { valid: false, error: 'Provider missing required configuration' };
    }

    if (!provider.endpoint.startsWith('https://')) {
        return { valid: false, error: 'Provider endpoint must use HTTPS' };
    }

    return { valid: true };
}

function getProviderModels(providerKey) {
    const provider = API_PROVIDERS[providerKey];
    return provider && provider.models ? provider.models : [];
}

function getDefaultProvider() {
    return 'CHUTES_AI';
}

function validateModel(providerKey, modelName) {
    if (!providerKey || !modelName) {
        return { valid: false, error: 'Provider and model name are required' };
    }

    const provider = API_PROVIDERS[providerKey];
    if (!provider) {
        return { valid: false, error: 'Provider not found' };
    }

    if (!provider.models || !Array.isArray(provider.models)) {
        return { valid: false, error: 'Provider has no models configured' };
    }

    const modelExists = provider.models.includes(modelName);
    if (!modelExists) {
        return { valid: false, error: `Model '${modelName}' not available for provider '${provider.name}'` };
    }

    return { valid: true };
}

function getDefaultModel(providerKey) {
    const provider = API_PROVIDERS[providerKey];
    if (!provider || !provider.models || provider.models.length === 0) {
        return null;
    }
    return provider.models[0];
}

function getModelWithFallback(providerKey, preferredModel) {
    // First try to validate the preferred model
    if (preferredModel) {
        const validation = validateModel(providerKey, preferredModel);
        if (validation.valid) {
            return preferredModel;
        }
    }

    // Fall back to default model for the provider
    const defaultModel = getDefaultModel(providerKey);
    if (defaultModel) {
        return defaultModel;
    }

    // Last resort: try to get any available model from any provider
    for (const [key, provider] of Object.entries(API_PROVIDERS)) {
        if (provider.models && provider.models.length > 0) {
            console.warn(`Falling back to model '${provider.models[0]}' from provider '${provider.name}'`);
            return provider.models[0];
        }
    }

    console.error('No models available from any provider');
    return null;
}
// --- End Configuration ---

// --- Model Display Names ---
const modelDisplayNames = {
    // Chutes AI Models
    'deepseek-ai/deepseek-r1-0528': 'DeepSeek R1 (Reasoning)',
    'gpt-4o': 'GPT-4o',
    'claude-3-5-sonnet-20241022': 'Claude 3.5 Sonnet',
    
    // OpenRouter Models
    'moonshotai/kimi-k2:free': 'Kimi K2 (Long Context)',
    'tngtech/deepseek-r1t2-chimera:free': 'DeepSeek R1 Chimera (Reasoning)',
    'deepseek/deepseek-r1-0528:free': 'DeepSeek R1 (Reasoning)',
    'qwen/qwen3-235b-a22b:free': 'Qwen 3 (235B)',
    'deepseek/deepseek-chat-v3-0324:free': 'DeepSeek Chat V3',
    'mistralai/mistral-small-3.2-24b-instruct:free': 'Mistral Small 3.2 (24B)',
    'cognitivecomputations/dolphin-mistral-24b-venice-edition:free': 'Dolphin Mistral Venice (24B)',
    'arliai/qwq-32b-arliai-rpr-v1:free': 'QwQ 32B (Math & Logic)'
};

/**
 * Get display name for a model
 * @param {string} modelId - Technical model ID
 * @returns {string} User-friendly display name
 */
function getModelDisplayName(modelId) {
    return modelDisplayNames[modelId] || modelId;
}

// --- Model Specific Configurations ---
const modelConfigurations = {
    'deepseek-ai/deepseek-r1-0528': { // DeepSeek R1 model with high token limit
        maxTokens: 161840
    },
    // OpenRouter models (including Kimi) support up to 65,536 tokens
    // No specific limit needed for moonshotai/kimi-k2:free - uses default 65k limit
    // Add other model-specific settings here in the future
    'default': {
        maxTokens: 32768 // Balanced default: 32k tokens for optimal performance
    }
};