// --- State Variables ---

// --- Configuration Loading ---
async function loadServerConfiguration() {
    try {
        const response = await fetch('/api/config');
        if (response.ok) {
            const config = await response.json();
            
            // Update global constants if they exist
            if (config.defaultApiKey && config.defaultApiKey !== "YOUR_DEFAULT_API_KEY_HERE") {
                window.SERVER_DEFAULT_API_KEY = config.defaultApiKey;
                console.log('Loaded Chutes AI key from server config');
            }
            if (config.defaultOpenrouterApiKey && config.defaultOpenrouterApiKey !== "YOUR_DEFAULT_OPENROUTER_API_KEY_HERE") {
                window.SERVER_DEFAULT_OPENROUTER_API_KEY = config.defaultOpenrouterApiKey;
                console.log('Loaded OpenRouter key from server config');
            }
            
            console.log('Server configuration loaded successfully');
            return config;
        } else {
            console.warn('Failed to load server configuration (HTTP error), using client-side defaults');
            return null;
        }
    } catch (error) {
        console.warn('Server not available, using client-side defaults only:', error.message);
        return null;
    }
}

// --- Main Send Message Function (Orchestrator) ---
function sendMessage() {
    const prompt = userInput.value.trim(); // userInput should be from uiElements.js
    if (!prompt) return;

    // Ensure addUserTextMessage is available (from messageHandler.js)
    if (typeof addUserTextMessage === 'function') {
        addUserTextMessage('user', prompt);
    } else {
        console.error("addUserTextMessage function not found in sendMessage.");
        return;
    }
    
    userInput.value = '';
    if (typeof updateCharCount === 'function') updateCharCount(); // updateCharCount from uiInteractions.js

    // Get the current provider and token using centralized logic
    const currentProvider = apiProviderSelect?.value || 'CHUTES_AI';
    const token = typeof getProviderToken === 'function' ? getProviderToken(currentProvider) : apiToken.value.trim();

    if (!token || (typeof isValidToken === 'function' && !isValidToken(token, currentProvider))) {
        // Ensure showAlert is available (from utils.js)
        if (typeof showAlert === 'function') showAlert('API token missing.', 'error');
        return;
    }

    const loadingId = 'loading-' + Date.now();
    // Ensure addLoadingIndicator is available (from messageHandler.js)
    if (typeof addLoadingIndicator === 'function') {
        addLoadingIndicator(loadingId);
    } else {
        console.error("addLoadingIndicator function not found.");
        return;
    }
    
    const requestData = {
        model: modelSelect.value, // modelSelect from uiElements.js
        messages: getConversationHistory(), // getConversationHistory needs to be defined or moved
        stream: streamCheckbox.checked, // streamCheckbox from uiElements.js
        max_tokens: parseInt(maxTokens.value), // maxTokens from uiElements.js
        temperature: parseFloat(temperature.value) // temperature from uiElements.js
    };
    // Ensure callAPI (for text) is available (from api.js)
    if (typeof callAPI === 'function') {
        callAPI(requestData, loadingId);
    } else {
        console.error("callAPI (text) function not found.");
    }
}

// --- Conversation History ---
// This function was previously part of the main script block.
function getConversationHistory() {
    const messages = [];
    // Get system prompt from the system prompt manager
    const systemPromptValue = typeof getCurrentSystemPromptContent === 'function' 
        ? getCurrentSystemPromptContent() 
        : (systemPromptInput?.value.trim() || '');

    if (systemPromptValue) {
        messages.push({ role: 'system', content: systemPromptValue });
    }
    
    // messagesContainer needs to be defined here, or passed, or uiElements.js needs DOMContentLoaded
    const messagesContainer = document.getElementById('messagesContainer');
    if (messagesContainer) {
        const messageElements = messagesContainer.querySelectorAll('.relative.rounded-lg.p-4');
        messageElements.forEach(el => {
            const roleElement = el.querySelector('p.font-medium');
            const roleText = roleElement?.textContent.trim().toLowerCase();
            const role = roleText === 'you' ? 'user' : 'assistant';

            const contentElement = el.querySelector('.message-stream');
            // Use rawContent from copy button if available, otherwise textContent
            const copyButton = el.querySelector('.copy-button');
            let content = contentElement?.textContent || '';
            if (copyButton && copyButton.dataset.rawContent) {
                content = copyButton.dataset.rawContent;
            }
            
            if (content && !el.querySelector('.typing-indicator')) {
                messages.push({
                    role: role,
                    content: content.trim()
                });
            }
        });
    } else {
        console.warn("messagesContainer not found in getConversationHistory. History might be incomplete.");
    }
    return messages;
}

// --- Visual Enhancements Integration ---
function initializeVisualEnhancementsIntegration() {
    console.log('🎨 Initializing Visual Enhancements Integration...');
    
    // Ensure consistent color application across all UI components
    ensureConsistentColorApplication();
    
    // Test color coordination between related elements
    testColorCoordination();
    
    // Verify theme switching functionality with enhanced visual elements
    verifyThemeSwitchingFunctionality();
    
    // Validate semantic consistency of color-coded information
    validateSemanticConsistency();
    
    // Initialize enhanced interactive feedback
    initializeEnhancedInteractiveFeedback();
    
    // Setup visual state synchronization
    setupVisualStateSynchronization();
    
    console.log('✅ Visual Enhancements Integration Complete');
}

function ensureConsistentColorApplication() {
    // Apply consistent colors to message elements
    const messageElements = document.querySelectorAll('[class*="message-"]');
    messageElements.forEach(element => {
        if (element.classList.contains('message-user')) {
            element.classList.add('border-l-4', 'border-user-message');
        } else if (element.classList.contains('message-assistant')) {
            element.classList.add('border-l-4', 'border-assistant-message');
        } else if (element.classList.contains('message-system')) {
            element.classList.add('border-l-4', 'border-system-message');
        }
    });
    
    // Apply consistent colors to settings elements
    const settingsElements = document.querySelectorAll('[id*="settings"], [class*="settings-"]');
    settingsElements.forEach(element => {
        if (element.id.includes('api') || element.classList.contains('api')) {
            element.classList.add('settings-api');
        } else if (element.id.includes('ui') || element.classList.contains('ui')) {
            element.classList.add('settings-ui');
        } else if (element.id.includes('prompt') || element.classList.contains('prompt')) {
            element.classList.add('settings-prompt');
        }
    });
}

function testColorCoordination() {
    // Test that related elements use coordinated colors
    const userElements = document.querySelectorAll('.text-user-message, .border-user-message');
    const assistantElements = document.querySelectorAll('.text-assistant-message, .border-assistant-message');
    const systemElements = document.querySelectorAll('.text-system-message, .border-system-message');
    
    // Log coordination status
    console.log(`User message elements: ${userElements.length}`);
    console.log(`Assistant message elements: ${assistantElements.length}`);
    console.log(`System message elements: ${systemElements.length}`);
}

function verifyThemeSwitchingFunctionality() {
    // Ensure all enhanced visual elements work with theme switching
    const themeToggleBtn = document.getElementById('themeToggle');
    if (themeToggleBtn) {
        const originalHandler = themeToggleBtn.onclick;
        themeToggleBtn.onclick = function(e) {
            if (originalHandler) originalHandler.call(this, e);
            
            // Update visual enhancements after theme change
            setTimeout(() => {
                updateVisualEnhancementsForTheme();
            }, 100);
        };
    }
}

function updateVisualEnhancementsForTheme() {
    // Update status indicators for current theme
    const statusIndicators = document.querySelectorAll('[class*="status-"]');
    statusIndicators.forEach(indicator => {
        // Force re-evaluation of theme-dependent styles
        indicator.style.display = 'none';
        indicator.offsetHeight; // Trigger reflow
        indicator.style.display = '';
    });
    
    // Update settings section headers for current theme
    const settingsHeaders = document.querySelectorAll('.settings-section-header');
    settingsHeaders.forEach(header => {
        header.style.display = 'none';
        header.offsetHeight; // Trigger reflow
        header.style.display = '';
    });
}

function validateSemanticConsistency() {
    // Ensure color-coded information maintains semantic meaning
    const colorMappings = {
        'blue': ['user', 'api', 'info', 'primary'],
        'purple': ['assistant', 'ui', 'secondary'],
        'green': ['system', 'success', 'connected', 'prompt'],
        'orange': ['warning', 'advanced'],
        'red': ['error', 'disconnected', 'danger']
    };
    
    Object.entries(colorMappings).forEach(([color, contexts]) => {
        contexts.forEach(context => {
            const elements = document.querySelectorAll(`[class*="${color}"], [class*="${context}"]`);
            if (elements.length > 0) {
                console.log(`✓ ${color} color used for ${context} context: ${elements.length} elements`);
            }
        });
    });
}

function initializeEnhancedInteractiveFeedback() {
    // Add enhanced hover effects to interactive elements
    const interactiveElements = document.querySelectorAll('button, input, select, textarea, [role="button"]');
    interactiveElements.forEach(element => {
        if (!element.classList.contains('enhanced-feedback')) {
            element.classList.add('enhanced-feedback', 'transition-all', 'duration-200');
            
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });
            
            element.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        }
    });
    
    // Add pulse effect to active elements
    const activeElements = document.querySelectorAll('[aria-pressed="true"], .active, [data-active="true"]');
    activeElements.forEach(element => {
        element.classList.add('animate-pulse');
        setTimeout(() => element.classList.remove('animate-pulse'), 2000);
    });
}

function setupVisualStateSynchronization() {
    // Synchronize visual states across related components
    const connectionStatusElements = document.querySelectorAll('[id*="connection"], [class*="connection"]');
    
    // Create a state synchronization observer
    const stateObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const element = mutation.target;
                if (element.classList.contains('status-connected')) {
                    synchronizeConnectionStatus('connected');
                } else if (element.classList.contains('status-disconnected')) {
                    synchronizeConnectionStatus('disconnected');
                } else if (element.classList.contains('status-connecting')) {
                    synchronizeConnectionStatus('connecting');
                }
            }
        });
    });
    
    connectionStatusElements.forEach(element => {
        stateObserver.observe(element, { attributes: true, attributeFilter: ['class'] });
    });
}

function synchronizeConnectionStatus(status) {
    const statusElements = document.querySelectorAll('[id*="Status"], [class*="status-indicator"]');
    statusElements.forEach(element => {
        // Remove existing status classes
        element.classList.remove('status-connected', 'status-disconnected', 'status-connecting', 'status-error');
        // Add new status class
        element.classList.add(`status-${status}`);
    });
}


// --- DOMContentLoaded Event Listener ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 Main.js: DOM loaded, initializing application...');
    
    // Load server configuration first
    await loadServerConfiguration();
    
    // Define elements that might not exist before DOM is fully loaded
    const messagesContainer = document.getElementById('messagesContainer');
    
    // Validate and initialize uiInteractions.js dependencies
    if (typeof initializeUIInteractionsDependencies === 'function') {
        try {
            const dependenciesInitialized = initializeUIInteractionsDependencies();
            if (!dependenciesInitialized) {
                console.warn('UI Interactions dependencies failed to initialize. Some features might not work correctly.');
                // Implement a fallback strategy if needed, e.g., disable certain buttons.
            }
        } catch (error) {
            console.error('Error during UI Interactions initialization:', error);
            // Fallback strategy
            if (typeof showAlert === 'function') {
                showAlert('A critical UI component failed to load. Some features may be disabled.', 'error');
            }
        }
    } else {
        console.error('initializeUIInteractionsDependencies function not found. UI will be basic.');
        if (typeof showAlert === 'function') {
            showAlert('Could not load UI interaction scripts. The application may not function correctly.', 'error');
        }
    }

    // Initialize functions that depend on DOM elements
    if (typeof updateCharCount === 'function') updateCharCount();
    if (typeof updateTemperatureValue === 'function') updateTemperatureValue(); // from settings.js
    if (typeof updateMaxTokensValue === 'function') updateMaxTokensValue();   // from settings.js
    if (typeof loadSettings === 'function') {
        loadSettings();                 // from settings.js
        // Apply model-specific settings right after loading the saved model
        if (typeof applyModelSpecificSettings === 'function') {
            setTimeout(applyModelSpecificSettings, 0); // Use a tiny delay to ensure DOM is ready
        }
    }
    // Initialize system prompt manager first
    if (typeof initializeSystemPromptManager === 'function') {
        initializeSystemPromptManager();
    }
    
    // Then update the indicator after system prompt manager is initialized
    if (typeof updateSystemPromptIndicator === 'function') updateSystemPromptIndicator(); // from settings.js

    // Initialize visual enhancements integration
    initializeVisualEnhancementsIntegration();

    // Attach Event Listeners
    // userInput, sendBtn, etc. are from uiElements.js and should be available globally if not using modules
    if (userInput) userInput.addEventListener('input', updateCharCount); // updateCharCount from uiInteractions.js
    if (sendBtn) sendBtn.addEventListener('click', sendMessage);
    if (userInput) {
        userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
    if (clearChat) clearChat.addEventListener('click', clearMessages); // clearMessages from messageHandler.js
    if (toggleParamsBtnHeader) toggleParamsBtnHeader.addEventListener('click', toggleParamsPanel); // from settings.js
    if (closeParamsModal) closeParamsModal.addEventListener('click', toggleParamsPanel); // from settings.js
    if (toggleTokenVisibility) toggleTokenVisibility.addEventListener('click', toggleTokenVisibilityHandler); // from settings.js
    if (toggleOpenrouterTokenVisibility) toggleOpenrouterTokenVisibility.addEventListener('click', toggleOpenrouterTokenVisibilityHandler); // from settings.js
    if (apiProviderSelect) apiProviderSelect.addEventListener('change', () => {
        updateProviderFields();
        updateModelOptions();
    }); // from settings.js
    if (temperature) temperature.addEventListener('input', updateTemperatureValue); // from settings.js
    if (maxTokens) maxTokens.addEventListener('input', updateMaxTokensValue);   // from settings.js
    if (modelSelect) modelSelect.addEventListener('change', handleModelSelectionChange); // from settings.js
    if (testConnectionBtn) testConnectionBtn.addEventListener('click', testConnection); // from api.js
    if (themeToggle) themeToggle.addEventListener('click', toggleTheme);       // from settings.js
    if (saveSettingsBtn) saveSettingsBtn.addEventListener('click', saveSettings); // from settings.js
    if (settingsDarkBtn) settingsDarkBtn.addEventListener('click', () => setTheme('dark')); // setTheme from settings.js
    if (settingsLightBtn) settingsLightBtn.addEventListener('click', () => setTheme('light')); // setTheme from settings.js
    
    // System Prompt Editor Listeners (ensure elements are from uiElements.js)
    if (expandSystemPromptBtn) expandSystemPromptBtn.addEventListener('click', openSystemPromptEditor); // from settings.js
    if (closeSystemPromptEditorBtn) closeSystemPromptEditorBtn.addEventListener('click', closeSystemPromptEditor); // from settings.js
    if (cancelSystemPromptEditorBtn) cancelSystemPromptEditorBtn.addEventListener('click', closeSystemPromptEditor); // from settings.js
    if (saveSystemPromptEditorBtn) saveSystemPromptEditorBtn.addEventListener('click', saveSystemPromptFromEditor); // from settings.js
    if (systemPromptInput) systemPromptInput.addEventListener('input', updateSystemPromptIndicator); // from settings.js
    if (systemPromptIndicator) systemPromptIndicator.addEventListener('click', toggleParamsPanel); // from settings.js



    // Message Container Click Listener
    if (messagesContainer) {
        messagesContainer.addEventListener('click', handleMessageContainerClick); // from uiInteractions.js
    } else {
        console.error("ERROR: Could not find messagesContainer element in DOMContentLoaded!");
    }


    
    // Initialize KaTeX and Prism.js for any existing content
    setTimeout(() => {
        if (typeof initKaTeX === 'function') initKaTeX(document.body); // from rendering.js
        if (window.Prism && typeof highlightCodeBlocks === 'function') { // from rendering.js
            document.querySelectorAll('pre code').forEach(block => {
                if (block.parentElement) highlightCodeBlocks(block.parentElement);
            });
        }
    }, 500);

    // Prism.js MutationObserver for dynamic content
    if (window.Prism && typeof highlightCodeBlocks === 'function') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            const codeBlocks = node.querySelectorAll('pre code');
                            if (codeBlocks.length > 0) {
                                codeBlocks.forEach(function(block) {
                                   if (block.parentElement) highlightCodeBlocks(block.parentElement);
                                });
                            }
                        }
                    });
                }
            });
        });
        if (messagesContainer) observer.observe(messagesContainer, { childList: true, subtree: true });
    }

    // Add page unload event listener to clean up active streams
    window.addEventListener('beforeunload', () => {
        if (typeof cleanupActiveStreams === 'function') {
            cleanupActiveStreams();
        }
    });
});

// Ensure Prism.js initialization script from <head> is still effective or replicated if needed.
// The Prism auto-loader and specific language components are loaded via CDN in <head>.
// The highlightCodeBlocks function should handle applying Prism to new and existing blocks.
