// --- Prompt Library Storage Functions ---
const PROMPT_LIBRARY_KEY = 'promptLibrary';

/**
 * Retrieves all prompts from Local Storage.
 * @returns {Object} An object containing the prompts.
 */
function getPrompts() {
    try {
        const prompts = localStorage.getItem(PROMPT_LIBRARY_KEY);
        return prompts ? JSON.parse(prompts) : {};
    } catch (error) {
        console.error("Error reading prompts from Local Storage:", error);
        return {};
    }
}

/**
 * Saves a single prompt to Local Storage.
 * @param {string} id - The unique identifier for the prompt.
 * @param {string} title - The title of the prompt.
 * @param {string} content - The content of the prompt.
 */
function savePrompt(id, title, content) {
    try {
        const prompts = getPrompts();
        prompts[id] = { title, content };
        localStorage.setItem(PROMPT_LIBRARY_KEY, JSON.stringify(prompts));
    } catch (error) {
        console.error("Error saving prompt to Local Storage:", error);
        showAlert('Failed to save prompt.', 'error');
    }
}

/**
 * Deletes a prompt from Local Storage.
 * @param {string} id - The unique identifier for the prompt to delete.
 */
function deletePrompt(id) {
    try {
        const prompts = getPrompts();
        delete prompts[id];
        localStorage.setItem(PROMPT_LIBRARY_KEY, JSON.stringify(prompts));
    } catch (error) {
        console.error("Error deleting prompt from Local Storage:", error);
        showAlert('Failed to delete prompt.', 'error');
    }
}
// --- Settings Functions ---
function saveSettings() {
    const settingsToSave = {
        apiProvider: apiProviderSelect.value,
        apiToken: apiToken.value, // Note: Saving token is insecure
        openrouterApiToken: openrouterApiToken.value, // Note: Saving token is insecure
        model: modelSelect.value,
        temperature: temperature.value,
        maxTokens: maxTokens.value,
        stream: streamCheckbox.checked,
        theme: localStorage.getItem('themePreference') || 'dark' // Add theme preference
    };
    try {
        localStorage.setItem('userSettings', JSON.stringify(settingsToSave));
        
        // Use the new colored feedback system
        showSaveConfirmationFeedback('Settings saved successfully!');
        updateSystemPromptIndicator();
        
        // Update connection status to show settings are applied
        updateConnectionStatusIndicator('connected', 'Settings applied');
        
    } catch (error) {
        console.error("Error saving settings to Local Storage:", error);
        
        // Show error feedback with colored indicator
        updateConnectionStatusIndicator('error', 'Failed to save settings');
        showAlert('Failed to save settings.', 'error');
    }
}

function loadSettings() {
    const savedTheme = localStorage.getItem('themePreference');
    if (savedTheme) {
        setTheme(savedTheme);
    } else {
        setTheme('dark'); // Default to dark if nothing saved
    }
    
    // Initialize visual enhancements for settings panel
    setTimeout(() => {
        initializeSettingsVisualEnhancements();
    }, 100); // Small delay to ensure DOM is ready

    const savedSettingsString = localStorage.getItem('userSettings');
    if (savedSettingsString) {
        try {
            const loadedSettings = JSON.parse(savedSettingsString);


            
            // Load API provider selection
            if (loadedSettings.apiProvider !== undefined) {
                apiProviderSelect.value = loadedSettings.apiProvider;
            } else {
                apiProviderSelect.value = getDefaultProvider(); // Use default from config
            }
            
            // Load API tokens
            if (loadedSettings.apiToken !== undefined) {
                 apiToken.value = loadedSettings.apiToken;
            }
            if (loadedSettings.openrouterApiToken !== undefined) {
                 openrouterApiToken.value = loadedSettings.openrouterApiToken;
            }
            
             if (loadedSettings.model !== undefined) {
                 modelSelect.value = loadedSettings.model;
            }

            updateSystemPromptIndicator();
            updateProviderFields(); // Update UI based on selected provider
            updateModelOptions(); // Update model options based on provider
            
            // Set model after options are updated with validation
            if (loadedSettings.model !== undefined) {
                // Small delay to ensure options are populated
                setTimeout(() => {
                    const validModel = getModelWithFallback(apiProviderSelect.value, loadedSettings.model);
                    if (validModel) {
                        modelSelect.value = validModel;
                        // Apply model-specific settings after setting the model
                        applyModelSpecificSettings();
                    }
                }, 10);
            }
            
            if (loadedSettings.temperature !== undefined) {
                 temperature.value = loadedSettings.temperature;
                 updateTemperatureValue();
            }
             if (loadedSettings.maxTokens !== undefined) {
                 maxTokens.value = loadedSettings.maxTokens;
                 updateMaxTokensValue();
            }
            if (loadedSettings.stream !== undefined) {
                 streamCheckbox.checked = loadedSettings.stream;
            }
        } catch (error) {
            console.error("Error loading settings from Local Storage:", error);
            localStorage.removeItem('userSettings');
        }
    } else {
        // Set defaults for new users
        apiProviderSelect.value = getDefaultProvider();
        updateProviderFields();
        updateModelOptions();
    }
}

function updateTemperatureValue() {
    if (tempValue) tempValue.textContent = temperature.value;
}

function updateMaxTokensValue() {
    if (maxTokensValue) maxTokensValue.textContent = maxTokens.value;
}

function setTheme(themeName) {
    const icon = themeToggle.querySelector('i');
    if (themeName === 'dark') {
        document.documentElement.classList.add('dark');
        localStorage.setItem('themePreference', 'dark');
        if (icon) icon.className = 'fas fa-sun text-white';
    } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('themePreference', 'light');
        if (icon) icon.className = 'fas fa-moon text-primary-500';
    }
}

function toggleTheme() {
    const isCurrentlyDark = document.documentElement.classList.contains('dark');
    const newTheme = isCurrentlyDark ? 'light' : 'dark';
    setTheme(newTheme);
}

function toggleParamsPanel() {
    if (paramsPanel.classList.contains('opacity-0')) {
        paramsPanel.classList.remove('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
        paramsPanel.classList.add('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
    } else {
        paramsPanel.classList.remove('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
        paramsPanel.classList.add('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
        // Reset panel size when closing
        resetSettingsPanelSize();
    }
}

function expandSettingsPanel() {
    // Find the settings panel content div and expand it
    const settingsContent = paramsPanel.querySelector('.bg-white, .dark\\:bg-sora-gray-dark');
    if (settingsContent) {
        // Change from max-w-md to max-w-2xl for expanded view
        settingsContent.classList.remove('max-w-md');
        settingsContent.classList.add('max-w-2xl');

        // Mark as expanded for potential styling
        settingsContent.setAttribute('data-expanded', 'true');
    }
}

function resetSettingsPanelSize() {
    // Reset the settings panel to normal size
    const settingsContent = paramsPanel.querySelector('.bg-white, .dark\\:bg-sora-gray-dark');
    if (settingsContent) {
        settingsContent.classList.remove('max-w-2xl');
        settingsContent.classList.add('max-w-md');
        settingsContent.removeAttribute('data-expanded');
    }

    // Reset textarea height
    if (systemPromptInput) {
        systemPromptInput.style.height = '';
    }
}

function toggleTokenVisibilityHandler() {
    if (apiToken.type === 'password') {
        apiToken.type = 'text';
        toggleTokenVisibility.innerHTML = '<i class="fas fa-eye-slash"></i>';
    } else {
        apiToken.type = 'password';
        toggleTokenVisibility.innerHTML = '<i class="fas fa-eye"></i>';
    }
}

function toggleOpenrouterTokenVisibilityHandler() {
    if (openrouterApiToken.type === 'password') {
        openrouterApiToken.type = 'text';
        toggleOpenrouterTokenVisibility.innerHTML = '<i class="fas fa-eye-slash"></i>';
    } else {
        openrouterApiToken.type = 'password';
        toggleOpenrouterTokenVisibility.innerHTML = '<i class="fas fa-eye"></i>';
    }
}

function updateProviderFields() {
    const selectedProvider = apiProviderSelect.value;
    
    if (selectedProvider === 'CHUTES_AI') {
        chutesApiTokenField.classList.remove('hidden');
        openrouterApiTokenField.classList.add('hidden');
    } else if (selectedProvider === 'OPENROUTER') {
        chutesApiTokenField.classList.add('hidden');
        openrouterApiTokenField.classList.remove('hidden');
    }
}

function updateModelOptions() {
    const selectedProvider = apiProviderSelect.value;
    const models = getProviderModels(selectedProvider);
    
    // Store current selection to preserve it if valid
    const currentModel = modelSelect.value;
    
    // Clear existing options
    modelSelect.innerHTML = '';
    
    // Add new options based on provider
    models.forEach(model => {
        const option = document.createElement('option');
        option.value = model;
        option.textContent = typeof getModelDisplayName === 'function' ? getModelDisplayName(model) : model;
        modelSelect.appendChild(option);
    });
    
    // Try to preserve current selection or fall back to default
    const validModel = getModelWithFallback(selectedProvider, currentModel);
    if (validModel && models.includes(validModel)) {
        modelSelect.value = validModel;
    } else if (models.length > 0) {
        modelSelect.value = models[0];
    }
    
    // Apply model-specific settings after model selection
    applyModelSpecificSettings();
}

// --- System Prompt Editor Functions ---
// Note: System prompt editor now uses expanded settings panel instead of separate modal

function openSystemPromptEditor() {
    // Check if panel is already expanded
    const settingsContent = paramsPanel.querySelector('.bg-white, .dark\\:bg-sora-gray-dark');
    const isExpanded = settingsContent && settingsContent.hasAttribute('data-expanded');

    if (isExpanded) {
        // Collapse the panel
        resetSettingsPanelSize();
        updateExpandButton(false);
    } else {
        // Expand the panel
        expandSettingsPanel();
        updateExpandButton(true);

        // Focus on the textarea in the expanded settings
        if (systemPromptInput) {
            systemPromptInput.focus();
            // Expand the textarea for better editing
            systemPromptInput.style.height = 'auto';
            systemPromptInput.style.height = Math.max(200, systemPromptInput.scrollHeight) + 'px';
        }
    }
}

function updateExpandButton(isExpanded) {
    const expandBtn = document.getElementById('expandSystemPromptBtn');
    if (expandBtn) {
        if (isExpanded) {
            expandBtn.title = 'Return to normal size';
            expandBtn.innerHTML = '<i class="fas fa-compress-alt mr-1"></i> Collapse';
        } else {
            expandBtn.title = 'Edit in larger window';
            expandBtn.innerHTML = '<i class="fas fa-expand-alt mr-1"></i> Expand';
        }
    }
}

// These functions are no longer needed since we use expanded settings panel
// function closeSystemPromptEditor() and saveSystemPromptFromEditor() removed

function updateSystemPromptIndicator() {
    // Find the system prompt indicator element dynamically
    const systemPromptIndicator = document.getElementById('systemPromptIndicator');

    // Use the new system prompt manager to get current content
    const systemPromptValue = typeof getCurrentSystemPromptContent === 'function'
        ? getCurrentSystemPromptContent()
        : (systemPromptInput ? systemPromptInput.value.trim() : '');

    if (systemPromptIndicator) { // Check if element exists
        if (systemPromptValue) {
            systemPromptIndicator.classList.remove('hidden');
            const truncatedPrompt = systemPromptValue.length > 50
                ? systemPromptValue.substring(0, 50) + '...'
                : systemPromptValue;
            systemPromptIndicator.setAttribute('title', `Active System Prompt: "${truncatedPrompt}"`);
        } else {
            systemPromptIndicator.classList.add('hidden');
            systemPromptIndicator.setAttribute('title', ''); // Clear title when hidden
        }
    }
}

function handleModelSelectionChange() {
    const selectedProvider = apiProviderSelect.value;
    const selectedModel = modelSelect.value;
    
    // Validate the selected model
    const validation = validateModel(selectedProvider, selectedModel);
    if (!validation.valid) {
        console.warn(`Invalid model selection: ${validation.error}`);
        
        // Fall back to a valid model
        const fallbackModel = getModelWithFallback(selectedProvider, null);
        if (fallbackModel && fallbackModel !== selectedModel) {
            console.log(`Falling back to model: ${fallbackModel}`);
            modelSelect.value = fallbackModel;
        }
    }
    
    // Apply model-specific settings
    applyModelSpecificSettings();
}

/**
 * Applies model-specific settings, like adjusting max tokens.
 * This function should be called after model selection changes or settings are loaded.
 */
function applyModelSpecificSettings() {
    if (!modelSelect || !maxTokens || typeof updateMaxTokensValue !== 'function' || typeof modelConfigurations === 'undefined') {
        console.warn("Cannot apply model-specific settings: required elements or config not found.");
        return;
    }

    const selectedModel = modelSelect.value;
    const config = modelConfigurations[selectedModel] || modelConfigurations['default'];

    if (config && config.maxTokens) {
        // First, update the max attribute of the slider itself
        maxTokens.max = config.maxTokens;
        // Then, set the value
        maxTokens.value = config.maxTokens;
        // Finally, update the text display
        updateMaxTokensValue(); // Update the UI to reflect the change
    }
}
// --- Settings Visual Enhancement Functions ---

/**
 * Settings category configuration for color coding and icons
 */
const settingsCategories = {
    api: { 
        color: 'blue', 
        icon: 'fa-plug',
        cssClass: 'api',
        displayName: 'API Settings'
    },
    ui: { 
        color: 'purple', 
        icon: 'fa-palette',
        cssClass: 'ui',
        displayName: 'UI Settings'
    },
    prompt: { 
        color: 'green', 
        icon: 'fa-comment-dots',
        cssClass: 'prompt',
        displayName: 'Prompt Settings'
    },
    advanced: { 
        color: 'orange', 
        icon: 'fa-cogs',
        cssClass: 'advanced',
        displayName: 'Advanced Settings'
    }
};

/**
 * Applies color coding to settings category elements
 * @param {string} category - The category name (api, ui, prompt, advanced)
 * @param {HTMLElement} element - The element to apply colors to
 */
function applyCategoryColors(category, element) {
    if (!settingsCategories[category] || !element) return;
    
    const config = settingsCategories[category];
    element.classList.add('settings-section-header', config.cssClass);
}

/**
 * Creates a colored section header for settings categories
 * @param {string} category - The category name
 * @param {string} title - The header title
 * @returns {HTMLElement} The created header element
 */
function createSettingsSectionHeader(category, title) {
    if (!settingsCategories[category]) return null;
    
    const config = settingsCategories[category];
    const header = document.createElement('div');
    header.className = `settings-section-header ${config.cssClass}`;
    
    const icon = document.createElement('i');
    icon.className = `fas ${config.icon} settings-icon ${config.cssClass}`;
    
    const titleSpan = document.createElement('span');
    titleSpan.textContent = title || config.displayName;
    titleSpan.className = 'font-semibold';
    
    header.appendChild(icon);
    header.appendChild(titleSpan);
    
    return header;
}

/**
 * Creates a colored divider for settings sections
 * @param {string} category - The category name
 * @returns {HTMLElement} The created divider element
 */
function createSettingsDivider(category) {
    if (!settingsCategories[category]) return null;
    
    const config = settingsCategories[category];
    const divider = document.createElement('hr');
    divider.className = `settings-divider ${config.cssClass}`;
    
    return divider;
}

/**
 * Applies category-specific styling to input elements
 * @param {string} category - The category name
 * @param {HTMLElement} input - The input element
 */
function applyInputCategoryStyle(category, input) {
    if (!settingsCategories[category] || !input) return;
    
    const config = settingsCategories[category];
    input.classList.add('settings-input', config.cssClass);
}

/**
 * Applies category-specific styling to button elements
 * @param {string} category - The category name
 * @param {HTMLElement} button - The button element
 */
function applyButtonCategoryStyle(category, button) {
    if (!settingsCategories[category] || !button) return;
    
    const config = settingsCategories[category];
    button.classList.add('settings-button', config.cssClass);
}

/**
 * Adds colored icons to settings elements
 * @param {string} category - The category name
 * @param {HTMLElement} element - The element to add icon to
 * @param {string} iconClass - Optional custom icon class
 */
function addSettingsIcon(category, element, iconClass = null) {
    if (!settingsCategories[category] || !element) return;
    
    const config = settingsCategories[category];
    const icon = document.createElement('i');
    icon.className = `fas ${iconClass || config.icon} settings-icon ${config.cssClass}`;
    
    // Insert icon at the beginning of the element
    element.insertBefore(icon, element.firstChild);
}

/**
 * Creates smooth transitions when switching between settings sections
 * @param {HTMLElement} fromSection - The section being hidden
 * @param {HTMLElement} toSection - The section being shown
 * @param {Function} callback - Optional callback after transition
 */
function transitionSettingsSection(fromSection, toSection, callback) {
    if (!fromSection || !toSection) return;
    
    // Add transition classes
    fromSection.style.transition = 'opacity 0.3s ease-in-out, transform 0.3s ease-in-out';
    toSection.style.transition = 'opacity 0.3s ease-in-out, transform 0.3s ease-in-out';
    
    // Fade out current section
    fromSection.style.opacity = '0';
    fromSection.style.transform = 'translateX(-10px)';
    
    setTimeout(() => {
        fromSection.style.display = 'none';
        toSection.style.display = 'block';
        toSection.style.opacity = '0';
        toSection.style.transform = 'translateX(10px)';
        
        // Fade in new section
        setTimeout(() => {
            toSection.style.opacity = '1';
            toSection.style.transform = 'translateX(0)';
            
            if (callback) callback();
        }, 50);
    }, 300);
}

/**
 * Initializes visual enhancements for settings panel
 */
function initializeSettingsVisualEnhancements() {
    // Apply color coding to existing settings elements
    applySettingsColorCoding();
    
    // Add visual indicators for different settings categories
    addSettingsVisualIndicators();
    
    // Setup smooth transitions for settings sections
    setupSettingsTransitions();
}

/**
 * Applies color coding to existing settings elements in the DOM
 */
function applySettingsColorCoding() {
    // API Settings elements
    const apiElements = [
        document.getElementById('apiProviderSelect'),
        document.getElementById('apiToken'),
        document.getElementById('openrouterApiToken'),
        document.getElementById('modelSelect'),
        document.getElementById('testConnectionBtn')
    ];
    
    apiElements.forEach(element => {
        if (element) {
            if (element.tagName === 'BUTTON') {
                applyButtonCategoryStyle('api', element);
            } else {
                applyInputCategoryStyle('api', element);
            }
        }
    });
    
    // UI Settings elements
    const uiElements = [
        document.getElementById('settingsDarkBtn'),
        document.getElementById('settingsLightBtn')
    ];
    
    uiElements.forEach(element => {
        if (element) {
            applyButtonCategoryStyle('ui', element);
        }
    });
    
    // Prompt Settings elements
    const promptElements = [
        document.getElementById('systemPromptSource'),
        document.getElementById('systemPromptInput'),
        document.getElementById('promptLibraryBtn'),
        document.getElementById('expandSystemPromptBtn')
    ];
    
    promptElements.forEach(element => {
        if (element) {
            if (element.tagName === 'BUTTON') {
                applyButtonCategoryStyle('prompt', element);
            } else {
                applyInputCategoryStyle('prompt', element);
            }
        }
    });
    
    // Advanced Settings elements (temperature, max tokens, stream)
    const advancedElements = [
        document.getElementById('temperature'),
        document.getElementById('maxTokens'),
        document.getElementById('streamCheckbox')
    ];
    
    advancedElements.forEach(element => {
        if (element) {
            applyInputCategoryStyle('advanced', element);
        }
    });
}

/**
 * Adds visual indicators for different settings categories
 */
function addSettingsVisualIndicators() {
    // Add section headers with colored backgrounds and icons
    addSettingsSectionHeaders();
    
    // Add colored dividers between sections
    addSettingsDividers();
    
    // Add status indicators for settings validation
    addSettingsStatusIndicators();
}

/**
 * Adds colored section headers to settings categories
 */
function addSettingsSectionHeaders() {
    // Find the API provider selection element and add header before it
    const apiProviderSelect = document.getElementById('apiProviderSelect');
    if (apiProviderSelect) {
        const apiHeader = createSettingsSectionHeader('api', 'API Configuration');
        if (apiHeader) {
            apiProviderSelect.parentNode.insertBefore(apiHeader, apiProviderSelect.parentNode.firstChild);
        }
    }
    
    // Find the UI settings section and add header
    const uiSettingsSection = document.querySelector('h3');
    if (uiSettingsSection && uiSettingsSection.textContent.includes('UI Settings')) {
        const uiHeader = createSettingsSectionHeader('ui', 'Interface Settings');
        if (uiHeader) {
            uiSettingsSection.parentNode.replaceChild(uiHeader, uiSettingsSection);
        }
    }
    
    // Find the system prompt section and add header
    const systemPromptLabel = document.querySelector('label[for="systemPromptInput"]');
    if (systemPromptLabel) {
        const promptHeader = createSettingsSectionHeader('prompt', 'System Prompt Configuration');
        if (promptHeader) {
            systemPromptLabel.parentNode.insertBefore(promptHeader, systemPromptLabel);
        }
    }
    
    // Add advanced settings header before temperature control
    const temperatureContainer = document.getElementById('temperature')?.parentNode;
    if (temperatureContainer) {
        const advancedHeader = createSettingsSectionHeader('advanced', 'Model Parameters');
        if (advancedHeader) {
            temperatureContainer.parentNode.insertBefore(advancedHeader, temperatureContainer);
        }
    }
}

/**
 * Adds colored dividers between settings sections
 */
function addSettingsDividers() {
    // Add dividers between major sections
    const sections = [
        { category: 'api', afterElement: document.getElementById('testConnectionBtn') },
        { category: 'prompt', afterElement: document.getElementById('promptLibraryBtn') },
        { category: 'ui', afterElement: document.getElementById('settingsLightBtn') }
    ];
    
    sections.forEach(section => {
        if (section.afterElement) {
            const divider = createSettingsDivider(section.category);
            if (divider) {
                section.afterElement.parentNode.insertBefore(divider, section.afterElement.nextSibling);
            }
        }
    });
}

/**
 * Adds status indicators for settings validation
 */
function addSettingsStatusIndicators() {
    // Add connection status indicator
    const testConnectionBtn = document.getElementById('testConnectionBtn');
    if (testConnectionBtn) {
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'connectionStatusIndicator';
        statusIndicator.className = 'settings-status-indicator api hidden';
        statusIndicator.innerHTML = '<i class="fas fa-circle"></i> <span>Connection Status</span>';
        testConnectionBtn.parentNode.insertBefore(statusIndicator, testConnectionBtn.nextSibling);
    }
    
    // Add validation indicators for form inputs
    addInputValidationIndicators();
}

/**
 * Adds validation indicators to form inputs
 */
function addInputValidationIndicators() {
    const inputsToValidate = [
        { id: 'apiToken', category: 'api' },
        { id: 'openrouterApiToken', category: 'api' },
        { id: 'systemPromptInput', category: 'prompt' }
    ];
    
    inputsToValidate.forEach(input => {
        const element = document.getElementById(input.id);
        if (element) {
            const indicator = document.createElement('div');
            indicator.className = `settings-validation-indicator ${input.category} hidden`;
            indicator.innerHTML = '<i class="fas fa-check-circle"></i> <span>Valid</span>';
            element.parentNode.appendChild(indicator);
        }
    });
}

/**
 * Sets up smooth transitions for settings sections
 */
function setupSettingsTransitions() {
    // Add transition classes to settings containers
    const settingsContainers = document.querySelectorAll('.settings-section-header');
    settingsContainers.forEach(container => {
        container.style.transition = 'all 0.2s ease-in-out';
    });
    
    // Add hover effects for section headers
    settingsContainers.forEach(header => {
        header.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(4px)';
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        });
        
        header.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.boxShadow = 'none';
        });
    });
}

/**
 * Comprehensive Status Management System
 */

/**
 * Status configuration for different types of status indicators
 */
const statusConfigurations = {
    connected: { 
        icon: 'fa-check-circle', 
        text: 'Connected', 
        class: 'status-connected',
        color: 'green'
    },
    disconnected: { 
        icon: 'fa-times-circle', 
        text: 'Disconnected', 
        class: 'status-disconnected',
        color: 'red'
    },
    connecting: { 
        icon: 'fa-spinner fa-spin', 
        text: 'Connecting...', 
        class: 'status-connecting',
        color: 'blue'
    },
    loading: { 
        icon: 'fa-spinner fa-spin', 
        text: 'Loading...', 
        class: 'status-loading',
        color: 'blue'
    },
    error: { 
        icon: 'fa-exclamation-circle', 
        text: 'Error', 
        class: 'status-error',
        color: 'red'
    },
    warning: { 
        icon: 'fa-exclamation-triangle', 
        text: 'Warning', 
        class: 'status-warning',
        color: 'orange'
    },
    success: { 
        icon: 'fa-check-circle', 
        text: 'Success', 
        class: 'status-connected',
        color: 'green'
    },
    validating: { 
        icon: 'fa-spinner fa-spin', 
        text: 'Validating...', 
        class: 'status-loading',
        color: 'blue'
    },
    saved: { 
        icon: 'fa-check', 
        text: 'Saved', 
        class: 'status-connected',
        color: 'green'
    }
};

/**
 * Updates connection status indicator with appropriate colors and animations
 * @param {string} status - The connection status (connected, disconnected, connecting, error, etc.)
 * @param {string} customText - Optional custom text to display
 */
function updateConnectionStatusIndicator(status, customText = null) {
    const indicator = document.getElementById('connectionStatusIndicator');
    if (!indicator) return;
    
    const config = statusConfigurations[status] || statusConfigurations.disconnected;
    const displayText = customText || config.text;
    
    // Add transition class for smooth status changes
    indicator.classList.add('status-transition');
    
    // Update the indicator with new status
    indicator.className = `settings-status-indicator api ${config.class} status-transition`;
    indicator.innerHTML = `<i class="fas ${config.icon}"></i> <span>${displayText}</span>`;
    indicator.classList.remove('hidden');
    
    // Trigger animation for status change
    indicator.classList.add('status-changing');
    setTimeout(() => {
        indicator.classList.remove('status-changing');
    }, 300);
}

/**
 * Shows validation feedback for settings inputs with colored indicators
 * @param {string} inputId - The input element ID
 * @param {boolean} isValid - Whether the input is valid
 * @param {string} message - Optional validation message
 * @param {string} status - Optional specific status (success, error, warning)
 */
function showSettingsValidationFeedback(inputId, isValid, message = '', status = null) {
    const input = document.getElementById(inputId);
    if (!input) return;
    
    const indicator = input.parentNode.querySelector('.settings-validation-indicator');
    if (!indicator) return;
    
    let statusType;
    if (status) {
        statusType = status;
    } else {
        statusType = isValid ? 'success' : 'error';
    }
    
    const config = statusConfigurations[statusType] || statusConfigurations.error;
    const displayText = message || config.text;
    
    // Add transition class for smooth changes
    indicator.classList.add('status-transition');
    
    // Update the indicator
    indicator.className = `settings-validation-indicator ${config.class} status-transition`;
    indicator.innerHTML = `<i class="fas ${config.icon}"></i> <span>${displayText}</span>`;
    indicator.classList.remove('hidden');
    
    // Add visual feedback to the input itself
    input.classList.remove('border-green-primary', 'border-error', 'border-warning', 'border-blue-primary');
    if (isValid) {
        input.classList.add('border-green-primary');
    } else {
        input.classList.add('border-error');
    }
    
    // Auto-hide success messages after 3 seconds
    if (isValid && !message) {
        setTimeout(() => {
            indicator.classList.add('hidden');
            input.classList.remove('border-green-primary');
        }, 3000);
    }
}

/**
 * Shows colored save confirmation feedback with green checkmark animation
 * @param {string} message - The save confirmation message
 * @param {number} duration - How long to show the feedback (ms)
 */
function showSaveConfirmationFeedback(message = 'Settings saved successfully!', duration = 3000) {
    // Create or update save confirmation indicator
    let saveIndicator = document.getElementById('saveConfirmationIndicator');
    if (!saveIndicator) {
        saveIndicator = document.createElement('div');
        saveIndicator.id = 'saveConfirmationIndicator';
        saveIndicator.className = 'status-indicator status-connected';
        
        // Insert after the save button or at the end of settings
        const saveButton = document.querySelector('button[onclick*="saveSettings"]') || 
                          document.querySelector('#paramsPanel .space-y-4');
        if (saveButton) {
            saveButton.parentNode.insertBefore(saveIndicator, saveButton.nextSibling);
        }
    }
    
    const config = statusConfigurations.saved;
    saveIndicator.innerHTML = `<i class="fas ${config.icon}"></i> <span>${message}</span>`;
    saveIndicator.className = `status-indicator ${config.class} status-transition`;
    saveIndicator.classList.remove('hidden');
    
    // Add checkmark animation
    const icon = saveIndicator.querySelector('i');
    if (icon) {
        icon.style.animation = 'pulse-green 0.6s ease-in-out';
        setTimeout(() => {
            icon.style.animation = '';
        }, 600);
    }
    
    // Auto-hide after duration
    setTimeout(() => {
        saveIndicator.classList.add('hidden');
    }, duration);
}

/**
 * Creates colored loading states for settings operations
 * @param {string} elementId - The element to show loading state for
 * @param {string} operation - The operation being performed
 * @param {boolean} show - Whether to show or hide the loading state
 */
function showSettingsLoadingState(elementId, operation = 'Loading', show = true) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    let loadingIndicator = element.parentNode.querySelector('.settings-loading-indicator');
    
    if (show) {
        if (!loadingIndicator) {
            loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'settings-loading-indicator status-loading status-transition';
            element.parentNode.appendChild(loadingIndicator);
        }
        
        const config = statusConfigurations.loading;
        loadingIndicator.innerHTML = `<i class="fas ${config.icon}"></i> <span>${operation}...</span>`;
        loadingIndicator.classList.remove('hidden');
        
        // Disable the associated element during loading
        element.disabled = true;
        element.style.opacity = '0.6';
    } else {
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }
        
        // Re-enable the element
        element.disabled = false;
        element.style.opacity = '1';
    }
}

/**
 * Shows API validation feedback with colored indicators
 * @param {boolean} isValid - Whether the API validation passed
 * @param {string} message - Validation message
 * @param {Object} details - Additional validation details
 */
function showApiValidationFeedback(isValid, message, details = {}) {
    updateConnectionStatusIndicator(isValid ? 'connected' : 'error', message);
    
    // Show validation for API token fields
    if (details.tokenValid !== undefined) {
        const tokenField = document.getElementById('apiToken').style.display !== 'none' ? 'apiToken' : 'openrouterApiToken';
        showSettingsValidationFeedback(tokenField, details.tokenValid, details.tokenMessage);
    }
    
    // Show validation for model selection
    if (details.modelValid !== undefined) {
        showSettingsValidationFeedback('modelSelect', details.modelValid, details.modelMessage);
    }
}

/**
 * Creates a generic status indicator element
 * @param {string} status - The status type
 * @param {string} text - The status text
 * @param {string} size - The size variant (sm, md, lg)
 * @param {string} variant - The style variant (default, outline, solid)
 * @returns {HTMLElement} The created status indicator element
 */
function createStatusIndicator(status, text, size = 'md', variant = 'default') {
    const config = statusConfigurations[status] || statusConfigurations.error;
    const indicator = document.createElement('div');
    
    let sizeClass = '';
    if (size === 'sm') sizeClass = 'status-indicator-sm';
    else if (size === 'lg') sizeClass = 'status-indicator-lg';
    
    let variantClass = '';
    if (variant === 'outline') variantClass = 'status-indicator-outline';
    else if (variant === 'solid') variantClass = 'status-indicator-solid';
    
    indicator.className = `status-indicator ${config.class} ${sizeClass} ${variantClass} status-transition`.trim();
    indicator.innerHTML = `<i class="fas ${config.icon}"></i> <span>${text}</span>`;
    
    return indicator;
}

/**
 * Creates a status badge (minimal circular indicator)
 * @param {string} status - The status type
 * @returns {HTMLElement} The created status badge element
 */
function createStatusBadge(status) {
    const config = statusConfigurations[status] || statusConfigurations.error;
    const badge = document.createElement('div');
    badge.className = `status-badge ${config.class}`;
    badge.title = config.text;
    return badge;
}

/**
 * Creates a status dot (even more minimal indicator)
 * @param {string} status - The status type
 * @returns {HTMLElement} The created status dot element
 */
function createStatusDot(status) {
    const config = statusConfigurations[status] || statusConfigurations.error;
    const dot = document.createElement('div');
    dot.className = `status-dot ${config.class}`;
    dot.title = config.text;
    return dot;
}

/**
 * Updates the status of multiple elements simultaneously
 * @param {Array} updates - Array of update objects {elementId, status, text}
 */
function updateMultipleStatuses(updates) {
    updates.forEach(update => {
        const element = document.getElementById(update.elementId);
        if (element) {
            const indicator = element.parentNode.querySelector('.status-indicator, .status-badge, .status-dot');
            if (indicator) {
                const config = statusConfigurations[update.status] || statusConfigurations.error;
                indicator.className = indicator.className.replace(/status-\w+/g, '') + ` ${config.class}`;
                
                if (update.text && indicator.querySelector('span')) {
                    indicator.querySelector('span').textContent = update.text;
                }
            }
        }
    });
}

/**
 * Enhanced settings save function with colored feedback
 */
function saveSettingsWithFeedback() {
    // Show loading state
    showSettingsLoadingState('saveSettingsBtn', 'Saving settings', true);
    
    try {
        // Call the original save settings function
        saveSettings();
        
        // Hide loading state
        showSettingsLoadingState('saveSettingsBtn', '', false);
        
        // Show success feedback
        showSaveConfirmationFeedback('Settings saved successfully!');
        
        // Update connection status if needed
        updateConnectionStatusIndicator('connected', 'Settings applied');
        
    } catch (error) {
        // Hide loading state
        showSettingsLoadingState('saveSettingsBtn', '', false);
        
        // Show error feedback
        updateConnectionStatusIndicator('error', 'Failed to save settings');
        console.error('Error saving settings:', error);
    }
}

// --- End Settings Functions ---

// Ensure functions are globally accessible for uiInteractions.js and other scripts
if (typeof window !== 'undefined') {
    window.getPrompts = getPrompts;
    window.savePrompt = savePrompt;
    window.deletePrompt = deletePrompt;
    window.saveSettings = saveSettings;
    window.loadSettings = loadSettings;
    window.updateTemperatureValue = updateTemperatureValue;
    window.updateMaxTokensValue = updateMaxTokensValue;
    window.setTheme = setTheme;
    window.toggleTheme = toggleTheme;
    window.toggleParamsPanel = toggleParamsPanel;
    window.toggleTokenVisibilityHandler = toggleTokenVisibilityHandler;
    window.toggleOpenrouterTokenVisibilityHandler = toggleOpenrouterTokenVisibilityHandler;
    window.updateProviderFields = updateProviderFields;
    window.updateModelOptions = updateModelOptions;
    window.openSystemPromptEditor = openSystemPromptEditor;
    window.updateSystemPromptIndicator = updateSystemPromptIndicator;
    window.handleModelSelectionChange = handleModelSelectionChange;
    window.applyModelSpecificSettings = applyModelSpecificSettings;
}
