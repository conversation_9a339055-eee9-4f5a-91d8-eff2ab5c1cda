// --- System Prompt Manager ---
// Manages different sources of system prompts: built-in files, manual input, and library

/**
 * System prompt source types
 */
const PROMPT_SOURCES = {
    FILE: 'file',
    MANUAL: 'manual',
    LIBRARY: 'library'
};

/**
 * Prompt source color configuration
 */
const PROMPT_SOURCE_COLORS = {
    [PROMPT_SOURCES.FILE]: {
        color: 'blue',
        cssClass: 'prompt-category-builtin',
        icon: 'fa-file-code',
        label: 'Built-in',
        statusClass: 'status-connected'
    },
    [PROMPT_SOURCES.MANUAL]: {
        color: 'purple',
        cssClass: 'prompt-category-manual',
        icon: 'fa-edit',
        label: 'Manual',
        statusClass: 'status-warning'
    },
    [PROMPT_SOURCES.LIBRARY]: {
        color: 'green',
        cssClass: 'prompt-category-library',
        icon: 'fa-book-open',
        label: 'Library',
        statusClass: 'status-connected'
    }
};

/**
 * Prompt state colors
 */
const PROMPT_STATE_COLORS = {
    ACTIVE: {
        color: 'green',
        cssClass: 'prompt-status-active',
        icon: 'fa-check-circle',
        label: 'Active'
    },
    MODIFIED: {
        color: 'orange',
        cssClass: 'prompt-status-modified',
        icon: 'fa-exclamation-triangle',
        label: 'Modified'
    },
    ERROR: {
        color: 'red',
        cssClass: 'prompt-status-error',
        icon: 'fa-times-circle',
        label: 'Error'
    },
    INACTIVE: {
        color: 'gray',
        cssClass: 'prompt-status-inactive',
        icon: 'fa-circle',
        label: 'Inactive'
    }
};

/**
 * Current system prompt configuration
 */
let currentSystemPromptConfig = {
    source: PROMPT_SOURCES.FILE, // Default to built-in file
    promptId: 'nsfwbase', // Default built-in prompt ID
    content: '' // Current active content
};

/**
 * Initialize system prompt manager
 */
function initializeSystemPromptManager() {
    // Load saved configuration
    loadSystemPromptConfig();
    
    // Set up event listeners
    setupSystemPromptEventListeners();
    
    // Initialize UI
    updateSystemPromptUI();
    
    // Load built-in prompt content
    loadBuiltInPromptContent();
    
    // Populate library prompts
    populateLibraryPrompts();
}

/**
 * Load system prompt configuration from localStorage
 */
function loadSystemPromptConfig() {
    try {
        const savedConfig = localStorage.getItem('systemPromptConfig');
        if (savedConfig) {
            const config = JSON.parse(savedConfig);
            currentSystemPromptConfig = { ...currentSystemPromptConfig, ...config };
        }
    } catch (error) {
        console.error('Error loading system prompt config:', error);
    }
}

/**
 * Save system prompt configuration to localStorage
 */
function saveSystemPromptConfig() {
    try {
        localStorage.setItem('systemPromptConfig', JSON.stringify(currentSystemPromptConfig));
    } catch (error) {
        console.error('Error saving system prompt config:', error);
    }
}

/**
 * Set up event listeners for system prompt elements
 */
function setupSystemPromptEventListeners() {
    // Source selector change
    if (systemPromptSource) {
        systemPromptSource.addEventListener('change', handleSystemPromptSourceChange);
        
        // Initialize source selector with colored options
        initializeSourceSelectorColors();
    }
    
    // Copy file prompt button
    if (copyFilePromptBtn) {
        copyFilePromptBtn.addEventListener('click', copyFilePromptToManual);
    }
    
    // Library prompt selector change
    if (libraryPromptSelect) {
        libraryPromptSelect.addEventListener('change', handleLibraryPromptChange);
    }
    
    // Manual input change
    if (systemPromptInput) {
        systemPromptInput.addEventListener('input', handleManualPromptChange);
    }
}

/**
 * Initialize source selector with colored options
 */
function initializeSourceSelectorColors() {
    if (!systemPromptSource) return;
    
    // Add colored styling to source selector options
    const options = systemPromptSource.querySelectorAll('option');
    options.forEach(option => {
        const sourceValue = option.value;
        const sourceConfig = PROMPT_SOURCE_COLORS[sourceValue];
        
        if (sourceConfig) {
            option.setAttribute('data-category', sourceValue);
            
            // Add colored icons to option text
            const iconHtml = `${sourceConfig.icon === 'fa-file-code' ? '📁' : sourceConfig.icon === 'fa-edit' ? '✏️' : '📚'} `;
            if (!option.textContent.includes(iconHtml.trim())) {
                option.textContent = iconHtml + option.textContent.replace(/^[📁✏️📚]\s*/, '');
            }
        }
    });
}

/**
 * Handle system prompt source change
 */
function handleSystemPromptSourceChange() {
    const newSource = systemPromptSource.value;
    currentSystemPromptConfig.source = newSource;
    
    updateSystemPromptUI();
    updateSystemPromptContent();
    saveSystemPromptConfig();
}

/**
 * Handle library prompt selection change
 */
function handleLibraryPromptChange() {
    const selectedPromptId = libraryPromptSelect.value;
    if (selectedPromptId) {
        const prompts = getPrompts(); // From settings.js
        const selectedPrompt = prompts[selectedPromptId];
        if (selectedPrompt) {
            currentSystemPromptConfig.promptId = selectedPromptId;
            currentSystemPromptConfig.content = selectedPrompt.content;
            
            // Update preview
            if (libraryPromptPreview) {
                libraryPromptPreview.textContent = selectedPrompt.content;
                libraryPromptPreview.classList.remove('hidden');
                
                // Apply colored preview styling
                updateLibraryPromptPreviewColors();
            }
            
            updateSystemPromptStatus();
            saveSystemPromptConfig();
        }
    } else {
        if (libraryPromptPreview) {
            libraryPromptPreview.classList.add('hidden');
            // Remove colored styling when hidden
            libraryPromptPreview.classList.remove('prompt-preview-area', 'has-content');
        }
    }
}

/**
 * Handle manual prompt input change
 */
function handleManualPromptChange() {
    currentSystemPromptConfig.content = systemPromptInput.value;
    updateSystemPromptStatus();
    saveSystemPromptConfig();
}

/**
 * Copy file prompt to manual input
 */
function copyFilePromptToManual() {
    if (typeof getDefaultBuiltInPrompt === 'function') {
        const builtInPrompt = getDefaultBuiltInPrompt();
        if (builtInPrompt && systemPromptInput) {
            systemPromptInput.value = builtInPrompt.content;
            currentSystemPromptConfig.source = PROMPT_SOURCES.MANUAL;
            currentSystemPromptConfig.content = builtInPrompt.content;
            
            // Update UI
            systemPromptSource.value = PROMPT_SOURCES.MANUAL;
            updateSystemPromptUI();
            updateSystemPromptStatus();
            saveSystemPromptConfig();
            
            // Show success message
            if (typeof showAlert === 'function') {
                showAlert('Built-in prompt copied to manual settings!', 'success');
            }
        }
    }
}

/**
 * Update system prompt UI based on current source
 */
function updateSystemPromptUI() {
    const source = currentSystemPromptConfig.source;
    
    // Hide all containers first
    if (filePromptPreview) filePromptPreview.classList.add('hidden');
    if (manualPromptInput) manualPromptInput.classList.add('hidden');
    if (libraryPromptSelector) libraryPromptSelector.classList.add('hidden');
    
    // Show appropriate container with animation
    setTimeout(() => {
        switch (source) {
            case PROMPT_SOURCES.FILE:
                if (filePromptPreview) {
                    filePromptPreview.classList.remove('hidden');
                    filePromptPreview.classList.add('prompt-source-transition');
                }
                break;
            case PROMPT_SOURCES.MANUAL:
                if (manualPromptInput) {
                    manualPromptInput.classList.remove('hidden');
                    manualPromptInput.classList.add('prompt-source-transition');
                }
                break;
            case PROMPT_SOURCES.LIBRARY:
                if (libraryPromptSelector) {
                    libraryPromptSelector.classList.remove('hidden');
                    libraryPromptSelector.classList.add('prompt-source-transition');
                }
                break;
        }
        
        // Remove animation class after animation completes
        setTimeout(() => {
            if (filePromptPreview) filePromptPreview.classList.remove('prompt-source-transition');
            if (manualPromptInput) manualPromptInput.classList.remove('prompt-source-transition');
            if (libraryPromptSelector) libraryPromptSelector.classList.remove('prompt-source-transition');
        }, 300);
    }, 50);
    
    // Update source selector with colors
    updateSourceSelectorColors(source);
    
    // Update source selector
    if (systemPromptSource) {
        systemPromptSource.value = source;
    }
    
    updateSystemPromptStatus();
}

/**
 * Update source selector with appropriate colors
 */
function updateSourceSelectorColors(source) {
    if (!systemPromptSource) return;
    
    const sourceConfig = PROMPT_SOURCE_COLORS[source];
    if (!sourceConfig) return;
    
    // Remove existing color classes
    Object.values(PROMPT_SOURCE_COLORS).forEach(config => {
        systemPromptSource.classList.remove(config.cssClass);
        systemPromptSource.classList.remove(`text-${config.color}-primary`);
        systemPromptSource.classList.remove(`border-${config.color}-primary`);
    });
    
    // Add current source color classes
    systemPromptSource.classList.add(`text-${sourceConfig.color}-primary`);
    systemPromptSource.classList.add(`border-${sourceConfig.color}-primary`);
    
    // Update focus ring color
    systemPromptSource.classList.add(`focus-${sourceConfig.color}`);
}

/**
 * Update system prompt content based on current source
 */
function updateSystemPromptContent() {
    const source = currentSystemPromptConfig.source;
    
    switch (source) {
        case PROMPT_SOURCES.FILE:
            loadBuiltInPromptContent();
            break;
        case PROMPT_SOURCES.MANUAL:
            // Content is already in systemPromptInput
            currentSystemPromptConfig.content = systemPromptInput ? systemPromptInput.value : '';
            break;
        case PROMPT_SOURCES.LIBRARY:
            // Content is set when library prompt is selected
            break;
    }
}

/**
 * Load built-in prompt content
 */
function loadBuiltInPromptContent() {
    if (typeof getDefaultBuiltInPrompt === 'function') {
        const builtInPrompt = getDefaultBuiltInPrompt();
        if (builtInPrompt && filePromptText) {
            filePromptText.textContent = builtInPrompt.content;
            currentSystemPromptConfig.content = builtInPrompt.content;
            currentSystemPromptConfig.promptId = builtInPrompt.id;
        }
    }
}

/**
 * Populate library prompts dropdown
 */
function populateLibraryPrompts() {
    if (!libraryPromptSelect || typeof getPrompts !== 'function') return;
    
    const prompts = getPrompts();
    
    // Clear existing options (except the first one)
    while (libraryPromptSelect.children.length > 1) {
        libraryPromptSelect.removeChild(libraryPromptSelect.lastChild);
    }
    
    // Add prompts from library
    Object.keys(prompts).forEach(promptId => {
        const prompt = prompts[promptId];
        const option = document.createElement('option');
        option.value = promptId;
        option.textContent = prompt.title || promptId;
        
        // Add data attribute for category-based coloring
        option.setAttribute('data-category', 'library');
        
        libraryPromptSelect.appendChild(option);
    });
    
    // Apply colored styling to the library selector
    updateLibraryPromptSelectorColors();
    
    // If current source is library, select the current prompt
    if (currentSystemPromptConfig.source === PROMPT_SOURCES.LIBRARY && currentSystemPromptConfig.promptId) {
        libraryPromptSelect.value = currentSystemPromptConfig.promptId;
        handleLibraryPromptChange();
    }
}

/**
 * Update system prompt status indicator
 */
function updateSystemPromptStatus() {
    if (!systemPromptStatusText) return;
    
    const source = currentSystemPromptConfig.source;
    const sourceConfig = PROMPT_SOURCE_COLORS[source];
    let statusText = '';
    let promptState = 'ACTIVE';
    
    switch (source) {
        case PROMPT_SOURCES.FILE:
            statusText = 'Using built-in prompt';
            promptState = 'ACTIVE';
            break;
        case PROMPT_SOURCES.MANUAL:
            const hasContent = currentSystemPromptConfig.content && currentSystemPromptConfig.content.trim();
            if (hasContent) {
                statusText = 'Using manual prompt';
                promptState = 'MODIFIED';
            } else {
                statusText = 'No manual prompt set';
                promptState = 'INACTIVE';
            }
            break;
        case PROMPT_SOURCES.LIBRARY:
            if (currentSystemPromptConfig.promptId) {
                const prompts = getPrompts();
                const prompt = prompts[currentSystemPromptConfig.promptId];
                if (prompt) {
                    statusText = `Using: ${prompt.title}`;
                    promptState = 'ACTIVE';
                } else {
                    statusText = 'Library prompt not found';
                    promptState = 'ERROR';
                }
            } else {
                statusText = 'No library prompt selected';
                promptState = 'INACTIVE';
            }
            break;
    }
    
    // Update status text
    systemPromptStatusText.textContent = statusText;
    
    // Apply colored status indicator
    updateStatusIndicatorColors(source, promptState);
    
    // Update source badge colors
    updateSourceBadgeColors(source);
}

/**
 * Update status indicator with appropriate colors
 */
function updateStatusIndicatorColors(source, state) {
    const statusElement = document.getElementById('systemPromptStatus');
    if (!statusElement) return;
    
    const sourceConfig = PROMPT_SOURCE_COLORS[source];
    const stateConfig = PROMPT_STATE_COLORS[state];
    
    if (!sourceConfig || !stateConfig) return;
    
    // Remove existing status classes
    Object.values(PROMPT_STATE_COLORS).forEach(config => {
        statusElement.classList.remove(config.cssClass);
    });
    
    // Add current state class
    statusElement.classList.add(stateConfig.cssClass);
    
    // Update icon if present
    const iconElement = statusElement.querySelector('i');
    if (iconElement) {
        // Remove existing icon classes
        iconElement.className = iconElement.className.replace(/fa-[a-z-]+/g, '');
        iconElement.classList.add('fas', stateConfig.icon);
    }
}

/**
 * Update source badge with appropriate colors
 */
function updateSourceBadgeColors(source) {
    const sourceConfig = PROMPT_SOURCE_COLORS[source];
    if (!sourceConfig) return;
    
    // Update system prompt indicator in the main UI
    const systemPromptIndicator = document.getElementById('systemPromptIndicator');
    if (systemPromptIndicator) {
        // Remove existing color classes
        Object.values(PROMPT_SOURCE_COLORS).forEach(config => {
            systemPromptIndicator.classList.remove(`text-${config.color}-primary`);
            systemPromptIndicator.classList.remove(config.cssClass);
        });
        
        // Add current source color
        systemPromptIndicator.classList.add(`text-${sourceConfig.color}-primary`);
        systemPromptIndicator.classList.add(sourceConfig.cssClass);
        
        // Update title with colored badge info
        const badgeText = `${sourceConfig.label} Prompt Active`;
        systemPromptIndicator.setAttribute('title', badgeText);
        
        // Show the indicator if it's hidden
        if (systemPromptIndicator.classList.contains('hidden')) {
            systemPromptIndicator.classList.remove('hidden');
        }
    }
}

/**
 * Create colored prompt source badge
 */
function createPromptSourceBadge(source) {
    const sourceConfig = PROMPT_SOURCE_COLORS[source];
    if (!sourceConfig) return null;
    
    const badge = document.createElement('span');
    badge.className = `prompt-category ${sourceConfig.cssClass}`;
    badge.innerHTML = `<i class="fas ${sourceConfig.icon}"></i>${sourceConfig.label}`;
    
    return badge;
}

/**
 * Create colored prompt status indicator
 */
function createPromptStatusIndicator(state) {
    const stateConfig = PROMPT_STATE_COLORS[state];
    if (!stateConfig) return null;
    
    const indicator = document.createElement('span');
    indicator.className = `prompt-status-indicator ${stateConfig.cssClass}`;
    indicator.innerHTML = `<i class="fas ${stateConfig.icon}"></i>${stateConfig.label}`;
    
    return indicator;
}

/**
 * Update library prompt selector with colored options
 */
function updateLibraryPromptSelectorColors() {
    if (!libraryPromptSelect) return;
    
    // Add colored selection states to dropdown
    const options = libraryPromptSelect.querySelectorAll('option');
    options.forEach((option, index) => {
        if (index === 0) return; // Skip the default option
        
        // Add data attribute for category-based coloring
        option.setAttribute('data-category', 'library');
    });
    
    // Apply library source colors to the select element
    const sourceConfig = PROMPT_SOURCE_COLORS[PROMPT_SOURCES.LIBRARY];
    if (sourceConfig) {
        libraryPromptSelect.classList.add(`text-${sourceConfig.color}-primary`);
        libraryPromptSelect.classList.add(`border-${sourceConfig.color}-primary`);
        libraryPromptSelect.classList.add(`focus-${sourceConfig.color}`);
    }
}

/**
 * Enhanced library prompt preview with colors
 */
function updateLibraryPromptPreviewColors() {
    if (!libraryPromptPreview) return;
    
    const sourceConfig = PROMPT_SOURCE_COLORS[PROMPT_SOURCES.LIBRARY];
    if (sourceConfig) {
        libraryPromptPreview.classList.add('prompt-preview-area', 'has-content');
        libraryPromptPreview.classList.add(`border-${sourceConfig.color}-primary`);
    }
}

/**
 * Get the current active system prompt content
 * This function is used by the conversation history builder
 */
function getCurrentSystemPromptContent() {
    const source = currentSystemPromptConfig.source;
    
    switch (source) {
        case PROMPT_SOURCES.FILE:
            if (typeof getDefaultBuiltInPrompt === 'function') {
                const builtInPrompt = getDefaultBuiltInPrompt();
                return builtInPrompt ? builtInPrompt.content : '';
            }
            return '';
        case PROMPT_SOURCES.MANUAL:
            return systemPromptInput ? systemPromptInput.value.trim() : '';
        case PROMPT_SOURCES.LIBRARY:
            return currentSystemPromptConfig.content || '';
        default:
            return '';
    }
}

/**
 * Refresh library prompts (call this when library is updated)
 */
function refreshLibraryPrompts() {
    populateLibraryPrompts();
}

/**
 * Programmatically set a library prompt as active
 * @param {string} promptId - The ID of the prompt from the library
 */
function setLibraryPrompt(promptId) {
    const prompts = getPrompts();
    const selectedPrompt = prompts[promptId];
    
    if (selectedPrompt) {
        // Update configuration
        currentSystemPromptConfig.source = PROMPT_SOURCES.LIBRARY;
        currentSystemPromptConfig.promptId = promptId;
        currentSystemPromptConfig.content = selectedPrompt.content;
        
        // Update UI
        if (systemPromptSource) {
            systemPromptSource.value = PROMPT_SOURCES.LIBRARY;
        }
        updateSystemPromptUI();
        
        // Update library selector
        if (libraryPromptSelect) {
            libraryPromptSelect.value = promptId;
        }
        
        // Update preview
        if (libraryPromptPreview) {
            libraryPromptPreview.textContent = selectedPrompt.content;
            libraryPromptPreview.classList.remove('hidden');
        }
        
        // Save configuration
        saveSystemPromptConfig();
        updateSystemPromptStatus();
        
        return true;
    }
    
    return false;
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeSystemPromptManager,
        getCurrentSystemPromptContent,
        refreshLibraryPrompts,
        setLibraryPrompt,
        PROMPT_SOURCES
    };
}