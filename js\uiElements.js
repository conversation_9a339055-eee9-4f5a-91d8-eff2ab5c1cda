// DOM Elements
const userInput = document.getElementById('userInput');
const sendBtn = document.getElementById('sendBtn');
// const messagesContainer = document.getElementById('messagesContainer'); // To be defined in main.js after DOMContentLoaded
const clearChat = document.getElementById('clearChat');
const toggleParamsBtnHeader = document.getElementById('toggleParamsBtnHeader');
const paramsPanel = document.getElementById('paramsPanel');
const closeParamsModal = document.getElementById('closeParamsModal');
const apiProviderSelect = document.getElementById('apiProviderSelect');
const chutesApiTokenField = document.getElementById('chutesApiTokenField');
const openrouterApiTokenField = document.getElementById('openrouterApiTokenField');
const apiToken = document.getElementById('apiToken');
const openrouterApiToken = document.getElementById('openrouterApiToken');
const toggleTokenVisibility = document.getElementById('toggleTokenVisibility');
const toggleOpenrouterTokenVisibility = document.getElementById('toggleOpenrouterTokenVisibility');
const modelSelect = document.getElementById('modelSelect');
const temperature = document.getElementById('temperature');
const tempValue = document.getElementById('tempValue');
const maxTokens = document.getElementById('maxTokens');
const maxTokensValue = document.getElementById('maxTokensValue');
const streamCheckbox = document.getElementById('streamCheckbox');
const testConnectionBtn = document.getElementById('testConnectionBtn');
const charCount = document.getElementById('charCount');
const themeToggle = document.getElementById('themeToggle');
const saveSettingsBtn = document.getElementById('saveSettingsBtn');
const settingsDarkBtn = document.getElementById('settingsDarkBtn');
const settingsLightBtn = document.getElementById('settingsLightBtn');
// System Prompt Editor Elements
const systemPromptInput = document.getElementById('systemPromptInput');
const expandSystemPromptBtn = document.getElementById('expandSystemPromptBtn');
const systemPromptIndicator = document.getElementById('systemPromptIndicator');
// Note: systemPromptEditorModal and related elements removed - now using expanded settings panel
// System Prompt Source Elements
const systemPromptSource = document.getElementById('systemPromptSource');
const systemPromptContainer = document.getElementById('systemPromptContainer');
const filePromptPreview = document.getElementById('filePromptPreview');
const filePromptText = document.getElementById('filePromptText');
const copyFilePromptBtn = document.getElementById('copyFilePromptBtn');
const manualPromptInput = document.getElementById('manualPromptInput');
const libraryPromptSelector = document.getElementById('libraryPromptSelector');
const libraryPromptSelect = document.getElementById('libraryPromptSelect');
const libraryPromptPreview = document.getElementById('libraryPromptPreview');
const systemPromptStatus = document.getElementById('systemPromptStatus');
const systemPromptStatusText = document.getElementById('systemPromptStatusText');
