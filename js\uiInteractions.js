// @ts-nocheck
/* eslint-disable */

// Adds null checks and safe access wrappers
function safeGetElementById(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`Element not found: ${id}`);
    }
    return element;
}

function safeQuerySelector(selector) {
    const element = document.querySelector(selector);
    if (!element) {
        console.warn(`Element not found: ${selector}`);
    }
    return element;
}

function safeQuerySelectorAll(selector) {
    const elements = document.querySelectorAll(selector);
    if (elements.length === 0) {
        console.warn(`No elements found: ${selector}`);
    }
    return elements;
}

function safeAddEventListener(element, event, handler, options = {}) {
    if (!element) {
        console.warn(`Cannot add listener, element is null for event: ${event}`);
        return;
    }

    try {
        element.addEventListener(event, handler, options);
    } catch (error) {
        console.error(`Failed to add ${event} listener:`, error);
    }
}

function safeRemoveEventListener(element, event, handler) {
    if (!element) {
        console.warn(`Cannot remove listener, element is null for event: ${event}`);
        return;
    }

    try {
        element.removeEventListener(event, handler);
    } catch (error) {
        console.error(`Failed to remove ${event} listener:`, error);
    }
}

function safeSetTimeout(callback, delay) {
    if (typeof callback !== 'function') {
        console.warn('Callback is not a function');
        return;
    }

    try {
        return setTimeout(callback, delay);
    } catch (error) {
        console.error('Failed to set timeout:', error);
    }
}

function safeSetInterval(callback, interval) {
    if (typeof callback !== 'function') {
        console.warn('Callback is not a function');
        return;
    }

    try {
        return setInterval(callback, interval);
    } catch (error) {
        console.error('Failed to set interval:', error);
    }
}

// ========================================
// DEPENDENCY VALIDATION AND INITIALIZATION
// ========================================

/**
 * Initialize and validate dependencies from uiElements.js
 * This function ensures all required global variables are available
 */
function initializeUIInteractionsDependencies() {
    console.log('🔍 Validating UI Interactions dependencies...');
    
    const requiredElements = {
        userInput: 'userInput',
        charCount: 'charCount', 
        clearInputBtn: 'clearInputBtn',
        sendBtn: 'sendBtn',
        messagesContainer: 'messagesContainer',
        systemPromptInput: 'systemPromptInput'
    };
    
    const missingElements = [];
    const foundElements = {};
    
    // Check global variables first
    Object.entries(requiredElements).forEach(([varName, elementId]) => {
        if (typeof window[varName] !== 'undefined' && window[varName]) {
            foundElements[varName] = window[varName];
            console.log(`✅ Found global variable: ${varName}`);
        } else {
            // Try to find element by ID as fallback
            const element = document.getElementById(elementId);
            if (element) {
                window[varName] = element;
                foundElements[varName] = element;
                console.log(`✅ Found and assigned element: ${varName} (fallback)`);
            } else {
                missingElements.push(varName);
                console.warn(`⚠️ Missing element: ${varName}`);
            }
        }
    });
    
    // Check for required functions from other modules
    const requiredFunctions = [
        'showAlert',
        'cancelStream', 
        'escapeHtml',
        'getPrompts',
        'savePrompt',
        'deletePrompt',
        'updateSystemPromptIndicator',
        'toggleParamsPanel'
    ];
    
    const missingFunctions = [];
    requiredFunctions.forEach(funcName => {
        if (typeof window[funcName] !== 'function') {
            missingFunctions.push(funcName);
            console.warn(`⚠️ Missing function: ${funcName}`);
        } else {
            console.log(`✅ Found function: ${funcName}`);
        }
    });
    
    // Create safe wrapper functions for missing dependencies
    createSafeWrappers(missingFunctions);
    
    // Report initialization status
    if (missingElements.length === 0 && missingFunctions.length === 0) {
        console.log('✅ All UI Interactions dependencies validated successfully');
        return true;
    } else {
        console.warn(`⚠️ UI Interactions initialized with ${missingElements.length} missing elements and ${missingFunctions.length} missing functions`);
        return false;
    }
}

/**
 * Create safe wrapper functions for missing dependencies
 */
function createSafeWrappers(missingFunctions) {
    const safeWrappers = {
        showAlert: (message, type) => {
            console.log(`Alert [${type}]: ${message}`);
            // Fallback to basic alert if available
            if (typeof alert !== 'undefined') {
                alert(`[${type.toUpperCase()}] ${message}`);
            }
        },
        cancelStream: (loadingId) => {
            console.log(`Cancel stream requested for: ${loadingId}`);
            return false;
        },
        escapeHtml: (text) => {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },
        getPrompts: () => {
            console.warn('getPrompts not available, returning empty object');
            return {};
        },
        savePrompt: (id, title, content) => {
            console.log(`Save prompt: ${id} - ${title}`);
            return false;
        },
        deletePrompt: (id) => {
            console.log(`Delete prompt: ${id}`);
            return false;
        },
        updateSystemPromptIndicator: () => {
            console.log('Update system prompt indicator requested');
        },
        toggleParamsPanel: () => {
            console.log('Toggle params panel requested');
        }
    };
    
    missingFunctions.forEach(funcName => {
        if (safeWrappers[funcName]) {
            window[funcName] = safeWrappers[funcName];
            console.log(`🛡️ Created safe wrapper for: ${funcName}`);
        }
    });
}

/**
 * Safe element access function
 */
function safeElementAccess(element, operation, fallback = null) {
    if (!element) {
        console.warn(`Cannot perform operation on null element: ${operation}`);
        return fallback;
    }
    
    try {
        return operation(element);
    } catch (error) {
        console.error(`Error performing operation on element: ${error.message}`);
        return fallback;
    }
}

function updateCharCount() {
    if (userInput && charCount) { // Ensure elements exist
        const count = userInput.value.length;
        charCount.textContent = `${count}/1000`;

        if (count > 1000) {
            charCount.classList.add('text-red-500');
            charCount.classList.remove('dark:text-sora-gray-light');
        } else {
            charCount.classList.remove('text-red-500');
            charCount.classList.add('dark:text-sora-gray-light');
        }
    }
}

// Function to handle clicks within the messages container
function handleMessageContainerClick(event) {
    const messagesContainer = document.getElementById('messagesContainer');
    if (!messagesContainer) return;

    // Handle Stop Button Click
    const stopButton = event.target.closest('.stop-button');
    if (stopButton) {
        const loadingId = stopButton.dataset.loadingId;
        if (loadingId && typeof cancelStream === 'function') {
            const cancelled = cancelStream(loadingId);
            if (cancelled) {
                // Hide the stop button after cancellation
                stopButton.style.display = 'none';
                if (typeof showAlert === 'function') {
                    showAlert('Stream cancelled', 'info');
                }
            }
        }
        return;
    }

    // Handle Code Block Click (enhanced version with colored feedback)
    const codeBlock = event.target.closest('pre code');
    if (codeBlock) {
        // Use enhanced code block system
        EnhancedCodeBlocks.initializeCodeBlock(codeBlock);
        return;
    }

    // Handle General Copy Button Click (for non-code block messages)
    const copyButton = event.target.closest('.copy-button:not(.code-copy-btn)'); // Exclude code-copy-btn
    if (copyButton) {
        const rawContent = copyButton.dataset.rawContent;
        if (rawContent) {
            // Ensure escapeHtml is available (e.g. from utils.js)
            const cleanedContent = typeof escapeHtml === 'function' ? rawContent.replace(/([*_`~#])/g, '') : rawContent.replace(/([*_`~#])/g, ''); // Basic cleaning
            navigator.clipboard.writeText(cleanedContent).then(() => {
                const icon = copyButton.querySelector('i');
                if (icon) icon.classList.replace('fa-copy', 'fa-check');
                if (typeof showAlert === 'function') showAlert('Copied to clipboard!', 'success');
                setTimeout(() => {
                    if (icon) icon.classList.replace('fa-check', 'fa-copy');
                }, 1500);
            }).catch(err => {
                console.error('Clipboard write failed:', err);
                if (typeof showAlert === 'function') showAlert('Failed to copy text.', 'error');
            });
        }
        return;
    }
}

// Feature 1: Dynamic Textarea Resizing
function resizeTextarea() {
    if (!userInput) return; // Ensure userInput element exists

    // Get max-height from CSS (e.g., "7.5rem") and convert to pixels
    // This is a simplified approach. A more robust method would be to use getComputedStyle
    // or CSS custom properties if the value is dynamic or complex.
    const cssMaxHeight = getComputedStyle(userInput).maxHeight; // e.g., "120px" or "7.5rem"
    let maxHeightInPx;

    if (cssMaxHeight.endsWith('px')) {
        maxHeightInPx = parseFloat(cssMaxHeight);
    } else if (cssMaxHeight.endsWith('rem')) {
        // Assuming 1rem = 16px (browser default). This might not always be accurate.
        // A better way is to get the root font size: parseFloat(getComputedStyle(document.documentElement).fontSize)
        const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
        maxHeightInPx = parseFloat(cssMaxHeight) * rootFontSize;
    } else {
        // Fallback if parsing fails, e.g. if max-height is 'none' or an unexpected unit
        // Use a sensible default or log an error. For now, using a default based on 5 lines.
        const approxLineHeight = parseFloat(getComputedStyle(userInput).lineHeight) || 24; // Approx line height in px
        maxHeightInPx = approxLineHeight * 5; // Default to 5 lines
        console.warn(`Could not parse userInput maxHeight ('${cssMaxHeight}'). Falling back to ${maxHeightInPx}px.`);
    }


    userInput.style.height = 'auto'; // Temporarily shrink to content
    let newHeight = userInput.scrollHeight;

    if (newHeight > maxHeightInPx) {
        userInput.style.height = maxHeightInPx + 'px';
        userInput.style.overflowY = 'auto';
    } else {
        userInput.style.height = newHeight + 'px';
        userInput.style.overflowY = 'hidden';
    }
}

// Initialize and add event listener for textarea resizing
// Ensure this runs after userInput is defined and the DOM is ready.
// If uiElements.js defines userInput, this should be fine if uiInteractions.js is loaded after it.
// Otherwise, wrap in DOMContentLoaded or ensure uiElements are available.
if (userInput) {
    userInput.addEventListener('input', resizeTextarea);
    // Initial resize on page load
    // Add a small delay to ensure styles are applied, especially if loaded async
    setTimeout(resizeTextarea, 50);
} else {
    // Fallback if userInput is not immediately available
    document.addEventListener('DOMContentLoaded', () => {
        // userInput should be globally available or from uiElements.js
        // This assumes uiElements.js has run and populated global/exported variables
        if (typeof userInput !== 'undefined' && userInput) {
            userInput.addEventListener('input', resizeTextarea);
            setTimeout(resizeTextarea, 50); // Initial resize
        } else {
            console.error("userInput element not found for dynamic resizing.");
        }
    });
}
// Feature 2: "Clear Input" Button
function setupClearInputButton() {
    // Assume clearInputBtn and userInput are available (e.g., from uiElements.js or globally)
    // If not, ensure they are selected here or passed as arguments.
    const clearButton = clearInputBtn; // Assuming clearInputBtn is a global const from uiElements.js
    const inputField = userInput; // Assuming userInput is a global const from uiElements.js

    if (!clearButton || !inputField) {
        console.error("Clear input button or user input field not found.");
        return;
    }

    const toggleClearButtonVisibility = () => {
        if (inputField.value.length > 0) {
            clearButton.classList.remove('hidden');
        } else {
            clearButton.classList.add('hidden');
        }
    };

    inputField.addEventListener('input', () => {
        toggleClearButtonVisibility();
        // resizeTextarea(); // resizeTextarea is already called by its own listener
    });

    clearButton.addEventListener('click', () => {
        inputField.value = '';
        clearButton.classList.add('hidden');
        // Dispatch an input event to trigger other listeners (like char count, resize)
        inputField.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
        inputField.focus();
    });

    // Initial check for visibility on page load
    toggleClearButtonVisibility();
}

// Call setup function
// Ensure this runs after clearInputBtn and userInput are defined and the DOM is ready.
// Similar to resizeTextarea, this depends on when uiElements.js runs.
if (typeof clearInputBtn !== 'undefined' && clearInputBtn && typeof userInput !== 'undefined' && userInput) {
    setupClearInputButton();
} else {
    document.addEventListener('DOMContentLoaded', () => {
        // This assumes uiElements.js has run and populated global/exported variables
        if (typeof clearInputBtn !== 'undefined' && clearInputBtn && typeof userInput !== 'undefined' && userInput) {
            setupClearInputButton();
        } else {
            console.error("userInput or clearInputBtn element not found for clear button setup.");
            // Attempt to select them if not globally available
            const localClearInputBtn = document.getElementById('clearInputBtn');
            const localUserInput = document.getElementById('userInput');
            if (localClearInputBtn && localUserInput) {
                // Make them available for the function if found
                window.clearInputBtn = localClearInputBtn; // Or pass as params
                window.userInput = localUserInput; // Or pass as params
                setupClearInputButton();
            } else {
                console.error("Failed to find userInput or clearInputBtn even after DOMContentLoaded.");
            }
        }
    });
}
// --- Prompt Library Modal ---

function populatePromptList() {
    const promptListContainer = document.getElementById('promptListContainer');
    if (!promptListContainer) return;

    promptListContainer.innerHTML = ''; // Clear existing list
    const prompts = getPrompts();
    const sortedPrompts = Object.entries(prompts).sort((a, b) => a[1].title.localeCompare(b[1].title));

    if (sortedPrompts.length === 0) {
        promptListContainer.innerHTML = `<p class="text-center text-sm text-gray-500 dark:text-sora-gray-light p-4">No prompts saved yet.</p>`;
        return;
    }

    for (const [id, prompt] of sortedPrompts) {
        const item = document.createElement('div');
        item.className = 'p-3 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-sora-gray-medium transition-colors border border-transparent hover:border-gray-200 dark:hover:border-sora-gray-medium';
        item.dataset.id = id;

        // Create main container with title and use button
        const headerDiv = document.createElement('div');
        headerDiv.className = 'flex justify-between items-center mb-2';

        const titleSpan = document.createElement('span');
        titleSpan.textContent = prompt.title;
        titleSpan.className = 'flex-grow font-medium text-gray-900 dark:text-white text-sm';

        const useButton = document.createElement('button');
        useButton.className = 'p-1 rounded text-gray-400 hover:text-primary-500 dark:hover:text-white transition-colors ml-2 flex-shrink-0';
        useButton.innerHTML = '<i class="fas fa-arrow-right-to-bracket"></i>';
        useButton.title = 'Use this prompt';

        // Add title and button to header
        headerDiv.appendChild(titleSpan);
        headerDiv.appendChild(useButton);

        // Create preview content
        const previewDiv = document.createElement('div');
        previewDiv.className = 'text-xs text-gray-600 dark:text-sora-gray-light leading-relaxed mt-1 line-clamp-2';

        // Smart truncation - try to break at word boundaries
        let previewText = prompt.content;
        if (prompt.content.length > 150) {
            const truncated = prompt.content.substring(0, 150);
            const lastSpace = truncated.lastIndexOf(' ');
            previewText = lastSpace > 100 ? truncated.substring(0, lastSpace) + '...' : truncated + '...';
        }

        previewDiv.textContent = previewText;
        previewDiv.title = prompt.content; // Show full content on hover

        // Add all elements to item
        item.appendChild(headerDiv);
        item.appendChild(previewDiv);

        useButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent the item click from firing

            // Use the new system prompt manager to set library prompt
            if (typeof setLibraryPrompt === 'function') {
                const success = setLibraryPrompt(id);
                if (success) {
                    showAlert('Prompt applied!', 'success');
                    if (typeof updateSystemPromptIndicator === 'function') {
                        updateSystemPromptIndicator();
                    }
                    // Close both modals
                    const promptLibraryModal = document.getElementById('promptLibraryModal');
                    if (promptLibraryModal) {
                        const toggleModal = (show) => {
                            if (show) {
                                promptLibraryModal.classList.remove('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
                                promptLibraryModal.classList.add('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
                            } else {
                                promptLibraryModal.classList.remove('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
                                promptLibraryModal.classList.add('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
                            }
                        };
                        toggleModal(false);
                    }
                    toggleParamsPanel(); // Also close the main settings panel
                } else {
                    showAlert('Failed to apply prompt', 'error');
                }
            } else {
                // Fallback to old method if new system is not available
                const prompts = getPrompts();
                const selectedPrompt = prompts[id];
                if (selectedPrompt) {
                    systemPromptInput.value = selectedPrompt.content;
                    showAlert('Prompt applied!', 'success');
                    updateSystemPromptIndicator();
                    // Close both modals
                    const promptLibraryModal = document.getElementById('promptLibraryModal');
                    if (promptLibraryModal) {
                        const toggleModal = (show) => {
                            if (show) {
                                promptLibraryModal.classList.remove('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
                                promptLibraryModal.classList.add('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
                            } else {
                                promptLibraryModal.classList.remove('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
                                promptLibraryModal.classList.add('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
                            }
                        };
                        toggleModal(false);
                    }
                    toggleParamsPanel(); // Also close the main settings panel
                }
            }
        });

        item.addEventListener('click', () => {
            const selected = document.querySelector('.prompt-item-selected');
            if (selected) {
                selected.classList.remove('prompt-item-selected', 'bg-primary-100', 'dark:bg-primary-900');
            }
            item.classList.add('prompt-item-selected', 'bg-primary-100', 'dark:bg-primary-900');
            loadPromptIntoEditor(id);
        });

        promptListContainer.appendChild(item);
    }
}

function loadPromptIntoEditor(id) {
    const prompts = getPrompts();
    const prompt = prompts[id];
    if (prompt) {
        document.getElementById('promptTitleInput').value = prompt.title;
        document.getElementById('promptContentTextarea').value = prompt.content;
        document.getElementById('savePromptBtn').dataset.id = id;
        document.getElementById('deletePromptBtn').dataset.id = id;
        document.getElementById('deletePromptBtn').classList.remove('hidden');
    }
}

function clearEditor() {
    document.getElementById('promptTitleInput').value = '';
    document.getElementById('promptContentTextarea').value = '';
    document.getElementById('savePromptBtn').removeAttribute('data-id');
    document.getElementById('deletePromptBtn').removeAttribute('data-id');
    document.getElementById('deletePromptBtn').classList.add('hidden');
    const selected = document.querySelector('.prompt-item-selected');
    if (selected) {
        selected.classList.remove('prompt-item-selected', 'bg-primary-100', 'dark:bg-primary-900');
    }
}

function setupPromptLibraryModal() {
    const promptLibraryBtn = document.getElementById('promptLibraryBtn');
    const promptLibraryModal = document.getElementById('promptLibraryModal');
    const closePromptLibraryBtn = document.getElementById('closePromptLibraryBtn');
    const addNewPromptBtn = document.getElementById('addNewPromptBtn');
    const savePromptBtn = document.getElementById('savePromptBtn');
    const deletePromptBtn = document.getElementById('deletePromptBtn');

    if (!promptLibraryBtn || !promptLibraryModal || !closePromptLibraryBtn || !addNewPromptBtn || !savePromptBtn || !deletePromptBtn) {
        console.error('Prompt library modal elements not found.');
        return;
    }

    const toggleModal = (show) => {
        if (show) {
            populatePromptList();
            clearEditor();
            promptLibraryModal.classList.remove('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
            promptLibraryModal.classList.add('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
        } else {
            promptLibraryModal.classList.remove('opacity-100', 'scale-100', 'visible', 'pointer-events-auto');
            promptLibraryModal.classList.add('opacity-0', 'scale-95', 'invisible', 'pointer-events-none');
        }
    };

    promptLibraryBtn.addEventListener('click', () => toggleModal(true));
    closePromptLibraryBtn.addEventListener('click', () => toggleModal(false));
    addNewPromptBtn.addEventListener('click', clearEditor);

    savePromptBtn.addEventListener('click', () => {
        const id = savePromptBtn.dataset.id || `prompt_${Date.now()}`;
        const title = document.getElementById('promptTitleInput').value.trim();
        const content = document.getElementById('promptContentTextarea').value.trim();

        if (!title || !content) {
            showAlert('Title and content cannot be empty.', 'error');
            return;
        }

        savePrompt(id, title, content);
        showAlert('Prompt saved!', 'success');
        populatePromptList();

        // Refresh library prompts in system prompt manager
        if (typeof refreshLibraryPrompts === 'function') {
            refreshLibraryPrompts();
        }
        // Highlight the saved prompt
        setTimeout(() => {
            const items = document.querySelectorAll('#promptListContainer > div');
            items.forEach(item => {
                if (item.dataset.id === id) {
                    item.classList.add('prompt-item-selected', 'bg-primary-100', 'dark:bg-primary-900');
                    loadPromptIntoEditor(id);
                }
            });
        }, 100);
    });

    deletePromptBtn.addEventListener('click', () => {
        const id = deletePromptBtn.dataset.id;
        if (id && confirm('Are you sure you want to delete this prompt?')) {
            deletePrompt(id);
            showAlert('Prompt deleted.', 'success');
            populatePromptList();
            clearEditor();

            // Refresh library prompts in system prompt manager
            if (typeof refreshLibraryPrompts === 'function') {
                refreshLibraryPrompts();
            }
        }
    });

    // Close with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !promptLibraryModal.classList.contains('invisible')) {
            toggleModal(false);
        }
    });
}

// Removed individual DOMContentLoaded - will be handled by centralized system

// Enhanced Interactive Element Feedback System

/**
 * Dynamic Color State Management
 */
const ColorStateManager = {
    // Track current color states for elements
    elementStates: new Map(),

    // Apply contextual color to an element based on context and state
    applyContextualColor: function (element, context, state) {
        if (!element) return;

        // Remove existing color classes
        this.removeColorClasses(element);

        // Apply new color classes based on context and state
        const colorClass = this.getColorClass(context, state);
        if (colorClass) {
            element.classList.add(colorClass);
            this.elementStates.set(element, { context, state, colorClass });
        }
    },

    // Get appropriate color class based on context and state
    getColorClass: function (context, state) {
        const colorMap = {
            'user': {
                'default': 'text-blue-primary',
                'hover': 'text-blue-light',
                'active': 'text-blue-secondary',
                'focus': 'focus-ring-blue'
            },
            'assistant': {
                'default': 'text-purple-primary',
                'hover': 'text-purple-light',
                'active': 'text-purple-secondary',
                'focus': 'focus-ring-purple'
            },
            'system': {
                'default': 'text-green-primary',
                'hover': 'text-green-light',
                'active': 'text-green-secondary',
                'focus': 'focus-ring-green'
            },
            'warning': {
                'default': 'text-orange-primary',
                'hover': 'text-orange-light',
                'active': 'text-orange-secondary',
                'focus': 'focus-ring-orange'
            },
            'success': {
                'default': 'text-green-primary',
                'hover': 'text-green-light',
                'active': 'text-green-secondary',
                'focus': 'focus-ring-green'
            },
            'error': {
                'default': 'text-error',
                'hover': 'text-red-400',
                'active': 'text-red-600',
                'focus': 'focus-ring-red'
            }
        };

        return colorMap[context] && colorMap[context][state] ? colorMap[context][state] : null;
    },

    // Remove existing color classes from element
    removeColorClasses: function (element) {
        const colorClasses = [
            'text-blue-primary', 'text-blue-secondary', 'text-blue-light',
            'text-purple-primary', 'text-purple-secondary', 'text-purple-light',
            'text-green-primary', 'text-green-secondary', 'text-green-light',
            'text-orange-primary', 'text-orange-secondary', 'text-orange-light',
            'text-error', 'text-red-400', 'text-red-600',
            'focus-ring-blue', 'focus-ring-purple', 'focus-ring-green', 'focus-ring-orange', 'focus-ring-red'
        ];

        element.classList.remove(...colorClasses);
    },

    // Update status indicator with appropriate color
    updateStatusIndicator: function (element, status) {
        if (!element) return;

        this.removeColorClasses(element);

        const statusColors = {
            'connected': 'status-connected',
            'disconnected': 'status-disconnected',
            'connecting': 'status-connecting',
            'loading': 'status-loading',
            'success': 'text-success',
            'error': 'text-error',
            'warning': 'text-warning'
        };

        const colorClass = statusColors[status];
        if (colorClass) {
            element.classList.add(colorClass);
            this.elementStates.set(element, { status, colorClass });
        }
    }
};

/**
 * Color Transition Utilities
 */
const ColorTransitions = {
    // Trigger smooth color transition between states
    triggerColorTransition: function (element, fromColor, toColor, duration = 300) {
        if (!element) return;

        // Add transition class
        element.classList.add('btn-state-transition');

        // Apply from color
        if (fromColor) {
            element.classList.add(fromColor);
        }

        // Transition to new color after a brief delay
        setTimeout(() => {
            if (fromColor) {
                element.classList.remove(fromColor);
            }
            if (toColor) {
                element.classList.add(toColor);
            }
        }, 50);

        // Remove transition class after animation completes
        setTimeout(() => {
            element.classList.remove('btn-state-transition');
        }, duration);
    },

    // Animate element state change with color feedback
    animateStateChange: function (element, newState, context = 'default') {
        if (!element) return;

        const stateClasses = {
            'idle': 'btn-state-idle',
            'hover': 'btn-state-hover',
            'active': 'btn-state-active',
            'loading': 'btn-state-loading',
            'disabled': 'btn-state-disabled'
        };

        // Remove all state classes
        Object.values(stateClasses).forEach(cls => element.classList.remove(cls));

        // Add new state class
        const stateClass = stateClasses[newState];
        if (stateClass) {
            element.classList.add(stateClass);
        }

        // Apply contextual color
        ColorStateManager.applyContextualColor(element, context, newState);
    }
};

/**
 * Enhanced Feedback Systems
 */
const FeedbackSystems = {
    // Show colored toast notification
    showColoredToast: function (message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;

        // Apply color based on type
        const typeColors = {
            'success': 'bg-green-primary text-white',
            'error': 'bg-error text-white',
            'warning': 'bg-orange-primary text-white',
            'info': 'bg-blue-primary text-white'
        };

        toast.classList.add(...(typeColors[type] || typeColors['info']).split(' '));
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Animate out and remove
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    },

    // Create loading indicator with color
    createLoadingIndicator: function (parent, color = 'blue') {
        if (!parent) return null;

        const loader = document.createElement('div');
        loader.className = `loading-pulse-${color} w-4 h-4 rounded-full inline-block`;
        loader.setAttribute('aria-label', 'Loading');

        parent.appendChild(loader);
        return loader;
    },

    // Remove loading indicator
    removeLoadingIndicator: function (loader) {
        if (loader && loader.parentNode) {
            loader.parentNode.removeChild(loader);
        }
    },

    // Show success feedback with animation
    showSuccessFeedback: function (element, message = 'Success!') {
        if (!element) return;

        element.classList.add('feedback-success');

        // Show temporary success message
        const originalText = element.textContent;
        element.textContent = message;

        setTimeout(() => {
            element.classList.remove('feedback-success');
            element.textContent = originalText;
        }, 1500);
    },

    // Show error feedback with animation
    showErrorFeedback: function (element, message = 'Error!') {
        if (!element) return;

        element.classList.add('feedback-error');

        // Show temporary error message
        const originalText = element.textContent;
        element.textContent = message;

        setTimeout(() => {
            element.classList.remove('feedback-error');
            element.textContent = originalText;
        }, 1500);
    }
};

/**
 * Interactive Animation Controllers
 */
const AnimationControllers = {
    // Add hover effects with color changes
    addHoverEffects: function (element, context = 'default') {
        if (!element) return;

        element.addEventListener('mouseenter', () => {
            ColorStateManager.applyContextualColor(element, context, 'hover');
            element.classList.add('transform', 'scale-105', 'transition-all', 'duration-200');
        });

        element.addEventListener('mouseleave', () => {
            ColorStateManager.applyContextualColor(element, context, 'default');
            element.classList.remove('transform', 'scale-105');
        });
    },

    // Add pulse effect for active elements
    addPulseEffect: function (element, color = 'blue') {
        if (!element) return;

        element.classList.add(`pulse-${color}`, 'animate-pulse');

        // Remove pulse after 2 seconds
        setTimeout(() => {
            element.classList.remove(`pulse-${color}`, 'animate-pulse');
        }, 2000);
    },

    // Add glow effect for focus states
    addGlowEffect: function (element, color = 'blue') {
        if (!element) return;

        element.addEventListener('focus', () => {
            element.classList.add(`glow-${color}`, 'transition-shadow', 'duration-200');
        });

        element.addEventListener('blur', () => {
            element.classList.remove(`glow-${color}`);
        });
    },

    // Add bounce animation for button interactions
    addBounceAnimation: function (element) {
        if (!element) return;

        element.addEventListener('click', () => {
            element.classList.add('animate-bounce');
            setTimeout(() => {
                element.classList.remove('animate-bounce');
            }, 500);
        });
    },

    // Add smooth color transition effects for focus states
    addFocusTransitions: function (element, context = 'default') {
        if (!element) return;

        element.addEventListener('focus', () => {
            ColorStateManager.applyContextualColor(element, context, 'focus');
            element.classList.add('transition-colors', 'duration-200');
        });

        element.addEventListener('blur', () => {
            ColorStateManager.applyContextualColor(element, context, 'default');
        });
    }
};

/**
 * Status Management Integration Functions
 */

/**
 * Initialize comprehensive status indicators across UI components
 */
function initializeStatusIndicators() {
    // Initialize connection status indicators
    initializeConnectionStatusIndicators();

    // Initialize form validation indicators
    initializeFormValidationIndicators();

    // Initialize loading state indicators
    initializeLoadingStateIndicators();

    // Initialize save confirmation indicators
    initializeSaveConfirmationIndicators();
}

/**
 * Initialize connection status indicators
 */
function initializeConnectionStatusIndicators() {
    // Add connection status to API settings section
    const apiSection = document.querySelector('[data-category="api"]') ||
        document.getElementById('apiProviderSelect')?.parentNode;

    if (apiSection && !document.getElementById('connectionStatusIndicator')) {
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'connectionStatusIndicator';
        statusIndicator.className = 'status-indicator status-disconnected hidden';
        statusIndicator.innerHTML = '<i class="fas fa-times-circle"></i> <span>Not Connected</span>';

        apiSection.appendChild(statusIndicator);
    }
}

/**
 * Initialize form validation indicators
 */
function initializeFormValidationIndicators() {
    const inputsToValidate = [
        { id: 'apiToken', category: 'api' },
        { id: 'openrouterApiToken', category: 'api' },
        { id: 'systemPromptInput', category: 'prompt' },
        { id: 'modelSelect', category: 'api' }
    ];

    inputsToValidate.forEach(input => {
        const element = document.getElementById(input.id);
        if (element && !element.parentNode.querySelector('.validation-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = `validation-indicator status-indicator-sm hidden`;
            indicator.innerHTML = '<i class="fas fa-check-circle"></i> <span>Valid</span>';

            element.parentNode.appendChild(indicator);

            // Add real-time validation
            element.addEventListener('input', () => {
                validateInputField(input.id, input.category);
            });

            element.addEventListener('blur', () => {
                validateInputField(input.id, input.category);
            });
        }
    });
}

/**
 * Initialize loading state indicators
 */
function initializeLoadingStateIndicators() {
    const buttonsWithLoading = [
        'testConnectionBtn',
        'saveSettingsBtn',
        'promptLibraryBtn'
    ];

    buttonsWithLoading.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button && !button.querySelector('.loading-indicator')) {
            // Add loading indicator container
            const loadingContainer = document.createElement('span');
            loadingContainer.className = 'loading-indicator hidden';
            loadingContainer.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            button.appendChild(loadingContainer);
        }
    });
}

/**
 * Initialize save confirmation indicators
 */
function initializeSaveConfirmationIndicators() {
    const saveButton = document.querySelector('button[onclick*="saveSettings"]') ||
        document.getElementById('saveSettingsBtn');

    if (saveButton && !document.getElementById('saveConfirmationIndicator')) {
        const confirmationIndicator = document.createElement('div');
        confirmationIndicator.id = 'saveConfirmationIndicator';
        confirmationIndicator.className = 'status-indicator status-connected hidden';
        confirmationIndicator.innerHTML = '<i class="fas fa-check"></i> <span>Saved!</span>';

        saveButton.parentNode.insertBefore(confirmationIndicator, saveButton.nextSibling);
    }
}

/**
 * Validate input field and show colored feedback
 */
function validateInputField(inputId, category) {
    const input = document.getElementById(inputId);
    const indicator = input?.parentNode.querySelector('.validation-indicator');

    if (!input || !indicator) return;

    let isValid = false;
    let message = '';

    switch (inputId) {
        case 'apiToken':
        case 'openrouterApiToken':
            isValid = input.value.trim().length > 0;
            message = isValid ? 'Token provided' : 'Token required';
            break;

        case 'systemPromptInput':
            isValid = input.value.trim().length > 0;
            message = isValid ? 'Prompt configured' : 'No system prompt';
            break;

        case 'modelSelect':
            isValid = input.value && input.value !== '';
            message = isValid ? 'Model selected' : 'Select a model';
            break;

        default:
            isValid = input.value.trim().length > 0;
            message = isValid ? 'Valid' : 'Required';
    }

    // Update indicator
    if (isValid) {
        indicator.className = 'validation-indicator status-indicator-sm status-connected';
        indicator.innerHTML = '<i class="fas fa-check-circle"></i> <span>' + message + '</span>';
    } else {
        indicator.className = 'validation-indicator status-indicator-sm status-error';
        indicator.innerHTML = '<i class="fas fa-exclamation-circle"></i> <span>' + message + '</span>';
    }

    indicator.classList.remove('hidden');

    // Auto-hide success messages after 3 seconds
    if (isValid) {
        setTimeout(() => {
            indicator.classList.add('hidden');
        }, 3000);
    }
}

/**
 * Show loading state for button operations
 */
function showButtonLoadingState(buttonId, show = true, loadingText = 'Loading...') {
    const button = document.getElementById(buttonId);
    if (!button) return;

    const loadingIndicator = button.querySelector('.loading-indicator');
    const buttonText = button.querySelector('.button-text') || button;

    if (show) {
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }

        button.disabled = true;
        button.classList.add('opacity-75', 'cursor-not-allowed');

        if (!button.dataset.originalText) {
            button.dataset.originalText = buttonText.textContent;
        }
        buttonText.textContent = loadingText;

    } else {
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }

        button.disabled = false;
        button.classList.remove('opacity-75', 'cursor-not-allowed');

        if (button.dataset.originalText) {
            buttonText.textContent = button.dataset.originalText;
            delete button.dataset.originalText;
        }
    }
}

/**
 * Enhanced alert function with colored status indicators
 */
function showEnhancedAlert(message, type = 'info', duration = 3000) {
    // Use the existing showAlert if available, otherwise create our own
    if (typeof showAlert === 'function') {
        showAlert(message, type);
    } else {
        FeedbackSystems.showColoredToast(message, type, duration);
    }
}

// Initialize status indicators when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        initializeStatusIndicators();
    }, 500); // Small delay to ensure all elements are loaded
});

// Export functions for use in other modules
if (typeof window !== 'undefined') {
    window.ColorStateManager = ColorStateManager;
    window.ColorTransitions = ColorTransitions;
    window.FeedbackSystems = FeedbackSystems;
    window.AnimationControllers = AnimationControllers;
    window.showButtonLoadingState = showButtonLoadingState;
    window.showEnhancedAlert = showEnhancedAlert;
    window.validateInputField = validateInputField;
}

/**
 * Enhanced Button Interactions
 */
const EnhancedButtons = {
    addHoverEffects: function(element, context = 'blue') {
        if (!element) return;

        element.classList.add('interactive-hover', `interactive-hover-${context}`);

        element.addEventListener('mouseenter', () => {
            ColorStateManager.applyContextualColor(element, context, 'hover');
        });

        element.addEventListener('mouseleave', () => {
            ColorStateManager.applyContextualColor(element, context, 'default');
        });
    },

    // Add pulse effect to element
    addPulseEffect: function(element, color = 'blue') {
        if (!element) return;

        element.classList.add(`pulse-${color}`);

        return () => {
            element.classList.remove(`pulse-${color}`);
        };
    },

    // Add glow effect for focus states
    addGlowEffect: function(element, color = 'blue') {
        if (!element) return;

        element.addEventListener('focus', () => {
            element.classList.add(`focus-ring-${color}`);
        });

        element.addEventListener('blur', () => {
            element.classList.remove(`focus-ring-${color}`);
        });
    },

    // Add bounce animation on click
    addBounceOnClick: function(element) {
        if (!element) return;

        element.addEventListener('click', () => {
            element.style.transform = 'scale(0.95)';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 150);
        });
    },
    // Initialize enhanced button with all effects
    initializeButton: function (button, options = {}) {
        if (!button) return;

        const {
            color = 'blue',
            enableHover = true,
            enableFocus = true,
            enableClick = true,
            enablePulse = false
        } = options;

        // Add base enhanced class
        button.classList.add('btn-enhanced', `btn-${color}-enhanced`);

        if (enableHover) {
            AnimationControllers.addHoverEffects(button, color);
        }

        if (enableFocus) {
            AnimationControllers.addGlowEffect(button, color);
        }

        if (enableClick) {
            this.addBounceOnClick(button);
        }

        if (enablePulse) {
            AnimationControllers.addPulseEffect(button, color);
        }
    },

    // Set button loading state
    setLoadingState: function (button, isLoading = true) {
        if (!button) return;

        if (isLoading) {
            button.classList.add('btn-state-loading');
            button.disabled = true;

            // Add loading indicator
            const loader = FeedbackSystems.createLoadingIndicator(button, 'blue');
            button.dataset.loader = 'true';
        } else {
            button.classList.remove('btn-state-loading');
            button.disabled = false;

            // Remove loading indicator
            const loader = button.querySelector('[aria-label="Loading"]');
            if (loader) {
                FeedbackSystems.removeLoadingIndicator(loader);
            }
            button.removeAttribute('data-loader');
        }
    }
};

/**
 * Enhanced Form Interactions
 */
const EnhancedForms = {
    // Initialize enhanced input field
    initializeInput: function (input, options = {}) {
        if (!input) return;

        const { validateOnBlur = true, showFeedback = true } = options;

        input.classList.add('input-enhanced');

        // Add focus effects
        input.addEventListener('focus', () => {
            ColorStateManager.applyContextualColor(input, 'blue', 'focus');
        });

        input.addEventListener('blur', () => {
            if (validateOnBlur) {
                this.validateInput(input, showFeedback);
            }
        });
    },

    // Validate input and show visual feedback
    validateInput: function (input, showFeedback = true) {
        if (!input) return false;

        const value = input.value.trim();
        const isRequired = input.hasAttribute('required');
        const pattern = input.getAttribute('pattern');
        const type = input.getAttribute('type');

        let isValid = true;
        let message = '';

        // Required validation
        if (isRequired && !value) {
            isValid = false;
            message = 'This field is required';
        }

        // Pattern validation
        if (isValid && pattern && value) {
            const regex = new RegExp(pattern);
            if (!regex.test(value)) {
                isValid = false;
                message = 'Invalid format';
            }
        }

        // Email validation
        if (isValid && type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Invalid email address';
            }
        }

        // Apply visual feedback
        if (showFeedback) {
            input.classList.remove('input-valid', 'input-invalid');

            if (value) {
                input.classList.add(isValid ? 'input-valid' : 'input-invalid');
            }
        }

        return { isValid, message };
    }
};

/**
 * Enhanced Code Block Interactions
 */
const EnhancedCodeBlocks = {
    // Language color mapping for enhanced visual feedback
    languageColors: {
        'javascript': '#f7df1e',
        'python': '#3776ab',
        'html': '#e34f26',
        'css': '#1572b6',
        'java': '#ed8b00',
        'cpp': '#00599c',
        'c': '#00599c',
        'csharp': '#239120',
        'php': '#777bb4',
        'ruby': '#cc342d',
        'go': '#00add8',
        'rust': '#ce422b',
        'typescript': '#3178c6',
        'json': '#38bdf8',
        'xml': '#ff6600',
        'sql': '#336791',
        'bash': '#4eaa25',
        'shell': '#4eaa25',
        'powershell': '#5391fe',
        'default': '#38bdf8'
    },

    // Enhanced language detection from code block class or content
    detectLanguage: function(codeBlock) {
        if (!codeBlock) return 'default';

        const pre = codeBlock.closest('pre');
        if (!pre) return 'default';

        // Check for existing language class on pre or code element
        const classMatch = pre.className.match(/language-(\w+)/) || 
                          codeBlock.className.match(/language-(\w+)/) ||
                          pre.className.match(/lang-(\w+)/) ||
                          codeBlock.className.match(/lang-(\w+)/);
        
        if (classMatch && classMatch[1]) {
            return classMatch[1].toLowerCase();
        }

        // Enhanced content-based detection
        const code = codeBlock.textContent || '';
        const trimmedCode = code.trim();
        const lowerCode = code.toLowerCase();
        
        // Early return for empty code
        if (!trimmedCode) return 'default';

        // JavaScript/TypeScript detection (enhanced)
        if (this.detectJavaScript(code)) {
            // Check for TypeScript specific features
            if (code.includes('interface ') || code.includes('type ') || 
                code.includes(': string') || code.includes(': number') ||
                code.includes('export interface') || code.includes('import type')) {
                return 'typescript';
            }
            return 'javascript';
        }
        
        // Python detection (enhanced)
        if (this.detectPython(code)) {
            return 'python';
        }
        
        // HTML detection (enhanced)
        if (this.detectHTML(code)) {
            return 'html';
        }
        
        // CSS detection (enhanced)
        if (this.detectCSS(code)) {
            return 'css';
        }
        
        // JSON detection (enhanced)
        if (this.detectJSON(code)) {
            return 'json';
        }
        
        // XML detection
        if (this.detectXML(code)) {
            return 'xml';
        }
        
        // SQL detection (enhanced)
        if (this.detectSQL(code)) {
            return 'sql';
        }
        
        // Shell/Bash detection (enhanced)
        if (this.detectShell(code)) {
            return 'bash';
        }
        
        // PowerShell detection
        if (this.detectPowerShell(code)) {
            return 'powershell';
        }
        
        // Java detection
        if (this.detectJava(code)) {
            return 'java';
        }
        
        // C/C++ detection
        if (this.detectCPlusPlus(code)) {
            return 'cpp';
        }
        
        // C# detection
        if (this.detectCSharp(code)) {
            return 'csharp';
        }
        
        // PHP detection
        if (this.detectPHP(code)) {
            return 'php';
        }
        
        // Ruby detection
        if (this.detectRuby(code)) {
            return 'ruby';
        }
        
        // Go detection
        if (this.detectGo(code)) {
            return 'go';
        }
        
        // Rust detection
        if (this.detectRust(code)) {
            return 'rust';
        }

        return 'default';
    },

    // Enhanced language detection helper methods
    detectJavaScript: function(code) {
        const jsPatterns = [
            /\b(function|const|let|var|=>|console\.log|document\.|window\.)\b/,
            /\b(async|await|Promise|setTimeout|setInterval)\b/,
            /\b(import|export|require|module\.exports)\b/,
            /\b(class|extends|constructor|super)\b/,
            /\b(if|else|for|while|switch|case|break|continue)\b.*{/,
            /\$\{.*\}/,  // Template literals
            /\/\*[\s\S]*?\*\/|\/\/.*$/m  // JS comments
        ];
        return jsPatterns.some(pattern => pattern.test(code));
    },

    detectPython: function(code) {
        const pythonPatterns = [
            /\b(def|class|import|from|if __name__|print\(|input\()\b/,
            /\b(elif|except|finally|with|as|lambda|yield)\b/,
            /\b(True|False|None|self|cls)\b/,
            /#.*$/m,  // Python comments
            /:\s*$/m,  // Colon at end of line (common in Python)
            /\brange\(|len\(|str\(|int\(|float\(/
        ];
        return pythonPatterns.some(pattern => pattern.test(code));
    },

    detectHTML: function(code) {
        const htmlPatterns = [
            /<\/?[a-z][\s\S]*>/i,  // HTML tags
            /<!DOCTYPE|<html|<head|<body|<div|<span|<p|<a|<img/i,
            /class=|id=|href=|src=/i,
            /<!--[\s\S]*?-->/  // HTML comments
        ];
        return htmlPatterns.some(pattern => pattern.test(code));
    },

    detectCSS: function(code) {
        const cssPatterns = [
            /[.#]?[a-zA-Z-_][a-zA-Z0-9-_]*\s*\{[\s\S]*?\}/,  // CSS rules
            /\b(color|background|margin|padding|border|font|width|height):/,
            /@(media|import|keyframes|font-face)/,
            /\/\*[\s\S]*?\*\//,  // CSS comments
            /:\s*(#[0-9a-fA-F]{3,6}|rgba?\(|hsla?\()/  // Color values
        ];
        return cssPatterns.some(pattern => pattern.test(code));
    },

    detectJSON: function(code) {
        const trimmed = code.trim();
        if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
            (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
            try {
                JSON.parse(trimmed);
                return true;
            } catch (e) {
                return false;
            }
        }
        return false;
    },

    detectXML: function(code) {
        const xmlPatterns = [
            /<\?xml/i,
            /<[a-zA-Z][a-zA-Z0-9-_]*[^>]*\/>/,  // Self-closing tags
            /<[a-zA-Z][a-zA-Z0-9-_]*[^>]*>[\s\S]*<\/[a-zA-Z][a-zA-Z0-9-_]*>/  // Opening/closing tags
        ];
        return xmlPatterns.some(pattern => pattern.test(code));
    },

    detectSQL: function(code) {
        const sqlPatterns = [
            /\b(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|FROM|WHERE|JOIN|GROUP BY|ORDER BY)\b/i,
            /\b(TABLE|DATABASE|INDEX|VIEW|PROCEDURE|FUNCTION)\b/i,
            /\b(INT|VARCHAR|TEXT|DATE|TIMESTAMP|BOOLEAN)\b/i,
            /--.*$/m  // SQL comments
        ];
        return sqlPatterns.some(pattern => pattern.test(code));
    },

    detectShell: function(code) {
        const shellPatterns = [
            /^#!/,  // Shebang
            /\b(echo|ls|cd|mkdir|rm|cp|mv|grep|awk|sed|cat|chmod)\b/,
            /\$[A-Z_][A-Z0-9_]*/,  // Environment variables
            /&&|\|\||;/,  // Shell operators
            /#.*$/m  // Shell comments
        ];
        return shellPatterns.some(pattern => pattern.test(code));
    },

    detectPowerShell: function(code) {
        const psPatterns = [
            /\b(Get-|Set-|New-|Remove-|Write-Host|Write-Output)\b/,
            /\$[a-zA-Z_][a-zA-Z0-9_]*/,  // PowerShell variables
            /\[.*\]/,  // Type casting
            /#.*$/m  // PowerShell comments
        ];
        return psPatterns.some(pattern => pattern.test(code));
    },

    detectJava: function(code) {
        const javaPatterns = [
            /\b(public|private|protected|static|final|class|interface|extends|implements)\b/,
            /\b(String|int|boolean|void|ArrayList|HashMap)\b/,
            /System\.out\.println/,
            /\/\*[\s\S]*?\*\/|\/\/.*$/m  // Java comments
        ];
        return javaPatterns.some(pattern => pattern.test(code));
    },

    detectCPlusPlus: function(code) {
        const cppPatterns = [
            /#include\s*<.*>/,
            /\b(std::|cout|cin|endl|vector|string)\b/,
            /\b(int|char|float|double|bool|void)\s+\w+\s*\(/,
            /\/\*[\s\S]*?\*\/|\/\/.*$/m  // C++ comments
        ];
        return cppPatterns.some(pattern => pattern.test(code));
    },

    detectCSharp: function(code) {
        const csharpPatterns = [
            /\b(using|namespace|class|interface|public|private|static|void)\b/,
            /\b(string|int|bool|var|Console\.WriteLine)\b/,
            /\/\*[\s\S]*?\*\/|\/\/.*$/m  // C# comments
        ];
        return csharpPatterns.some(pattern => pattern.test(code));
    },

    detectPHP: function(code) {
        const phpPatterns = [
            /<\?php/,
            /\$[a-zA-Z_][a-zA-Z0-9_]*/,  // PHP variables
            /\b(echo|print|function|class|extends|public|private)\b/,
            /\/\*[\s\S]*?\*\/|\/\/.*$/m  // PHP comments
        ];
        return phpPatterns.some(pattern => pattern.test(code));
    },

    detectRuby: function(code) {
        const rubyPatterns = [
            /\b(def|class|module|end|puts|print|require)\b/,
            /\b(if|elsif|else|unless|case|when)\b/,
            /#.*$/m,  // Ruby comments
            /@[a-zA-Z_][a-zA-Z0-9_]*/  // Instance variables
        ];
        return rubyPatterns.some(pattern => pattern.test(code));
    },

    detectGo: function(code) {
        const goPatterns = [
            /\b(package|import|func|var|const|type|struct|interface)\b/,
            /\b(fmt\.Print|fmt\.Println|make|append|len|cap)\b/,
            /\/\*[\s\S]*?\*\/|\/\/.*$/m  // Go comments
        ];
        return goPatterns.some(pattern => pattern.test(code));
    },

    detectRust: function(code) {
        const rustPatterns = [
            /\b(fn|let|mut|struct|enum|impl|trait|use|mod)\b/,
            /\b(println!|print!|vec!|String|Vec|Option|Result)\b/,
            /\/\*[\s\S]*?\*\/|\/\/.*$/m  // Rust comments
        ];
        return rustPatterns.some(pattern => pattern.test(code));
    },

    // Apply language-specific colored borders and styling
    applyLanguageColors: function(pre, language) {
        if (!pre || !language) return;

        // Remove existing language classes
        pre.classList.forEach(className => {
            if (className.startsWith('lang-')) {
                pre.classList.remove(className);
            }
        });

        // Add new language class
        pre.classList.add(`lang-${language}`);
    },

    // Create enhanced language badge
    createLanguageBadge: function(language) {
        const badge = document.createElement('div');
        badge.className = `code-language-badge lang-${language}`;
        badge.textContent = language.toUpperCase();
        badge.title = `Language: ${language}`;
        return badge;
    },

    // Create enhanced copy button with colored states
    createEnhancedCopyButton: function(language) {
        const copyBtn = document.createElement('button');
        copyBtn.className = 'code-copy-btn-enhanced';
        copyBtn.innerHTML = '<i class="fas fa-copy"></i><span>Copy</span>';
        copyBtn.title = 'Copy code to clipboard';
        copyBtn.setAttribute('data-language', language);
        return copyBtn;
    },

    // Initialize enhanced code block with colored copy button and language detection
    initializeCodeBlock: function (codeBlock) {
        if (!codeBlock) return;

        const pre = codeBlock.closest('pre');
        if (!pre) return;

        // Skip if already enhanced
        if (pre.classList.contains('code-block-enhanced')) return;

        // Detect language with enhanced logic
        const language = this.detectLanguage(codeBlock);

        // Create container wrapper if needed
        let container = pre.parentElement;
        if (!container.classList.contains('code-block-enhanced')) {
            container = document.createElement('div');
            container.className = 'code-block-enhanced';
            pre.parentNode.insertBefore(container, pre);
            container.appendChild(pre);
        }

        // Mark as enhanced to prevent re-processing
        pre.classList.add('code-block-enhanced');

        // Apply language-specific styling with enhanced colors
        this.applyLanguageColors(container, language);

        // Create header for badges and buttons
        let header = container.querySelector('.code-block-header');
        if (!header) {
            header = document.createElement('div');
            header.className = 'code-block-header';
            container.appendChild(header);
        }

        // Create enhanced language badge with better styling
        if (!header.querySelector('.code-language-badge')) {
            const badge = this.createLanguageBadge(language);
            badge.setAttribute('data-language', language);
            badge.setAttribute('aria-label', `Code language: ${language}`);
            header.appendChild(badge);
        }

        // Create enhanced copy button with improved accessibility
        if (!header.querySelector('.code-copy-btn-enhanced')) {
            const copyBtn = this.createEnhancedCopyButton(language);
            copyBtn.setAttribute('tabindex', '0');
            copyBtn.setAttribute('role', 'button');
            copyBtn.setAttribute('aria-label', `Copy ${language} code to clipboard`);
            
            copyBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.copyCodeWithColoredFeedback(copyBtn, codeBlock.textContent, language);
            });

            header.appendChild(copyBtn);
        }

        // Add enhanced hover effect handlers with language-specific colors
        this.addHoverEffects(container, language);

        // Add language detection indicator for debugging (optional)
        if (!container.querySelector('.code-language-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'code-language-indicator';
            indicator.textContent = `Detected: ${language}`;
            indicator.setAttribute('aria-hidden', 'true');
            container.appendChild(indicator);
        }

        // Store language data for future reference
        container.setAttribute('data-language', language);
        codeBlock.setAttribute('data-language', language);
    },

    // Add hover effects for code blocks with colored visual feedback
    addHoverEffects: function(container, language) {
        if (!container) return;

        const copyBtn = container.querySelector('.code-copy-btn-enhanced');
        const badge = container.querySelector('.code-language-badge');
        const languageColor = this.languageColors[language] || this.languageColors.default;

        // Enhanced container hover effects with language-specific colors
        container.addEventListener('mouseenter', () => {
            // Add subtle glow effect based on language color
            container.style.boxShadow = `0 0 20px ${languageColor}30, 0 4px 12px rgba(0, 0, 0, 0.15)`;
            container.style.borderColor = `${languageColor}50`;
            container.style.transform = 'translateY(-2px)';
            
            // Enhance language badge visibility on hover
            if (badge) {
                badge.style.opacity = '1';
                badge.style.transform = 'scale(1.05)';
                badge.style.boxShadow = `0 2px 8px ${languageColor}40`;
            }
        });

        container.addEventListener('mouseleave', () => {
            // Remove hover effects
            container.style.boxShadow = '';
            container.style.borderColor = '';
            container.style.transform = '';
            
            if (badge) {
                badge.style.opacity = '';
                badge.style.transform = '';
                badge.style.boxShadow = '';
            }
        });

        // Enhanced copy button hover effects with language-specific colors
        if (copyBtn) {
            copyBtn.addEventListener('mouseenter', () => {
                copyBtn.style.transform = 'scale(1.05)';
                copyBtn.style.borderColor = `${languageColor}60`;
                copyBtn.style.backgroundColor = `${languageColor}20`;
                copyBtn.style.color = languageColor;
                copyBtn.style.boxShadow = `0 2px 8px ${languageColor}30`;
            });

            copyBtn.addEventListener('mouseleave', () => {
                // Only reset if not in copied or error state
                if (!copyBtn.classList.contains('copied') && !copyBtn.classList.contains('error')) {
                    copyBtn.style.transform = 'scale(1)';
                    copyBtn.style.borderColor = '';
                    copyBtn.style.backgroundColor = '';
                    copyBtn.style.color = '';
                    copyBtn.style.boxShadow = '';
                }
            });

            // Enhanced click feedback
            copyBtn.addEventListener('mousedown', () => {
                copyBtn.style.transform = 'scale(0.95)';
            });

            copyBtn.addEventListener('mouseup', () => {
                copyBtn.style.transform = 'scale(1.05)';
            });
        }

        // Enhanced language badge hover effects
        if (badge) {
            badge.addEventListener('mouseenter', () => {
                badge.style.transform = 'scale(1.1)';
                badge.style.opacity = '1';
                badge.style.fontWeight = '700';
                badge.style.letterSpacing = '0.05em';
            });

            badge.addEventListener('mouseleave', () => {
                badge.style.transform = 'scale(1.05)';
                badge.style.fontWeight = '';
                badge.style.letterSpacing = '';
            });
        }

        // Add keyboard accessibility for copy button
        if (copyBtn) {
            copyBtn.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    copyBtn.click();
                }
            });

            copyBtn.addEventListener('focus', () => {
                copyBtn.style.outline = `2px solid ${languageColor}`;
                copyBtn.style.outlineOffset = '2px';
            });

            copyBtn.addEventListener('blur', () => {
                copyBtn.style.outline = '';
                copyBtn.style.outlineOffset = '';
            });
        }
    },

    // Copy code with enhanced colored visual feedback
    copyCodeWithColoredFeedback: function (button, code, language) {
        if (!button || !code) return;

        // Add copying animation with language-specific color
        button.classList.add('copying');
        
        // Apply language-specific color during copying
        const languageColor = this.languageColors[language] || this.languageColors.default;
        button.style.borderColor = `${languageColor}50`;
        button.style.backgroundColor = `${languageColor}20`;

        navigator.clipboard.writeText(code).then(() => {
            // Success feedback with language-specific colors
            button.classList.remove('copying');
            button.classList.add('copied');
            
            // Update button content with success styling
            const icon = button.querySelector('i');
            const span = button.querySelector('span');
            if (icon) {
                icon.classList.replace('fa-copy', 'fa-check');
                icon.style.color = 'var(--color-success)';
            }
            if (span) {
                span.textContent = 'Copied!';
                span.style.color = 'var(--color-success)';
            }

            // Apply success color styling
            button.style.borderColor = 'var(--color-success)';
            button.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';

            // Show colored toast notification with language info
            if (typeof FeedbackSystems !== 'undefined' && FeedbackSystems.showColoredToast) {
                FeedbackSystems.showColoredToast(`${language.toUpperCase()} code copied to clipboard!`, 'success');
            } else if (typeof showAlert === 'function') {
                showAlert(`${language.toUpperCase()} code copied to clipboard!`, 'success');
            }

            // Add success pulse animation
            button.style.animation = 'copy-success 0.3s ease-in-out';

            // Reset button after delay
            setTimeout(() => {
                button.classList.remove('copied');
                if (icon) {
                    icon.classList.replace('fa-check', 'fa-copy');
                    icon.style.color = '';
                }
                if (span) {
                    span.textContent = 'Copy';
                    span.style.color = '';
                }
                // Reset to default styling
                button.style.borderColor = '';
                button.style.backgroundColor = '';
                button.style.animation = '';
            }, 2000);

        }).catch(err => {
            console.error('Copy failed:', err);
            
            // Error feedback with red coloring
            button.classList.remove('copying');
            button.classList.add('error');

            // Update button content with error styling
            const icon = button.querySelector('i');
            const span = button.querySelector('span');
            if (icon) {
                icon.classList.replace('fa-copy', 'fa-exclamation-triangle');
                icon.style.color = 'var(--color-error)';
            }
            if (span) {
                span.textContent = 'Error!';
                span.style.color = 'var(--color-error)';
            }

            // Apply error color styling
            button.style.borderColor = 'var(--color-error)';
            button.style.backgroundColor = 'rgba(239, 68, 68, 0.2)';

            // Show error toast with language info
            if (typeof FeedbackSystems !== 'undefined' && FeedbackSystems.showColoredToast) {
                FeedbackSystems.showColoredToast(`Failed to copy ${language.toUpperCase()} code`, 'error');
            } else if (typeof showAlert === 'function') {
                showAlert(`Failed to copy ${language.toUpperCase()} code`, 'error');
            }

            // Add error shake animation
            button.style.animation = 'shake-red 0.5s ease-in-out';

            // Reset button after delay
            setTimeout(() => {
                button.classList.remove('error');
                if (icon) {
                    icon.classList.replace('fa-exclamation-triangle', 'fa-copy');
                    icon.style.color = '';
                }
                if (span) {
                    span.textContent = 'Copy';
                    span.style.color = '';
                }
                // Reset to default styling
                button.style.borderColor = '';
                button.style.backgroundColor = '';
                button.style.animation = '';
            }, 2000);
        });
    },

    // Initialize all code blocks on the page
    initializeAllCodeBlocks: function() {
        const codeBlocks = document.querySelectorAll('pre code');
        codeBlocks.forEach(codeBlock => {
            this.initializeCodeBlock(codeBlock);
        });
    },

    // Re-initialize code blocks (for dynamically added content)
    reinitializeCodeBlocks: function() {
        // Remove existing enhancements first
        document.querySelectorAll('.code-block-enhanced').forEach(container => {
            const pre = container.querySelector('pre');
            if (pre) {
                // Move pre back to original position
                container.parentNode.insertBefore(pre, container);
                container.remove();
            }
        });

        // Re-initialize all code blocks
        this.initializeAllCodeBlocks();
    }
};

/**
 * Initialize Enhanced UI Interactions
 */
function initializeEnhancedInteractions() {
    // Initialize enhanced code blocks
    EnhancedCodeBlocks.initializeAllCodeBlocks();
    
    // Initialize enhanced buttons
    document.querySelectorAll('button:not(.code-copy-btn)').forEach(button => {
        // Skip if already enhanced
        if (button.classList.contains('btn-enhanced')) return;

        // Determine color based on context
        let color = 'blue';
        if (button.classList.contains('btn-success') || button.textContent.toLowerCase().includes('save')) {
            color = 'green';
        } else if (button.classList.contains('btn-warning') || button.textContent.toLowerCase().includes('warning')) {
            color = 'orange';
        } else if (button.classList.contains('btn-danger') || button.textContent.toLowerCase().includes('delete')) {
            color = 'red';
        } else if (button.closest('.settings-ui')) {
            color = 'purple';
        }

        EnhancedButtons.initializeButton(button, { color });
    });

    // Initialize enhanced inputs
    document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], textarea').forEach(input => {
        EnhancedForms.initializeInput(input);
    });

    // Initialize enhanced code blocks
    document.querySelectorAll('pre code').forEach(codeBlock => {
        EnhancedCodeBlocks.initializeCodeBlock(codeBlock);
    });

    // Initialize enhanced cards/panels
    document.querySelectorAll('.card, .panel, .message').forEach(card => {
        if (!card.classList.contains('card-enhanced')) {
            card.classList.add('card-enhanced');

            // Determine color based on context
            if (card.classList.contains('message-user')) {
                card.classList.add('card-enhanced-blue');
            } else if (card.classList.contains('message-assistant')) {
                card.classList.add('card-enhanced-purple');
            } else if (card.classList.contains('message-system')) {
                card.classList.add('card-enhanced-green');
            }
        }
    });
}

// Initialize enhanced interactions when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeEnhancedInteractions);
} else {
    initializeEnhancedInteractions();
}

// Re-initialize for dynamically added content
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                // Initialize new buttons
                if (node.tagName === 'BUTTON' && !node.classList.contains('btn-enhanced')) {
                    EnhancedButtons.initializeButton(node);
                }

                // Initialize new inputs
                if ((node.tagName === 'INPUT' || node.tagName === 'TEXTAREA') && !node.classList.contains('input-enhanced')) {
                    EnhancedForms.initializeInput(node);
                }

                // Initialize new code blocks
                if (node.tagName === 'PRE' || node.querySelector('pre code')) {
                    const codeBlocks = node.tagName === 'PRE' ? [node.querySelector('code')] : node.querySelectorAll('pre code');
                    codeBlocks.forEach(codeBlock => {
                        if (codeBlock) EnhancedCodeBlocks.initializeCodeBlock(codeBlock);
                    });
                }

                // Initialize new cards
                if (node.classList && (node.classList.contains('card') || node.classList.contains('panel') || node.classList.contains('message')) && !node.classList.contains('card-enhanced')) {
                    node.classList.add('card-enhanced');
                }
            }
        });
    });
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true
});

// Export functions for global use
window.ColorStateManager = ColorStateManager;
window.ColorTransitions = ColorTransitions;
window.FeedbackSystems = FeedbackSystems;
window.AnimationControllers = AnimationControllers;
window.EnhancedButtons = EnhancedButtons;
window.EnhancedForms = EnhancedForms;
window.EnhancedCodeBlocks = EnhancedCodeBlocks;

// ========================================
// COMPREHENSIVE ANIMATION CONTROLLERS
// ========================================

/**
 * Enhanced Animation Trigger Functions
 */
const AnimationTriggers = {
    // Trigger enhanced hover animations with color changes
    triggerHoverAnimation: function(element, context = 'blue', options = {}) {
        if (!element) return;

        const {
            scale = 1.05,
            translateY = -2,
            duration = 300,
            enableGlow = true,
            enableColorShift = true
        } = options;

        // Add hover animation classes
        element.classList.add('color-transition-fast', 'interactive-element');

        const handleMouseEnter = () => {
            // Apply transform
            element.style.transform = `scale(${scale}) translateY(${translateY}px)`;
            element.style.transition = `all ${duration}ms var(--animation-ease-out)`;

            // Apply color changes
            if (enableColorShift) {
                ColorStateManager.applyContextualColor(element, context, 'hover');
            }

            // Apply glow effect
            if (enableGlow) {
                element.classList.add(`hover-glow-${context}`);
            }
        };

        const handleMouseLeave = () => {
            // Reset transform
            element.style.transform = 'scale(1) translateY(0)';

            // Reset color
            if (enableColorShift) {
                ColorStateManager.applyContextualColor(element, context, 'default');
            }

            // Remove glow effect
            if (enableGlow) {
                element.classList.remove(`hover-glow-${context}`);
            }
        };

        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);

        // Return cleanup function
        return () => {
            element.removeEventListener('mouseenter', handleMouseEnter);
            element.removeEventListener('mouseleave', handleMouseLeave);
        };
    },

    // Trigger click animation with bounce effect
    triggerClickAnimation: function(element, options = {}) {
        if (!element) return;

        const {
            scaleDown = 0.95,
            scaleUp = 1.05,
            duration = 150,
            enableColorFeedback = true,
            context = 'blue'
        } = options;

        const handleClick = () => {
            // Scale down
            element.style.transform = `scale(${scaleDown})`;
            element.style.transition = `transform ${duration}ms var(--animation-ease-out)`;

            // Apply active color
            if (enableColorFeedback) {
                ColorStateManager.applyContextualColor(element, context, 'active');
            }

            // Scale up and reset
            setTimeout(() => {
                element.style.transform = `scale(${scaleUp})`;
                
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    
                    // Reset color
                    if (enableColorFeedback) {
                        ColorStateManager.applyContextualColor(element, context, 'default');
                    }
                }, duration);
            }, duration);
        };

        element.addEventListener('click', handleClick);

        // Return cleanup function
        return () => {
            element.removeEventListener('click', handleClick);
        };
    },

    // Trigger loading animation with pulse effect
    triggerLoadingAnimation: function(element, context = 'blue', options = {}) {
        if (!element) return;

        const {
            showText = true,
            loadingText = 'Loading...',
            enablePulse = true,
            enableColorShift = true
        } = options;

        // Store original content
        const originalContent = element.innerHTML;
        const originalDisabled = element.disabled;

        // Apply loading state
        element.disabled = true;
        element.classList.add('btn-state-loading');

        if (enableColorShift) {
            ColorStateManager.applyContextualColor(element, context, 'loading');
        }

        if (enablePulse) {
            element.classList.add(`pulse-${context}`);
        }

        if (showText) {
            element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${loadingText}`;
        }

        // Return function to stop loading
        return () => {
            element.disabled = originalDisabled;
            element.classList.remove('btn-state-loading');
            element.innerHTML = originalContent;

            if (enableColorShift) {
                ColorStateManager.applyContextualColor(element, context, 'default');
            }

            if (enablePulse) {
                element.classList.remove(`pulse-${context}`);
            }
        };
    }
};

/**
 * Pulse and Glow Effect Functions
 */
const PulseGlowEffects = {
    // Add pulse effect to active elements
    addPulseEffect: function(element, color = 'blue', options = {}) {
        if (!element) return;

        const {
            duration = 2000,
            intensity = 'normal', // 'subtle', 'normal', 'strong'
            continuous = true
        } = options;

        // Apply pulse class based on intensity
        const pulseClass = intensity === 'subtle' ? `pulse-${color}-subtle` : 
                          intensity === 'strong' ? `pulse-${color}-strong` : 
                          `pulse-${color}`;

        element.classList.add(pulseClass);

        // If not continuous, remove after duration
        if (!continuous) {
            setTimeout(() => {
                element.classList.remove(pulseClass);
            }, duration);
        }

        // Return cleanup function
        return () => {
            element.classList.remove(pulseClass);
        };
    },

    // Add glow effect for focus states
    addGlowEffect: function(element, color = 'blue', options = {}) {
        if (!element) return;

        const {
            intensity = 'normal', // 'subtle', 'normal', 'strong'
            trigger = 'focus', // 'focus', 'hover', 'active', 'always'
            duration = 300
        } = options;

        const glowClass = `glow-active-${color}`;
        
        if (trigger === 'always') {
            element.classList.add(glowClass);
        } else {
            const addGlow = () => {
                element.classList.add(glowClass);
                element.style.transition = `box-shadow ${duration}ms var(--animation-ease-out)`;
            };

            const removeGlow = () => {
                element.classList.remove(glowClass);
            };

            if (trigger === 'focus') {
                element.addEventListener('focus', addGlow);
                element.addEventListener('blur', removeGlow);
            } else if (trigger === 'hover') {
                element.addEventListener('mouseenter', addGlow);
                element.addEventListener('mouseleave', removeGlow);
            } else if (trigger === 'active') {
                element.addEventListener('mousedown', addGlow);
                element.addEventListener('mouseup', removeGlow);
            }
        }

        // Return cleanup function
        return () => {
            element.classList.remove(glowClass);
        };
    },

    // Create breathing animation for loading states
    addBreathingEffect: function(element, options = {}) {
        if (!element) return;

        const {
            duration = 2000,
            minOpacity = 0.7,
            maxScale = 1.05
        } = options;

        element.classList.add('loading-breathe');
        element.style.animationDuration = `${duration}ms`;

        // Return cleanup function
        return () => {
            element.classList.remove('loading-breathe');
            element.style.animationDuration = '';
        };
    }
};

/**
 * Smooth Color Transition Effects
 */
const SmoothColorTransitions = {
    // Create smooth color transition for focus states
    addFocusTransition: function(element, context = 'blue', options = {}) {
        if (!element) return;

        const {
            duration = 300,
            enableRing = true,
            enableColorShift = true,
            enableGlow = false
        } = options;

        element.classList.add('color-transition', 'focus-ring-animated');

        const handleFocus = () => {
            if (enableColorShift) {
                ColorStateManager.applyContextualColor(element, context, 'focus');
            }

            if (enableRing) {
                element.classList.add(`focus-ring-${context}`);
            }

            if (enableGlow) {
                element.classList.add(`glow-active-${context}`);
            }
        };

        const handleBlur = () => {
            if (enableColorShift) {
                ColorStateManager.applyContextualColor(element, context, 'default');
            }

            if (enableRing) {
                element.classList.remove(`focus-ring-${context}`);
            }

            if (enableGlow) {
                element.classList.remove(`glow-active-${context}`);
            }
        };

        element.addEventListener('focus', handleFocus);
        element.addEventListener('blur', handleBlur);

        // Return cleanup function
        return () => {
            element.removeEventListener('focus', handleFocus);
            element.removeEventListener('blur', handleBlur);
        };
    },

    // Create smooth state transition
    transitionToState: function(element, fromState, toState, context = 'blue', duration = 300) {
        if (!element) return;

        // Add transition class
        element.classList.add('color-transition');

        // Apply from state
        ColorStateManager.applyContextualColor(element, context, fromState);

        // Transition to new state
        setTimeout(() => {
            ColorStateManager.applyContextualColor(element, context, toState);
        }, 50);

        // Clean up transition class
        setTimeout(() => {
            element.classList.remove('color-transition');
        }, duration);
    },

    // Create morphing color animation
    morphColors: function(element, colors = [], duration = 2000) {
        if (!element || colors.length < 2) return;

        let currentIndex = 0;
        const interval = duration / colors.length;

        const morph = () => {
            const currentColor = colors[currentIndex];
            const nextColor = colors[(currentIndex + 1) % colors.length];

            // Apply current color
            element.style.color = currentColor;
            element.style.transition = `color ${interval}ms var(--animation-ease-in-out)`;

            currentIndex = (currentIndex + 1) % colors.length;
        };

        // Start morphing
        const morphInterval = setInterval(morph, interval);
        morph(); // Initial call

        // Return cleanup function
        return () => {
            clearInterval(morphInterval);
            element.style.color = '';
            element.style.transition = '';
        };
    }
};

/**
 * Theme Switching Animation Functions
 */
const ThemeSwitchingAnimations = {
    // Animate theme switch with smooth color transitions
    animateThemeSwitch: function(options = {}) {
        const {
            duration = 500,
            enableColorMorph = true,
            enableFadeEffect = true
        } = options;

        const body = document.body;
        const allElements = document.querySelectorAll('*');

        // Add theme transition classes
        allElements.forEach(element => {
            element.classList.add('theme-transition');
        });

        if (enableColorMorph) {
            body.classList.add('color-morph');
        }

        if (enableFadeEffect) {
            body.classList.add('theme-switching');
        }

        // Clean up after animation
        setTimeout(() => {
            allElements.forEach(element => {
                element.classList.remove('theme-transition');
            });

            if (enableColorMorph) {
                body.classList.remove('color-morph');
            }

            if (enableFadeEffect) {
                body.classList.remove('theme-switching');
            }
        }, duration);
    },

    // Animate individual component theme change
    animateComponentThemeChange: function(element, options = {}) {
        if (!element) return;

        const {
            duration = 300,
            enableScale = true,
            enableColorShift = true
        } = options;

        element.classList.add('theme-transition');

        if (enableScale) {
            element.style.transform = 'scale(0.98)';
            element.style.transition = `all ${duration}ms var(--animation-ease-in-out)`;

            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, duration / 2);
        }

        // Clean up
        setTimeout(() => {
            element.classList.remove('theme-transition');
            element.style.transform = '';
            element.style.transition = '';
        }, duration);
    },

    // Create ripple effect for theme switch button
    createThemeSwitchRipple: function(button, event) {
        if (!button || !event) return;

        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 600ms ease-out;
            pointer-events: none;
            z-index: 1000;
        `;

        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);

        // Remove ripple after animation
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }
};

/**
 * Message Animation Controllers
 */
const MessageAnimations = {
    // Animate new message appearance
    animateNewMessage: function(messageElement, role = 'user', options = {}) {
        if (!messageElement) return;

        const {
            animationType = 'fadeIn', // 'fadeIn', 'slideIn', 'bounce'
            enableColorShift = true,
            delay = 0
        } = options;

        // Determine color based on role
        const colorMap = {
            'user': 'blue',
            'assistant': 'purple',
            'system': 'green'
        };

        const color = colorMap[role] || 'blue';

        // Add initial state
        messageElement.style.opacity = '0';
        messageElement.style.transform = 'translateY(20px)';

        setTimeout(() => {
            // Add animation classes
            if (animationType === 'fadeIn') {
                messageElement.classList.add(`fade-in-${color}`);
            } else if (animationType === 'slideIn') {
                messageElement.classList.add(role === 'user' ? 'slide-in-right' : 'slide-in-left');
            } else if (animationType === 'bounce') {
                messageElement.classList.add('animate-bounce');
            }

            // Apply color coding
            if (enableColorShift) {
                messageElement.classList.add(`border-l-4`, `border-${color}-primary`, `bg-${color}-tint`);
            }

            // Reset styles
            messageElement.style.opacity = '';
            messageElement.style.transform = '';
        }, delay);
    },

    // Animate typing indicator
    animateTypingIndicator: function(container) {
        if (!container) return;

        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator p-4';
        typingIndicator.innerHTML = `
            <div class="flex items-center space-x-1">
                <span class="text-purple-primary">Assistant is typing</span>
                <div class="flex space-x-1 ml-2">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;

        container.appendChild(typingIndicator);

        // Return cleanup function
        return () => {
            if (typingIndicator.parentNode) {
                typingIndicator.parentNode.removeChild(typingIndicator);
            }
        };
    },

    // Animate message streaming
    animateMessageStreaming: function(messageElement, options = {}) {
        if (!messageElement) return;

        const {
            enablePulse = true,
            enableGlow = true,
            color = 'purple'
        } = options;

        if (enablePulse) {
            messageElement.classList.add(`pulse-${color}`);
        }

        if (enableGlow) {
            messageElement.classList.add(`glow-active-${color}`);
        }

        // Return cleanup function
        return () => {
            if (enablePulse) {
                messageElement.classList.remove(`pulse-${color}`);
            }

            if (enableGlow) {
                messageElement.classList.remove(`glow-active-${color}`);
            }
        };
    }
};

/**
 * Settings Panel Animation Controllers
 */
const SettingsAnimations = {
    // Animate settings panel opening
    animateSettingsOpen: function(panel, options = {}) {
        if (!panel) return;

        const {
            animationType = 'slideIn', // 'slideIn', 'fadeIn', 'scale'
            duration = 300,
            enableBackdrop = true
        } = options;

        // Add backdrop if enabled
        if (enableBackdrop) {
            const backdrop = document.createElement('div');
            backdrop.className = 'fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300';
            backdrop.style.opacity = '0';
            document.body.appendChild(backdrop);

            setTimeout(() => {
                backdrop.style.opacity = '1';
            }, 10);

            panel.dataset.backdrop = 'true';
        }

        // Apply animation based on type
        if (animationType === 'slideIn') {
            panel.style.transform = 'translateX(100%)';
            panel.style.transition = `transform ${duration}ms var(--animation-ease-out)`;
            
            setTimeout(() => {
                panel.style.transform = 'translateX(0)';
            }, 10);
        } else if (animationType === 'fadeIn') {
            panel.style.opacity = '0';
            panel.style.transition = `opacity ${duration}ms var(--animation-ease-out)`;
            
            setTimeout(() => {
                panel.style.opacity = '1';
            }, 10);
        } else if (animationType === 'scale') {
            panel.style.transform = 'scale(0.9)';
            panel.style.opacity = '0';
            panel.style.transition = `all ${duration}ms var(--animation-ease-out)`;
            
            setTimeout(() => {
                panel.style.transform = 'scale(1)';
                panel.style.opacity = '1';
            }, 10);
        }
    },

    // Animate settings panel closing
    animateSettingsClose: function(panel, options = {}) {
        if (!panel) return;

        const {
            animationType = 'slideOut',
            duration = 300,
            removeBackdrop = true
        } = options;

        // Remove backdrop if it exists
        if (removeBackdrop && panel.dataset.backdrop) {
            const backdrop = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
            if (backdrop) {
                backdrop.style.opacity = '0';
                setTimeout(() => {
                    if (backdrop.parentNode) {
                        backdrop.parentNode.removeChild(backdrop);
                    }
                }, duration);
            }
        }

        // Apply closing animation
        if (animationType === 'slideOut') {
            panel.style.transform = 'translateX(100%)';
        } else if (animationType === 'fadeOut') {
            panel.style.opacity = '0';
        } else if (animationType === 'scale') {
            panel.style.transform = 'scale(0.9)';
            panel.style.opacity = '0';
        }

        // Hide panel after animation
        setTimeout(() => {
            panel.style.display = 'none';
            panel.style.transform = '';
            panel.style.opacity = '';
            panel.style.transition = '';
            delete panel.dataset.backdrop;
        }, duration);
    },

    // Animate settings section transitions
    animateSettingsSection: function(fromSection, toSection, options = {}) {
        if (!fromSection || !toSection) return;

        const {
            duration = 300,
            direction = 'horizontal' // 'horizontal', 'vertical', 'fade'
        } = options;

        if (direction === 'horizontal') {
            // Slide out current section
            fromSection.style.transform = 'translateX(-100%)';
            fromSection.style.transition = `transform ${duration}ms var(--animation-ease-in-out)`;

            // Slide in new section
            toSection.style.transform = 'translateX(100%)';
            toSection.style.display = 'block';
            toSection.style.transition = `transform ${duration}ms var(--animation-ease-in-out)`;

            setTimeout(() => {
                toSection.style.transform = 'translateX(0)';
            }, 10);

            // Clean up after animation
            setTimeout(() => {
                fromSection.style.display = 'none';
                fromSection.style.transform = '';
                fromSection.style.transition = '';
                toSection.style.transform = '';
                toSection.style.transition = '';
            }, duration);
        } else if (direction === 'fade') {
            // Fade out current section
            fromSection.style.opacity = '0';
            fromSection.style.transition = `opacity ${duration / 2}ms var(--animation-ease-out)`;

            setTimeout(() => {
                fromSection.style.display = 'none';
                toSection.style.display = 'block';
                toSection.style.opacity = '0';
                toSection.style.transition = `opacity ${duration / 2}ms var(--animation-ease-in)`;

                setTimeout(() => {
                    toSection.style.opacity = '1';
                }, 10);
            }, duration / 2);

            // Clean up
            setTimeout(() => {
                fromSection.style.opacity = '';
                fromSection.style.transition = '';
                toSection.style.opacity = '';
                toSection.style.transition = '';
            }, duration);
        }
    }
};

// Export all animation controllers to global scope
window.AnimationTriggers = AnimationTriggers;
window.PulseGlowEffects = PulseGlowEffects;
window.SmoothColorTransitions = SmoothColorTransitions;
window.ThemeSwitchingAnimations = ThemeSwitchingAnimations;
window.MessageAnimations = MessageAnimations;
window.SettingsAnimations = SettingsAnimations;

// Initialize enhanced animations on existing elements
function initializeEnhancedAnimations() {
    // Add hover animations to interactive elements
    document.querySelectorAll('button, .interactive, .clickable').forEach(element => {
        if (!element.dataset.animationInitialized) {
            AnimationTriggers.triggerHoverAnimation(element, 'blue', {
                enableGlow: true,
                enableColorShift: true
            });
            element.dataset.animationInitialized = 'true';
        }
    });

    // Add focus transitions to form elements
    document.querySelectorAll('input, textarea, select').forEach(element => {
        if (!element.dataset.focusAnimationInitialized) {
            SmoothColorTransitions.addFocusTransition(element, 'blue', {
                enableRing: true,
                enableColorShift: true,
                enableGlow: false
            });
            element.dataset.focusAnimationInitialized = 'true';
        }
    });

    // Add pulse effects to status indicators
    document.querySelectorAll('.status-indicator, .badge, .notification').forEach(element => {
        if (!element.dataset.pulseInitialized) {
            PulseGlowEffects.addPulseEffect(element, 'blue', {
                intensity: 'subtle',
                continuous: true
            });
            element.dataset.pulseInitialized = 'true';
        }
    });
}

// Initialize animations when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeEnhancedAnimations);
} else {
    initializeEnhancedAnimations();
}

// Add CSS keyframes for ripple effect
const rippleStyles = document.createElement('style');
rippleStyles.textContent = `
    @keyframes ripple {
        0% {
            transform: scale(0);
            opacity: 1;
        }
        100% {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyles);

// Initialize enhanced code blocks when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        EnhancedCodeBlocks.initializeAllCodeBlocks();
    });
} else {
    EnhancedCodeBlocks.initializeAllCodeBlocks();
}

// Re-initialize code blocks when new content is added (for dynamic content)
const codeBlockObserver = new MutationObserver((mutations) => {
    let shouldReinitialize = false;
    
    mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if new code blocks were added
                    if (node.tagName === 'PRE' || node.querySelector('pre')) {
                        shouldReinitialize = true;
                    }
                }
            });
        }
    });
    
    if (shouldReinitialize) {
        // Debounce the reinitialization to avoid excessive calls
        clearTimeout(codeBlockObserver.reinitTimeout);
        codeBlockObserver.reinitTimeout = setTimeout(() => {
            EnhancedCodeBlocks.initializeAllCodeBlocks();
        }, 100);
    }
});

// Start observing for dynamic content changes
codeBlockObserver.observe(document.body, {
    childList: true,
    subtree: true
});

// ========================================
// CENTRALIZED INITIALIZATION SYSTEM
// ========================================

/**
 * Centralized initialization manager for all UI interactions
 */
class UIInteractionsInitializer {
    constructor() {
        this.initialized = false;
        this.initializationSteps = [];
        this.eventListeners = [];
        this.cleanupFunctions = [];
    }

    /**
     * Add an initialization step
     */
    addInitializationStep(name, stepFunction, dependencies = []) {
        this.initializationSteps.push({ name, stepFunction, dependencies });
    }

    /**
     * Execute all initialization steps in proper order
     */
    async initialize() {
        if (this.initialized) {
            console.warn('UIInteractions already initialized');
            return;
        }

        console.log('🚀 Starting centralized UI Interactions initialization...');

        try {
            // Step 1: Validate dependencies
            await this.runStep('validateDependencies', () => {
                return initializeUIInteractionsDependencies();
            });

            // Step 2: Setup basic features
            await this.runStep('setupTextareaResize', () => {
                if (userInput) {
                    this.safeAddEventListener(userInput, 'input', resizeTextarea);
                    setTimeout(resizeTextarea, 50);
                } else {
                    console.warn('UserInput not available for textarea resize');
                }
            });

            await this.runStep('setupClearButton', () => {
                if (typeof clearInputBtn !== 'undefined' && clearInputBtn && typeof userInput !== 'undefined' && userInput) {
                    setupClearInputButton();
                } else {
                    console.warn('Clear input button elements not available');
                }
            });

            // Step 3: Setup prompt library modal
            await this.runStep('setupPromptLibrary', () => {
                setupPromptLibraryModal();
            });

            // Step 4: Initialize enhanced interactions
            await this.runStep('initializeEnhanced', () => {
                initializeEnhancedInteractions();
            });

            // Step 5: Initialize status indicators
            await this.runStep('initializeStatus', () => {
                setTimeout(() => {
                    initializeStatusIndicators();
                }, 500);
            });

            // Step 6: Initialize animations
            await this.runStep('initializeAnimations', () => {
                initializeEnhancedAnimations();
            });

            this.initialized = true;
            console.log('✅ UI Interactions initialization completed successfully');

        } catch (error) {
            console.error('❌ UI Interactions initialization failed:', error);
        }
    }

    /**
     * Run a single initialization step with error handling
     */
    async runStep(stepName, stepFunction) {
        try {
            console.log(`⚙️ Running step: ${stepName}`);
            const result = await stepFunction();
            console.log(`✅ Step completed: ${stepName}`);
            return result;
        } catch (error) {
            console.error(`❌ Step failed: ${stepName}`, error);
            // Don't throw - allow other steps to continue
        }
    }

    /**
     * Safely add event listener with cleanup tracking
     */
    safeAddEventListener(element, event, handler, options = {}) {
        if (!element) {
            console.warn(`Cannot add ${event} listener to null element`);
            return;
        }

        try {
            element.addEventListener(event, handler, options);
            // Track for cleanup
            this.eventListeners.push({ element, event, handler, options });
        } catch (error) {
            console.error(`Failed to add ${event} listener:`, error);
        }
    }

    /**
     * Clean up all event listeners and resources
     */
    cleanup() {
        console.log('🧹 Cleaning up UI Interactions...');
        
        // Remove all tracked event listeners
        this.eventListeners.forEach(({ element, event, handler }) => {
            try {
                element.removeEventListener(event, handler);
            } catch (error) {
                console.warn('Failed to remove event listener:', error);
            }
        });
        
        // Run cleanup functions
        this.cleanupFunctions.forEach(cleanup => {
            try {
                cleanup();
            } catch (error) {
                console.warn('Cleanup function failed:', error);
            }
        });
        
        this.eventListeners = [];
        this.cleanupFunctions = [];
        this.initialized = false;
    }

    /**
     * Add a cleanup function
     */
    addCleanupFunction(cleanupFn) {
        this.cleanupFunctions.push(cleanupFn);
    }
}

// Create global initializer instance
window.uiInteractionsInitializer = new UIInteractionsInitializer();

// ========================================
// CONSOLIDATED DOM CONTENT LOADED HANDLER
// ========================================

/**
 * Main DOMContentLoaded handler - replaces all individual handlers
 */
function handleDOMContentLoaded() {
    console.log('📄 DOM Content Loaded - Starting UI Interactions setup...');
    
    // Initialize UI Interactions using centralized system
    window.uiInteractionsInitializer.initialize().then(() => {
        console.log('🎉 UI Interactions fully initialized');
    }).catch(error => {
        console.error('Failed to initialize UI Interactions:', error);
    });
}

// Single DOMContentLoaded event listener
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', handleDOMContentLoaded);
} else {
    // DOM already loaded
    handleDOMContentLoaded();
}

// Page unload cleanup
window.addEventListener('beforeunload', () => {
    if (window.uiInteractionsInitializer) {
        window.uiInteractionsInitializer.cleanup();
    }
});

// Export the initializer for external use
window.initializeUIInteractionsDependencies = initializeUIInteractionsDependencies;
window.safeElementAccess = safeElementAccess;
