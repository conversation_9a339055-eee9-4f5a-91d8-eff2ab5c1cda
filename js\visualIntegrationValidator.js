/**
 * Visual Integration Validator
 * Validates that all visual enhancements work together consistently
 */

class VisualIntegrationValidator {
    constructor() {
        this.validationResults = [];
        this.colorPalette = {
            blue: ['#38bdf8', '#0ea5e9', 'rgb(59, 130, 246)'],
            purple: ['#8b5cf6', '#a855f7', '#9333ea'],
            green: ['#10b981', '#059669', '#047857'],
            orange: ['#f59e0b', '#d97706', '#b45309']
        };
    }

    /**
     * Run all validation tests
     */
    async validateAll() {
        console.log('🔍 Starting Visual Integration Validation...');
        
        this.validateColorPalette();
        this.validateMessageTypeColors();
        this.validateInteractiveElements();
        this.validateSettingsVisualOrganization();
        this.validateStatusIndicators();
        this.validateCodeBlockEnhancements();
        this.validateAccessibilityFeatures();
        this.validateAnimationSystem();
        this.validateThemeConsistency();
        
        this.generateReport();
        return this.validationResults;
    }

    /**
     * Validate enhanced color palette system
     */
    validateColorPalette() {
        console.log('🎨 Validating Color Palette System...');
        
        const results = {
            test: 'Color Palette System',
            passed: true,
            issues: []
        };

        // Check CSS custom properties
        const rootStyles = getComputedStyle(document.documentElement);
        
        const requiredColors = [
            '--color-blue-primary',
            '--color-purple-primary', 
            '--color-green-primary',
            '--color-orange-primary',
            '--color-user-message',
            '--color-assistant-message',
            '--color-system-message'
        ];

        requiredColors.forEach(colorVar => {
            const colorValue = rootStyles.getPropertyValue(colorVar).trim();
            if (!colorValue) {
                results.passed = false;
                results.issues.push(`Missing CSS custom property: ${colorVar}`);
            }
        });

        // Check utility classes exist
        const utilityClasses = [
            'text-blue-primary',
            'text-purple-primary',
            'text-green-primary',
            'text-orange-primary',
            'bg-blue-tint',
            'bg-purple-tint',
            'bg-green-tint',
            'bg-orange-tint'
        ];

        utilityClasses.forEach(className => {
            const testElement = document.createElement('div');
            testElement.className = className;
            document.body.appendChild(testElement);
            
            const styles = getComputedStyle(testElement);
            const hasColor = styles.color !== 'rgba(0, 0, 0, 0)' || styles.backgroundColor !== 'rgba(0, 0, 0, 0)';
            
            if (!hasColor) {
                results.passed = false;
                results.issues.push(`Utility class not working: ${className}`);
            }
            
            document.body.removeChild(testElement);
        });

        this.validationResults.push(results);
    }

    /**
     * Validate message type color coding
     */
    validateMessageTypeColors() {
        console.log('💬 Validating Message Type Colors...');
        
        const results = {
            test: 'Message Type Color Coding',
            passed: true,
            issues: []
        };

        // Check message role classes
        const messageTypes = ['user', 'assistant', 'system'];
        
        messageTypes.forEach(type => {
            const testMessage = document.createElement('div');
            testMessage.className = `message-${type}`;
            
            const roleIcon = document.createElement('i');
            roleIcon.className = `fas fa-${type === 'user' ? 'user' : type === 'assistant' ? 'robot' : 'cog'} text-${type}-message`;
            
            testMessage.appendChild(roleIcon);
            document.body.appendChild(testMessage);
            
            const iconStyles = getComputedStyle(roleIcon);
            const hasColor = iconStyles.color !== 'rgba(0, 0, 0, 0)';
            
            if (!hasColor) {
                results.passed = false;
                results.issues.push(`Message type color not applied: ${type}`);
            }
            
            document.body.removeChild(testMessage);
        });

        this.validationResults.push(results);
    }

    /**
     * Validate interactive element feedback
     */
    validateInteractiveElements() {
        console.log('🖱️ Validating Interactive Elements...');
        
        const results = {
            test: 'Interactive Element Feedback',
            passed: true,
            issues: []
        };

        // Test button hover states
        const testButton = document.createElement('button');
        testButton.className = 'px-4 py-2 bg-blue-primary hover:bg-blue-secondary transition-all duration-200 hover:scale-105';
        testButton.textContent = 'Test Button';
        document.body.appendChild(testButton);

        // Simulate hover
        testButton.dispatchEvent(new MouseEvent('mouseenter'));
        
        const buttonStyles = getComputedStyle(testButton);
        const hasTransition = buttonStyles.transition.includes('all');
        
        if (!hasTransition) {
            results.passed = false;
            results.issues.push('Button transitions not working properly');
        }

        document.body.removeChild(testButton);

        // Test focus rings
        const testInput = document.createElement('input');
        testInput.className = 'focus:ring-2 focus:ring-blue-primary';
        document.body.appendChild(testInput);
        
        testInput.focus();
        const inputStyles = getComputedStyle(testInput);
        
        // Note: Focus ring detection is limited in automated tests
        // This is more of a visual verification
        
        document.body.removeChild(testInput);

        this.validationResults.push(results);
    }

    /**
     * Validate settings panel visual organization
     */
    validateSettingsVisualOrganization() {
        console.log('⚙️ Validating Settings Visual Organization...');
        
        const results = {
            test: 'Settings Visual Organization',
            passed: true,
            issues: []
        };

        const categories = ['api', 'ui', 'prompt', 'advanced'];
        
        categories.forEach(category => {
            const testHeader = document.createElement('div');
            testHeader.className = `settings-section-header ${category}`;
            
            const icon = document.createElement('i');
            icon.className = `fas settings-icon ${category}`;
            
            testHeader.appendChild(icon);
            document.body.appendChild(testHeader);
            
            const headerStyles = getComputedStyle(testHeader);
            const iconStyles = getComputedStyle(icon);
            
            const hasBackgroundGradient = headerStyles.background.includes('gradient') || 
                                        headerStyles.backgroundImage.includes('gradient');
            const hasColor = iconStyles.color !== 'rgba(0, 0, 0, 0)';
            
            if (!hasBackgroundGradient && !hasColor) {
                results.passed = false;
                results.issues.push(`Settings category styling missing: ${category}`);
            }
            
            document.body.removeChild(testHeader);
        });

        this.validationResults.push(results);
    }

    /**
     * Validate status indicator system
     */
    validateStatusIndicators() {
        console.log('📊 Validating Status Indicators...');
        
        const results = {
            test: 'Status Indicator System',
            passed: true,
            issues: []
        };

        const statusTypes = ['connected', 'error', 'warning', 'loading'];
        
        statusTypes.forEach(status => {
            // Test status indicator
            const indicator = document.createElement('div');
            indicator.className = `status-indicator status-${status}`;
            
            const icon = document.createElement('i');
            icon.className = 'fas fa-circle';
            
            indicator.appendChild(icon);
            document.body.appendChild(indicator);
            
            const indicatorStyles = getComputedStyle(indicator);
            const hasColor = indicatorStyles.color !== 'rgba(0, 0, 0, 0)' || 
                           indicatorStyles.backgroundColor !== 'rgba(0, 0, 0, 0)';
            
            if (!hasColor) {
                results.passed = false;
                results.issues.push(`Status indicator color missing: ${status}`);
            }
            
            document.body.removeChild(indicator);
            
            // Test status badge
            const badge = document.createElement('div');
            badge.className = `status-badge status-${status}`;
            document.body.appendChild(badge);
            
            const badgeStyles = getComputedStyle(badge);
            const badgeHasColor = badgeStyles.backgroundColor !== 'rgba(0, 0, 0, 0)';
            
            if (!badgeHasColor) {
                results.passed = false;
                results.issues.push(`Status badge color missing: ${status}`);
            }
            
            document.body.removeChild(badge);
        });

        this.validationResults.push(results);
    }

    /**
     * Validate code block enhancements
     */
    validateCodeBlockEnhancements() {
        console.log('💻 Validating Code Block Enhancements...');
        
        const results = {
            test: 'Code Block Visual System',
            passed: true,
            issues: []
        };

        // Test code language badge
        const languageBadge = document.createElement('div');
        languageBadge.className = 'code-language-badge';
        languageBadge.textContent = 'JavaScript';
        document.body.appendChild(languageBadge);
        
        const badgeStyles = getComputedStyle(languageBadge);
        const hasBadgeStyles = badgeStyles.position === 'absolute' || 
                             badgeStyles.backgroundColor !== 'rgba(0, 0, 0, 0)';
        
        if (!hasBadgeStyles) {
            results.passed = false;
            results.issues.push('Code language badge styling missing');
        }
        
        document.body.removeChild(languageBadge);

        // Test enhanced code block
        const codeBlock = document.createElement('pre');
        codeBlock.className = 'code-block-enhanced';
        
        const code = document.createElement('code');
        code.className = 'language-javascript';
        code.textContent = 'console.log("test");';
        
        codeBlock.appendChild(code);
        document.body.appendChild(codeBlock);
        
        const codeBlockStyles = getComputedStyle(codeBlock);
        const hasEnhancedStyles = codeBlockStyles.borderRadius !== '0px' || 
                                codeBlockStyles.padding !== '0px';
        
        if (!hasEnhancedStyles) {
            results.passed = false;
            results.issues.push('Enhanced code block styling missing');
        }
        
        document.body.removeChild(codeBlock);

        this.validationResults.push(results);
    }

    /**
     * Validate accessibility features
     */
    validateAccessibilityFeatures() {
        console.log('♿ Validating Accessibility Features...');
        
        const results = {
            test: 'Accessibility Features',
            passed: true,
            issues: []
        };

        // Test high contrast alternatives
        const patternIndicator = document.createElement('div');
        patternIndicator.className = 'pattern-indicator status-connected';
        document.body.appendChild(patternIndicator);
        
        const patternStyles = getComputedStyle(patternIndicator);
        const hasPattern = patternStyles.background.includes('repeating-linear-gradient') ||
                         patternStyles.backgroundImage.includes('repeating-linear-gradient');
        
        if (!hasPattern) {
            results.passed = false;
            results.issues.push('Pattern indicators for accessibility missing');
        }
        
        document.body.removeChild(patternIndicator);

        // Test text-based indicators
        const textIndicator = document.createElement('span');
        textIndicator.className = 'status-text-indicator status-connected';
        textIndicator.textContent = 'Connected';
        document.body.appendChild(textIndicator);
        
        const textStyles = getComputedStyle(textIndicator);
        const hasTextStyling = textStyles.textTransform === 'uppercase' ||
                             textStyles.fontWeight !== '400';
        
        if (!hasTextStyling) {
            results.passed = false;
            results.issues.push('Text-based status indicators missing styling');
        }
        
        document.body.removeChild(textIndicator);

        // Test focus indicators
        const focusElement = document.createElement('button');
        focusElement.className = 'focus-enhanced';
        focusElement.textContent = 'Test Focus';
        document.body.appendChild(focusElement);
        
        focusElement.focus();
        // Focus ring validation would need more complex testing
        
        document.body.removeChild(focusElement);

        this.validationResults.push(results);
    }

    /**
     * Validate animation system
     */
    validateAnimationSystem() {
        console.log('🎬 Validating Animation System...');
        
        const results = {
            test: 'Animation System',
            passed: true,
            issues: []
        };

        // Test transition classes
        const transitionElement = document.createElement('div');
        transitionElement.className = 'transition-all duration-200';
        document.body.appendChild(transitionElement);
        
        const transitionStyles = getComputedStyle(transitionElement);
        const hasTransition = transitionStyles.transition.includes('all') &&
                            transitionStyles.transitionDuration.includes('0.2s');
        
        if (!hasTransition) {
            results.passed = false;
            results.issues.push('Transition animations not working');
        }
        
        document.body.removeChild(transitionElement);

        // Test hover scale effects
        const scaleElement = document.createElement('div');
        scaleElement.className = 'hover:scale-105 transition-transform';
        document.body.appendChild(scaleElement);
        
        // Simulate hover
        scaleElement.dispatchEvent(new MouseEvent('mouseenter'));
        
        const scaleStyles = getComputedStyle(scaleElement);
        const hasTransform = scaleStyles.transition.includes('transform');
        
        if (!hasTransform) {
            results.passed = false;
            results.issues.push('Scale animations not working');
        }
        
        document.body.removeChild(scaleElement);

        this.validationResults.push(results);
    }

    /**
     * Validate theme consistency
     */
    validateThemeConsistency() {
        console.log('🌓 Validating Theme Consistency...');
        
        const results = {
            test: 'Theme Consistency',
            passed: true,
            issues: []
        };

        const originalTheme = document.documentElement.classList.contains('dark');
        
        // Test dark theme
        document.documentElement.classList.add('dark');
        
        const darkTestElement = document.createElement('div');
        darkTestElement.className = 'bg-white dark:bg-black text-gray-900 dark:text-white';
        document.body.appendChild(darkTestElement);
        
        const darkStyles = getComputedStyle(darkTestElement);
        const hasDarkBackground = darkStyles.backgroundColor !== 'rgb(255, 255, 255)';
        const hasDarkText = darkStyles.color !== 'rgb(17, 24, 39)';
        
        if (!hasDarkBackground || !hasDarkText) {
            results.passed = false;
            results.issues.push('Dark theme colors not applying correctly');
        }
        
        document.body.removeChild(darkTestElement);
        
        // Test light theme
        document.documentElement.classList.remove('dark');
        
        const lightTestElement = document.createElement('div');
        lightTestElement.className = 'bg-white dark:bg-black text-gray-900 dark:text-white';
        document.body.appendChild(lightTestElement);
        
        const lightStyles = getComputedStyle(lightTestElement);
        const hasLightBackground = lightStyles.backgroundColor === 'rgb(255, 255, 255)';
        const hasLightText = lightStyles.color === 'rgb(17, 24, 39)';
        
        if (!hasLightBackground || !hasLightText) {
            results.passed = false;
            results.issues.push('Light theme colors not applying correctly');
        }
        
        document.body.removeChild(lightTestElement);
        
        // Restore original theme
        if (originalTheme) {
            document.documentElement.classList.add('dark');
        }

        this.validationResults.push(results);
    }

    /**
     * Generate validation report
     */
    generateReport() {
        console.log('\n📋 Visual Integration Validation Report');
        console.log('=====================================');
        
        let totalTests = this.validationResults.length;
        let passedTests = this.validationResults.filter(result => result.passed).length;
        let failedTests = totalTests - passedTests;
        
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests} ✅`);
        console.log(`Failed: ${failedTests} ❌`);
        console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
        
        console.log('\nDetailed Results:');
        console.log('-----------------');
        
        this.validationResults.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.test}`);
            
            if (!result.passed && result.issues.length > 0) {
                result.issues.forEach(issue => {
                    console.log(`   ⚠️  ${issue}`);
                });
            }
        });
        
        if (failedTests === 0) {
            console.log('\n🎉 All visual enhancements are properly integrated!');
        } else {
            console.log(`\n⚠️  ${failedTests} integration issues found. Please review the details above.`);
        }
        
        return {
            totalTests,
            passedTests,
            failedTests,
            successRate: Math.round((passedTests / totalTests) * 100),
            results: this.validationResults
        };
    }

    /**
     * Test color coordination between related elements
     */
    testColorCoordination() {
        console.log('🎨 Testing Color Coordination...');
        
        // Test message type coordination
        const userMessage = document.createElement('div');
        userMessage.className = 'message-user border-l-4 border-user-message';
        
        const userIcon = document.createElement('i');
        userIcon.className = 'text-user-message';
        
        userMessage.appendChild(userIcon);
        document.body.appendChild(userMessage);
        
        const messageStyles = getComputedStyle(userMessage);
        const iconStyles = getComputedStyle(userIcon);
        
        // Both should use the same color variable
        const borderColor = messageStyles.borderLeftColor;
        const iconColor = iconStyles.color;
        
        console.log(`User message border: ${borderColor}, icon: ${iconColor}`);
        
        document.body.removeChild(userMessage);
    }

    /**
     * Test state synchronization
     */
    testStateSynchronization() {
        console.log('🔄 Testing State Synchronization...');
        
        // Test connection status synchronization
        const headerStatus = document.createElement('div');
        headerStatus.className = 'status-indicator status-connected';
        headerStatus.id = 'headerConnectionStatus';
        
        const settingsStatus = document.createElement('div');
        settingsStatus.className = 'settings-status-indicator status-connected';
        settingsStatus.id = 'connectionStatusIndicator';
        
        document.body.appendChild(headerStatus);
        document.body.appendChild(settingsStatus);
        
        // Both should have consistent styling for the same status
        const headerStyles = getComputedStyle(headerStatus);
        const settingsStyles = getComputedStyle(settingsStatus);
        
        console.log(`Header status color: ${headerStyles.color}`);
        console.log(`Settings status color: ${settingsStyles.color}`);
        
        document.body.removeChild(headerStatus);
        document.body.removeChild(settingsStatus);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VisualIntegrationValidator;
} else {
    window.VisualIntegrationValidator = VisualIntegrationValidator;
}

// Auto-run validation if in test environment
if (typeof window !== 'undefined' && window.location.pathname.includes('test-visual-integration')) {
    document.addEventListener('DOMContentLoaded', async () => {
        const validator = new VisualIntegrationValidator();
        await validator.validateAll();
        validator.testColorCoordination();
        validator.testStateSynchronization();
    });
}