{"name": "nebula-ai-interface", "version": "1.0.0", "description": "A modern, dark-themed web interface for interacting with Large Language Models (LLMs).", "main": "index.html", "scripts": {"build:css": "postcss css/main.css -o css/main.compiled.css --map", "build:css:prod": "cross-env NODE_ENV=production postcss css/main.css -o css/main.compiled.css --no-map", "watch:css": "postcss css/main.css -o css/main.compiled.css --watch --map", "start": "npm run build:css && live-server --port=8080", "start:prod": "npm run build:css:prod && live-server --port=8080", "dev": "concurrently \"npm run watch:css\" \"live-server --port=8080\"", "build": "npm run css:quality && npm run build:css:prod && npm run extract:critical", "extract:critical": "node extract-critical-css.js", "test": "echo \"Error: no test specified\" && exit 1", "lint:css": "stylelint \"css/**/*.css\" --ignore-path .gitignore", "lint:css:fix": "stylelint \"css/**/*.css\" --fix --ignore-path .gitignore", "format:css": "prettier --write \"css/**/*.css\"", "format:check": "prettier --check \"css/**/*.css\"", "css:quality": "npm run format:css && npm run lint:css:fix", "css:validate": "npm run format:check && npm run lint:css", "validate:tokens": "node validate-design-tokens.js", "css:full-check": "npm run css:quality && npm run validate:tokens", "analyze:css": "npm run build:css:prod && node css-usage-analyzer.js"}, "keywords": ["llm", "ai", "interface", "chatbot", "dark-theme", "web-app", "javascript", "tailwind"], "author": "Your Name <<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/yourusername/nebula-ai-interface.git"}, "homepage": "https://github.com/yourusername/nebula-ai-interface#readme", "bugs": {"url": "https://github.com/yourusername/nebula-ai-interface/issues"}, "devDependencies": {"autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "cssnano": "^7.1.0", "live-server": "^1.2.2", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "postcss-custom-properties": "^14.0.6", "postcss-import": "^16.1.1", "postcss-preset-env": "^10.2.4", "prettier": "^3.6.2", "stylelint": "^16.22.0", "stylelint-config-standard": "^36.0.1"}}