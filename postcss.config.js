module.exports = (ctx) => ({
  plugins: [
    // Import CSS files
    require('postcss-import')({
      path: ['css']
    }),
    
    // Process modern CSS features
    require('postcss-preset-env')({
      stage: 1,
      features: {
        'custom-properties': false // Keep custom properties for runtime
      }
    }),
    
    // Add vendor prefixes
    require('autoprefixer'),
    
    // Optimize and minify for production
    ctx.env === 'production' && require('cssnano')({
      preset: ['default', {
        discardComments: {
          removeAll: true
        },
        normalizeWhitespace: true,
        mergeLonghand: true,
        mergeRules: true,
        minifySelectors: true,
        minifyParams: true,
        minifyFontValues: true,
        colormin: true,
        calc: true,
        convertValues: true,
        discardDuplicates: true,
        discardEmpty: true,
        discardOverridden: true,
        discardUnused: false, // Keep this false to avoid removing used classes
        normalizeCharset: true,
        normalizeDisplayValues: true,
        normalizePositions: true,
        normalizeRepeatStyle: true,
        normalizeString: true,
        normalizeTimingFunctions: true,
        normalizeUnicode: true,
        normalizeUrl: true,
        orderedValues: true,
        reduceIdents: false, // Keep this false to preserve keyframe names
        reduceInitial: true,
        reduceTransforms: true,
        svgo: true,
        uniqueSelectors: true
      }]
    })
  ].filter(Boolean)
});