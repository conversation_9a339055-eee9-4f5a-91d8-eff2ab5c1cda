<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Expand Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>System Prompt Editor Test</h1>
        <p>This page tests the new expand functionality for the system prompt editor.</p>
        
        <div id="test-results">
            <div class="test-result info">
                <strong>Test Status:</strong> Ready to test
            </div>
        </div>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Open the main application at <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
            <li>Click the Settings button (gear icon) to open the settings panel</li>
            <li>Find the "System Prompt Configuration" section</li>
            <li>Click the "Expand" button next to the system prompt textarea</li>
            <li>Verify that:
                <ul>
                    <li>The settings panel expands to a larger width</li>
                    <li>The textarea becomes taller (min-height: 200px)</li>
                    <li>The button text changes to "Collapse"</li>
                    <li>The button icon changes to compress icon</li>
                    <li>No second modal window opens</li>
                </ul>
            </li>
            <li>Click "Collapse" to return to normal size</li>
        </ol>
        
        <h2>Expected Behavior:</h2>
        <ul>
            <li>✅ Single settings panel that expands in place</li>
            <li>✅ Smooth transition animation</li>
            <li>✅ Toggle between expand/collapse states</li>
            <li>✅ Enhanced textarea for better editing</li>
            <li>❌ No separate modal window should appear</li>
        </ul>
    </div>
</body>
</html>
